{"abi": [{"type": "constructor", "inputs": [{"name": "name_", "type": "string", "internalType": "string"}, {"name": "symbol_", "type": "string", "internalType": "string"}, {"name": "initialSupply_", "type": "uint256", "internalType": "uint256"}, {"name": "mintRecipient_", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "DOMAIN_SEPARATOR", "inputs": [], "outputs": [{"name": "result", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "result", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "result", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "nonces", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "result", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "permit", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "result", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "AllowanceOverflow", "inputs": []}, {"type": "error", "name": "AllowanceUnderflow", "inputs": []}, {"type": "error", "name": "InsufficientAllowance", "inputs": []}, {"type": "error", "name": "InsufficientBalance", "inputs": []}, {"type": "error", "name": "InvalidPermit", "inputs": []}, {"type": "error", "name": "PermitExpired", "inputs": []}, {"type": "error", "name": "TotalSupplyOverflow", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "110:571:16:-:0;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;-1:-1:-1;;;;;110:571:16;;;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;110:571:16;;;;;;;;;:::i;:::-;;;;;;;;;;;;;-1:-1:-1;;;;;110:571:16;;;;;;;;-1:-1:-1;;;;;110:571:16;;;;-1:-1:-1;110:571:16;;;;;;;;;;;-1:-1:-1;110:571:16;;;;;;;;;;;-1:-1:-1;110:571:16;;;;;;;;;;;;;;;;-1:-1:-1;110:571:16;;;;;;;;;;;;;;;;;;;-1:-1:-1;110:571:16;;;;-1:-1:-1;;;;;110:571:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;110:571:16;;;;;;;;;;;;;;;;;;;;;;17884:946:59;;;;;;;;;;;;;;;-1:-1:-1;17884:946:59;110:571:16;17884:946:59;;;;;;;;110:571:16;17884:946:59;;;110:571:16;17884:946:59;-1:-1:-1;17884:946:59;110:571:16;17884:946:59;;110:571:16;;;;;;;;;17884:946:59;;-1:-1:-1;17884:946:59;;;;110:571:16;;;;-1:-1:-1;110:571:16;;;;;;;;;;;-1:-1:-1;110:571:16;;-1:-1:-1;110:571:16;;-1:-1:-1;110:571:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;110:571:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;110:571:16;;;;;;;;;-1:-1:-1;110:571:16;;;;;;;;-1:-1:-1;110:571:16;;;;;-1:-1:-1;110:571:16;;;;;;;;;;;;-1:-1:-1;110:571:16;;;;;-1:-1:-1;110:571:16;;;;;-1:-1:-1;110:571:16;;;;;;;;;;-1:-1:-1;110:571:16;;;-1:-1:-1;110:571:16;;-1:-1:-1;110:571:16;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;110:571:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;110:571:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;110:571:16;;;;;;;;;-1:-1:-1;110:571:16;;;;;;;;;;;;-1:-1:-1;110:571:16;;;;;;;;;-1:-1:-1;;110:571:16;;;-1:-1:-1;;;;;110:571:16;;;;;;;;;;:::o;:::-;;;;;;;;;;;;-1:-1:-1;;;;;110:571:16;;;;;;;;-1:-1:-1;;110:571:16;;;;:::i;:::-;;;;;;;;;;;;;;-1:-1:-1;110:571:16;;;;;;;;;;;;;;:::o", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "110:571:16:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;110:571:16;;;;;;:::i;:::-;;;:::i;:::-;;7114:184:59;;;;110:571:16;7114:184:59;110:571:16;7114:184:59;;;;110:571:16;;;;;;;;;;;;;;;;-1:-1:-1;;110:571:16;;;;;;:::i;:::-;;;:::i;:::-;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;13704:24:59;13781:2752;;;;;;110:571:16;13781:2752:59;10164:1922;;;;;;13781:2752;10164:1922;;;;;;13781:2752;;;;;;110:571:16;13781:2752:59;110:571:16;;13781:2752:59;;;;;;;;;110:571:16;13781:2752:59;;;;;;110:571:16;13781:2752:59;;;;;;;110:571:16;13781:2752:59;;;;;;;110:571:16;13781:2752:59;;;;;;110:571:16;13781:2752:59;;;;;;;;;;;;;110:571:16;13781:2752:59;;;;;;;;;110:571:16;13781:2752:59;110:571:16;13781:2752:59;110:571:16;13781:2752:59;110:571:16;;;13781:2752:59;110:571:16;;;13781:2752:59;110:571:16;13781:2752:59;110:571:16;;;13781:2752:59;;;;;;;;;;;110:571:16;13781:2752:59;;;;;;;;;110:571:16;13781:2752:59;;;;;;110:571:16;13781:2752:59;;110:571:16;13781:2752:59;110:571:16;13781:2752:59;;;;110:571:16;13781:2752:59;110:571:16;13781:2752:59;;110:571:16;;;;;;-1:-1:-1;;110:571:16;;;;;;:::i;:::-;;;8378:1143:59;;;;;110:571:16;8378:1143:59;110:571:16;8378:1143:59;;;;;;;;;;;;;;110:571:16;8378:1143:59;110:571:16;8378:1143:59;;;;;;;;110:571:16;8378:1143:59;;;;;;;110:571:16;8378:1143:59;;110:571:16;;;;;;;8378:1143:59;;110:571:16;8378:1143:59;110:571:16;8378:1143:59;;110:571:16;;;;;;-1:-1:-1;;110:571:16;;;;;;;665:7;110:571;;665:7;110:571;;665:7;110:571;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;110:571:16;;;;;-1:-1:-1;;110:571:16;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;665:7;110:571;;;;;;-1:-1:-1;110:571:16;;;;;;;;;;;;;;;;;;665:7;110:571;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;110:571:16;;;;;;:::i;:::-;12929:205:59;;;110:571:16;12929:205:59;110:571:16;12929:205:59;;;;110:571:16;;;;;;;;;;;;-1:-1:-1;;110:571:16;;;;;;:::i;:::-;6680:148:59;;;110:571:16;6680:148:59;110:571:16;6680:148:59;;;;110:571:16;;;;;;;;;;;;-1:-1:-1;;110:571:16;;;;;16961:346:59;110:571:16;;:::i;:::-;;;;;;16884:24:59;110:571:16;16961:346:59;;;;;;;;;;110:571:16;16961:346:59;;;;;;;;;;;;;;110:571:16;;;;;;;;;;;;-1:-1:-1;;110:571:16;;;;;;;5933:2:59;110:571:16;;;;;;;;;-1:-1:-1;;110:571:16;;;;;;:::i;:::-;;;:::i;:::-;;;10164:1922:59;;110:571:16;10164:1922:59;;110:571:16;10164:1922:59;;;;;;;;;;;;;110:571:16;10164:1922:59;;;;110:571:16;10164:1922:59;;;;;;;110:571:16;10164:1922:59;;;;;;;;;;;;;;110:571:16;10164:1922:59;110:571:16;10164:1922:59;;;;;;;;110:571:16;10164:1922:59;;;110:571:16;10164:1922:59;;;;;;;;;110:571:16;10164:1922:59;;110:571:16;;;;;;;10164:1922:59;;;;;;;;;;;;;;;;;110:571:16;10164:1922:59;110:571:16;10164:1922:59;;110:571:16;;;;;;-1:-1:-1;;110:571:16;;;;;6408:68:59;;110:571:16;;;;;;;;;;;;-1:-1:-1;;110:571:16;;;;;;:::i;:::-;;;7568:413:59;110:571:16;7568:413:59;;;;;110:571:16;7568:413:59;;;;;;110:571:16;7568:413:59;;;;;;;110:571:16;;7568:413:59;110:571:16;;;;;;;;;;;;;-1:-1:-1;;110:571:16;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;110:571:16;;;;:::o;:::-;;;;-1:-1:-1;;;;;110:571:16;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;110:571:16;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;110:571:16;;;;;-1:-1:-1;;110:571:16;;;;;;;;;;;;;;:::o;:::-;;;;;;;-1:-1:-1;110:571:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {"DOMAIN_SEPARATOR()": "3644e515", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "decimals()": "313ce567", "name()": "06fdde03", "nonces(address)": "7ecebe00", "permit(address,address,uint256,uint256,uint8,bytes32,bytes32)": "d505accf", "symbol()": "95d89b41", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol_\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"initialSupply_\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"mintRecipient_\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AllowanceOverflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AllowanceUnderflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidPermit\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PermitExpired\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TotalSupplyOverflow\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"DOMAIN_SEPARATOR\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"result\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"result\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"result\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"nonces\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"result\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"permit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"result\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"AllowanceOverflow()\":[{\"details\":\"The allowance has overflowed.\"}],\"AllowanceUnderflow()\":[{\"details\":\"The allowance has underflowed.\"}],\"InsufficientAllowance()\":[{\"details\":\"Insufficient allowance.\"}],\"InsufficientBalance()\":[{\"details\":\"Insufficient balance.\"}],\"InvalidPermit()\":[{\"details\":\"The permit is invalid.\"}],\"PermitExpired()\":[{\"details\":\"The permit has expired.\"}],\"TotalSupplyOverflow()\":[{\"details\":\"The total supply has overflowed.\"}]},\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when `amount` tokens is approved by `owner` to be used by `spender`.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `amount` tokens is transferred from `from` to `to`.\"}},\"kind\":\"dev\",\"methods\":{\"DOMAIN_SEPARATOR()\":{\"details\":\"Returns the EIP-712 domain separator for the EIP-2612 permit.\"},\"allowance(address,address)\":{\"details\":\"Returns the amount of tokens that `spender` can spend on behalf of `owner`.\"},\"approve(address,uint256)\":{\"details\":\"Sets `amount` as the allowance of `spender` over the caller's tokens. Emits a {Approval} event.\"},\"balanceOf(address)\":{\"details\":\"Returns the amount of tokens owned by `owner`.\"},\"decimals()\":{\"details\":\"Returns the decimals places of the token.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"nonces(address)\":{\"details\":\"Returns the current nonce for `owner`. This value is used to compute the signature for EIP-2612 permit.\"},\"permit(address,address,uint256,uint256,uint8,bytes32,bytes32)\":{\"details\":\"Sets `value` as the allowance of `spender` over the tokens of `owner`, authorized by a signed approval by `owner`. Emits a {Approval} event.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token.\"},\"totalSupply()\":{\"details\":\"Returns the amount of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"Transfer `amount` tokens from the caller to `to`. Requirements: - `from` must at least have `amount`. Emits a {Transfer} event.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"Transfers `amount` tokens from `from` to `to`. Note: Does not update the allowance if it is the maximum uint256 value. Requirements: - `from` must at least have `amount`. - The caller must have at least `amount` of allowance to transfer the tokens of `from`. Emits a {Transfer} event.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/periphery/ERC20.sol\":\"KuruERC20\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/periphery/ERC20.sol\":{\"keccak256\":\"0x8aa728c92ff36c2a5b281d212fff85ee7d1ced42c6fbdcebcfc1f4dae55abbbe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://65555a3627600408de33493cf5acb82b0621f81a0b70d730a46391e96c5ca965\",\"dweb:/ipfs/QmUUu9vjRRVx6htK4PyaPw1gXPrPccCctEu3rg9vpXcuB5\"]},\"node_modules/solady/src/tokens/ERC20.sol\":{\"keccak256\":\"0xb4a3f9ba8a05107f7370de42cff57f3ad26dafd438712c11531a5892de2f59e0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f0a9ca06e3cf6dea1f9a4c5599581573b7d81cd64dc3afb582f325ccf5fdd6dc\",\"dweb:/ipfs/Qmb9r5dDceNF4W8S5u6i85RsNTgE5XG9HbTXkyS25ad3C6\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "string", "name": "symbol_", "type": "string"}, {"internalType": "uint256", "name": "initialSupply_", "type": "uint256"}, {"internalType": "address", "name": "mintRecipient_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "AllowanceOverflow"}, {"inputs": [], "type": "error", "name": "AllowanceUnderflow"}, {"inputs": [], "type": "error", "name": "InsufficientAllowance"}, {"inputs": [], "type": "error", "name": "InsufficientBalance"}, {"inputs": [], "type": "error", "name": "InvalidPermit"}, {"inputs": [], "type": "error", "name": "PermitExpired"}, {"inputs": [], "type": "error", "name": "TotalSupplyOverflow"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "result", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "nonces", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "permit"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"DOMAIN_SEPARATOR()": {"details": "Returns the EIP-712 domain separator for the EIP-2612 permit."}, "allowance(address,address)": {"details": "Returns the amount of tokens that `spender` can spend on behalf of `owner`."}, "approve(address,uint256)": {"details": "Sets `amount` as the allowance of `spender` over the caller's tokens. Emits a {Approval} event."}, "balanceOf(address)": {"details": "Returns the amount of tokens owned by `owner`."}, "decimals()": {"details": "Returns the decimals places of the token."}, "name()": {"details": "Returns the name of the token."}, "nonces(address)": {"details": "Returns the current nonce for `owner`. This value is used to compute the signature for EIP-2612 permit."}, "permit(address,address,uint256,uint256,uint8,bytes32,bytes32)": {"details": "Sets `value` as the allowance of `spender` over the tokens of `owner`, authorized by a signed approval by `owner`. Emits a {Approval} event."}, "symbol()": {"details": "Returns the symbol of the token."}, "totalSupply()": {"details": "Returns the amount of tokens in existence."}, "transfer(address,uint256)": {"details": "Transfer `amount` tokens from the caller to `to`. Requirements: - `from` must at least have `amount`. Emits a {Transfer} event."}, "transferFrom(address,address,uint256)": {"details": "Transfers `amount` tokens from `from` to `to`. Note: Does not update the allowance if it is the maximum uint256 value. Requirements: - `from` must at least have `amount`. - The caller must have at least `amount` of allowance to transfer the tokens of `from`. Emits a {Transfer} event."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/periphery/ERC20.sol": "KuruERC20"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/periphery/ERC20.sol": {"keccak256": "0x8aa728c92ff36c2a5b281d212fff85ee7d1ced42c6fbdcebcfc1f4dae55abbbe", "urls": ["bzz-raw://65555a3627600408de33493cf5acb82b0621f81a0b70d730a46391e96c5ca965", "dweb:/ipfs/QmUUu9vjRRVx6htK4PyaPw1gXPrPccCctEu3rg9vpXcuB5"], "license": "MIT"}, "node_modules/solady/src/tokens/ERC20.sol": {"keccak256": "0xb4a3f9ba8a05107f7370de42cff57f3ad26dafd438712c11531a5892de2f59e0", "urls": ["bzz-raw://f0a9ca06e3cf6dea1f9a4c5599581573b7d81cd64dc3afb582f325ccf5fdd6dc", "dweb:/ipfs/Qmb9r5dDceNF4W8S5u6i85RsNTgE5XG9HbTXkyS25ad3C6"], "license": "MIT"}}, "version": 1}, "id": 16}