{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "DOMAIN_SEPARATOR", "inputs": [], "outputs": [{"name": "result", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SPREAD_CONSTANT", "inputs": [], "outputs": [{"name": "", "type": "uint96", "internalType": "uint96"}], "stateMutability": "view"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "result", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "result", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "deposit", "inputs": [{"name": "baseDeposit", "type": "uint256", "internalType": "uint256"}, {"name": "quoteDeposit", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_owner", "type": "address", "internalType": "address"}, {"name": "token1_", "type": "address", "internalType": "address"}, {"name": "token2_", "type": "address", "internalType": "address"}, {"name": "_marginAccount", "type": "address", "internalType": "address"}, {"name": "_market", "type": "address", "internalType": "address"}, {"name": "_spreadConstant", "type": "uint96", "internalType": "uint96"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "marginAccount", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IMarginAccount"}], "stateMutability": "view"}, {"type": "function", "name": "market", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IOrderBook"}], "stateMutability": "view"}, {"type": "function", "name": "marketParams", "inputs": [], "outputs": [{"name": "pricePrecision", "type": "uint32", "internalType": "uint32"}, {"name": "sizePrecision", "type": "uint96", "internalType": "uint96"}, {"name": "baseAssetAddress", "type": "address", "internalType": "address"}, {"name": "baseAssetDecimals", "type": "uint256", "internalType": "uint256"}, {"name": "quote<PERSON>set<PERSON>dd<PERSON>", "type": "address", "internalType": "address"}, {"name": "quoteAssetDecimals", "type": "uint256", "internalType": "uint256"}, {"name": "tickSize", "type": "uint32", "internalType": "uint32"}, {"name": "minSize", "type": "uint96", "internalType": "uint96"}, {"name": "maxSize", "type": "uint96", "internalType": "uint96"}, {"name": "takerFeeBps", "type": "uint256", "internalType": "uint256"}, {"name": "makerFeeBps", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "mint", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "nonces", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "result", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "permit", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "previewDeposit", "inputs": [{"name": "asset1", "type": "uint256", "internalType": "uint256"}, {"name": "asset2", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "previewMint", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "previewWithdraw", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "setMarketParams", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "token1", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "token2", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "totalAssets", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "result", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "_newOwner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "_shares", "type": "uint256", "internalType": "uint256"}, {"name": "_receiver", "type": "address", "internalType": "address"}, {"name": "_owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "KuruVaultDeposit", "inputs": [{"name": "amount1", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount2", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "userAddress", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "KuruVaultWithdraw", "inputs": [{"name": "amount1", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount2", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "userAddress", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AllowanceOverflow", "inputs": []}, {"type": "error", "name": "AllowanceUnderflow", "inputs": []}, {"type": "error", "name": "InsufficientAllowance", "inputs": []}, {"type": "error", "name": "InsufficientBalance", "inputs": []}, {"type": "error", "name": "InsufficientBalance", "inputs": []}, {"type": "error", "name": "InsufficientLiquidityMinted", "inputs": []}, {"type": "error", "name": "InsufficientQuoteToken", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidPermit", "inputs": []}, {"type": "error", "name": "NativeAssetMismatch", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "PermitExpired", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "TotalSupplyOverflow", "inputs": []}, {"type": "error", "name": "Uint96Overflow", "inputs": []}, {"type": "error", "name": "Unauthorized", "inputs": []}, {"type": "error", "name": "UnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UpgradeFailed", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "825:18296:1:-:0;;;;;;;1525:4:64;1501:31;;-1:-1:-1;;6669:609:62;;;;;;2001:66;6669:609;-1:-1:-1;;;;;;6669:609:62;;;-1:-1:-1;825:18296:1;;;;;;1501:31:64;825:18296:1;;;;;;;;;;;6669:609:62;-1:-1:-1;;;;;;;6669:609:62;-1:-1:-1;;;;;6669:609:62;;;;;;;;;;;;-1:-1:-1;6669:609:62;;;;825:18296:1;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "825:18296:1:-:0;;;;;;;;;-1:-1:-1;825:18296:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1367:35;825:18296;;;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;;:::i;:::-;3003:112;;:::i;:::-;825:18296;;-1:-1:-1;;;;;;825:18296:1;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;;:::i;:::-;;;;:::i;:::-;;7114:184:59;;;;;825:18296:1;7114:184:59;;;;825:18296:1;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;;:::i;:::-;;;:::i;:::-;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;13704:24:59;13781:2752;;;;;;825:18296:1;13781:2752:59;10164:1922;;;;;;13781:2752;10164:1922;;;;;;13781:2752;;;;;;;;825:18296:1;;13781:2752:59;;;;;;;;;825:18296:1;13781:2752:59;;;;;;825:18296:1;13781:2752:59;;;;;;;825:18296:1;13781:2752:59;;;;;;;825:18296:1;13781:2752:59;;;;;;825:18296:1;13781:2752:59;;;;;;;;;;;;;825:18296:1;13781:2752:59;;;;;;;;;;;825:18296:1;13781:2752:59;825:18296:1;13781:2752:59;825:18296:1;;;13781:2752:59;825:18296:1;;;13781:2752:59;825:18296:1;13781:2752:59;825:18296:1;13781:2752:59;825:18296:1;13781:2752:59;;;;;;;;;;;;-1:-1:-1;;;13781:2752:59;;825:18296:1;13781:2752:59;;;;;;;825:18296:1;;13781:2752:59;825:18296:1;;13781:2752:59;;;;825:18296:1;13781:2752:59;;;;;;825:18296:1;13781:2752:59;;825:18296:1;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;;:::i;:::-;;;:::i;:::-;;;:::i;:::-;;;;;;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;-1:-1:-1;;;;;825:18296:1;;;;;;;2001:66:62;;3207:622;;;;;;;;;825:18296:1;-1:-1:-1;825:18296:1;;-1:-1:-1;;;;;;825:18296:1;;;-1:-1:-1;;;;;825:18296:1;;;;;;10164:1922:59;825:18296:1;;;;;;;;;;;;;;1933:21;;;;;825:18296;1933:54;;825:18296;;-1:-1:-1;;;1957:25:1;;825:18296;;;;1957:25;;;;;;;825:18296;1957:25;;;;;1933:54;;;825:18296;1916:71;825:18296;3207:622:62;825:18296:1;;-1:-1:-1;;;;;;825:18296:1;-1:-1:-1;;;;;825:18296:1;;;;;;;;;2040:21;;;;;;;:54;;;;825:18296;;-1:-1:-1;;;2064:25:1;;825:18296;-1:-1:-1;825:18296:1;;;;;2064:25;;;;;;;;;825:18296;2064:25;;;;;2040:54;;;825:18296;;;-1:-1:-1;;;;;825:18296:1;;2104:46;825:18296;;;2104:46;825:18296;-1:-1:-1;;;;;825:18296:1;;2160:28;825:18296;;;2160:28;825:18296;-1:-1:-1;;;;;825:18296:1;2198:33;825:18296;;;2198:33;825:18296;2198:33;;;;:::i;:::-;2268:106;;2040:54;2383:106;;;;2040:54;-1:-1:-1;;2570:141:1;;;825:18296;;;;;;;;;;;;2626:23;;;;;;;;;;;;;2570:141;2611:38;2570:141;;2720;;;825:18296;;-1:-1:-1;;;2776:23:1;;825:18296;;;;;;;2776:23;;;;;;1890:16;825:18296;2776:23;825:18296;2776:23;2198:33;2776:23;;;;;;;2720:141;2761:38;2720:141;825:18296;;;;;;;;;;;;;;;;;;-1:-1:-1;;;825:18296:1;;;;;;;;;;;;;;;;-1:-1:-1;;;825:18296:1;;;;-1:-1:-1;;;1916:71:1;825:18296;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;2870:77;825:18296;;:::i;:::-;;;;;;2720:141;825:18296;;;;;;;;;;;;;;;;;;;;1890:16;825:18296;;;;;3207:622:62;825:18296:1;;;;;2870:77;825:18296;;3892:296:62;;825:18296:1;;;3892:296:62;1916:71:1;3892:296:62;;1890:16:1;825:18296;3892:296:62;;825:18296:1;3892:296:62;;825:18296:1;;;;;;-1:-1:-1;825:18296:1;;;;;2870:77;825:18296;;-1:-1:-1;;825:18296:1;;;;;-1:-1:-1;;;;;;;;;;;825:18296:1;;;;;;;;;;1890:16;825:18296;;;;;;;;;;;;2870:77;825:18296;;;;;;;;;;3207:622:62;825:18296:1;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1890:16:1;825:18296;;;;;;;;;;;;;;;;;2870:77;825:18296;;;;;2104:46;825:18296;-1:-1:-1;;;;;;;;;;;825:18296:1;;;;;;;;;;;;;;;2104:46;825:18296;-1:-1:-1;;;;;;;;;;;825:18296:1;;;;;;;;;;;;;;;;;-1:-1:-1;1890:16:1;825:18296;;;;-1:-1:-1;;;;;;;;;;;825:18296:1;-1:-1:-1;825:18296:1;;;-1:-1:-1;;;825:18296:1;;;;;;;;2776:23;;;;;;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;;825:18296;;;;;;;;;2720:141;825:18296;;;;2198:33;1890:16;825:18296;;;;;;:::i;:::-;2720:141;;2626:23;;;;;;;;;;;;;:::i;:::-;;;;;825:18296;;;;;;;;;2570:141;825:18296;;;:::i;:::-;2570:141;;;2383:106;2460:17;;;:::i;:::-;2383:106;;;;2268;2345:17;;;:::i;:::-;2268:106;;;;2064:25;;;;825:18296;2064:25;825:18296;2064:25;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;;;825:18296;;;;;;;;;2040:54;825:18296;2092:2;2040:54;;1957:25;;;;825:18296;1957:25;825:18296;1957:25;;;;;;;:::i;:::-;;;;;825:18296;;;;;;;;;1933:54;825:18296;1985:2;1933:54;;3207:622:62;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;825:18296:1;3207:622:62;;825:18296:1;;;;;;;;;;;;;;;;;;;;;;;;;10164:1922:59;825:18296:1;;;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;;;;:::i;:::-;;;:::i;:::-;1181:103:49;;:::i;:::-;6680:148:59;;;;825:18296:1;6680:148:59;825:18296:1;6680:148:59;;;9349:28:1;;825:18296;;-1:-1:-1;;;;;825:18296:1;;;9435:10;:20;;;9431:95;;825:18296;10015:36;;;:::i;:::-;19466:887:59;;;;;6680:148;;19466:887;;825:18296:1;19466:887:59;825:18296:1;6680:148:59;19466:887;;;;;;;;;;;825:18296:1;19466:887:59;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;825:18296:1;19466:887:59;;10261:6:1;825:18296;-1:-1:-1;;;;;825:18296:1;10261:77;;;;;825:18296;;;10261:77;825:18296;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;10261:77;;825:18296;;10261:77;;825:18296;;;;;;;;;;;;;;;;;;;;;;10261:77;;;;;;;;825:18296;-1:-1:-1;10783:13:1;825:18296;10164:1922:59;825:18296:1;10164:1922:59;;-1:-1:-1;;;;;825:18296:1;;;;;10783:44;;;;;825:18296;;-1:-1:-1;;;10783:44:1;;825:18296;10783:44;;825:18296;;;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;;;;;;;10783:44;;;;;;;;825:18296;-1:-1:-1;10783:13:1;825:18296;10875:6;825:18296;-1:-1:-1;;;;;825:18296:1;;;;;10837:45;;;;;825:18296;;-1:-1:-1;;;10837:45:1;;825:18296;10837:45;;825:18296;;;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;;;;;;;10837:45;;;;;;;;825:18296;10164:1922:59;;10437:69:1;10164:1922:59;11001:8:1;10164:1922:59;10937:8:1;825:18296;10164:1922:59;;;;;;;825:18296:1;;;10937:8;;:::i;:::-;10875:6;825:18296;-1:-1:-1;;;;;825:18296:1;11001:8;;:::i;:::-;825:18296;;;;;1149:5;;;825:18296;;;1149:5;;;825:18296;;;;-1:-1:-1;;;;;825:18296:1;;;;1149:5;;;825:18296;1149:5;;10437:69;-1:-1:-1;;;;;;;;;;;3556:68:51;825:18296:1;;;;;;;;;;10837:45;;;;;;:::i;:::-;825:18296;;10837:45;;;;825:18296;;;;;;;;;10783:44;;;;;:::i;:::-;825:18296;;10783:44;;;;10261:77;;;;;825:18296;10261:77;;:::i;:::-;825:18296;10261:77;;;;;825:18296;;;;;;;;;10261:77;825:18296;;;19466:887:59;;825:18296:1;19466:887:59;825:18296:1;19466:887:59;;9431:95:1;9435:10;825:18296;22675:798:59;;6680:148;22675:798;;825:18296:1;22675:798:59;;6680:148;22675:798;;;825:18296:1;22675:798:59;;;;9431:95:1;;;;;22675:798:59;;;;;;;;;;;;;;;;;825:18296:1;22675:798:59;825:18296:1;22675:798:59;;825:18296:1;;;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;4743:30;825:18296;;4743:30;:::i;:::-;825:18296;;;;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;;:::i;:::-;;;8378:1143:59;;;;;825:18296:1;8378:1143:59;825:18296:1;8378:1143:59;;;;;;;;;;;;;;825:18296:1;8378:1143:59;825:18296:1;8378:1143:59;;;;;;;;825:18296:1;8378:1143:59;;;;;;-1:-1:-1;;;;;;;;;;;825:18296:1;8378:1143:59;;825:18296:1;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;-1:-1:-1;;;;;1476:29:1;825:18296;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;825:18296:1;;;;;;;:::i;:::-;5591:7;4743:30;825:18296;;4743:30;:::i;:::-;5591:7;;;;;;:::i;:::-;;825:18296;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;5591:7;825:18296;;:::i;:::-;;;;;5591:7;:::i;:::-;825:18296;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;;;;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;1408:24;825:18296;;;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;;:::i;:::-;12929:205:59;;;825:18296:1;12929:205:59;825:18296:1;12929:205:59;;;;825:18296:1;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;1438:32;825:18296;10164:1922:59;;;;;1438:32:1;825:18296;;1438:32;825:18296;-1:-1:-1;;;;;10164:1922:59;;;;;1438:32:1;825:18296;;1438:32;825:18296;1438:32;825:18296;;1438:32;825:18296;;1438:32;825:18296;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;4462:40;4408:37;;:::i;:::-;825:18296;;;;;;4462:40;:::i;825:18296::-;;;;;;-1:-1:-1;;825:18296:1;;;;;;:::i;:::-;;;;;;-1:-1:-1;;825:18296:1;;;;;;:::i;:::-;6680:148:59;;;825:18296:1;6680:148:59;825:18296:1;6680:148:59;;;;825:18296:1;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;6466:184:64;6407:6;6466:184;;;825:18296:1;;;2619:66:64;825:18296:1;;;6466:184:64;;825:18296:1;6466:184:64;825:18296:1;6466:184:64;;825:18296:1;;;-1:-1:-1;;825:18296:1;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5771:446:64;5712:6;5771:446;;;3244:89:1;;:::i;:::-;10164:1922:59;;;;;4259:1327:64;;;;;825:18296:1;4259:1327:64;825:18296:1;4259:1327:64;;;;;;;;;;825:18296:1;4259:1327:64;;;;;;;;825:18296:1;4259:1327:64;;825:18296:1;4259:1327:64;825:18296:1;;4259:1327:64;825:18296:1;;4259:1327:64;;;;;;;;;;825:18296:1;4259:1327:64;;825:18296:1;4259:1327:64;;;;;;;;;825:18296:1;4259:1327:64;;825:18296:1;;;;;;-1:-1:-1;;825:18296:1;;;;;16961:346:59;825:18296:1;;:::i;:::-;;;;;;16884:24:59;825:18296:1;16961:346:59;;;;;;;;;;825:18296:1;16961:346:59;;;;;;;;;;;;;;825:18296:1;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;;;5933:2:59;825:18296:1;;;;;;;;;-1:-1:-1;;825:18296:1;;;;1303:21;825:18296;;;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;;:::i;:::-;;;:::i;:::-;;;10164:1922:59;;825:18296:1;10164:1922:59;;825:18296:1;10164:1922:59;;;;;;;;;;;;;825:18296:1;10164:1922:59;;;;825:18296:1;10164:1922:59;;;;;;;825:18296:1;10164:1922:59;;;;;;;;;;;;;;825:18296:1;10164:1922:59;825:18296:1;10164:1922:59;;;;;;;;825:18296:1;10164:1922:59;;;825:18296:1;10164:1922:59;;;;;;;;-1:-1:-1;;;;;;;;;;;825:18296:1;10164:1922:59;;825:18296:1;;;;;;;10164:1922:59;;;;;;;;;;;;;;;825:18296:1;;;;;;-1:-1:-1;;825:18296:1;;;;;6408:68:59;;825:18296:1;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;14377:35;825:18296;;14377:35;:::i;:::-;14344:68;;;825:18296;;;;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;;:::i;:::-;;;7568:413:59;825:18296:1;7568:413:59;;;;;825:18296:1;7568:413:59;;;;;;825:18296:1;7568:413:59;;;;;;;825:18296:1;;7568:413:59;825:18296:1;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;825:18296:1;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;;;;;;;-1:-1:-1;;;;;825:18296:1;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;825:18296:1;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;825:18296:1;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;-1:-1:-1;825:18296:1;;;;;-1:-1:-1;825:18296:1;5150:184;5230:13;825:18296;10164:1922:59;825:18296:1;;;-1:-1:-1;;;5230:47:1;;5263:4;5230:47;;;825:18296;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;5230:47;825:18296;;;;5230:47;;;;;;;-1:-1:-1;5230:47:1;;;5150:184;-1:-1:-1;5319:6:1;825:18296;;;-1:-1:-1;;;5279:47:1;;5263:4;5230:47;5279;;825:18296;-1:-1:-1;;;;;825:18296:1;;;;;;;;;5230:47;;825:18296;;;;;;5279:47;;;;;;;-1:-1:-1;5279:47:1;;;5222:105;;5150:184;:::o;5279:47::-;;;;5230;5279;;5230;5279;;;;;;825:18296;5279:47;;;:::i;:::-;;;825:18296;;;;;5150:184;:::o;5279:47::-;;;-1:-1:-1;5279:47:1;;5230;;;;;;;;;;;;;825:18296;5230:47;;;:::i;:::-;;;825:18296;;;;;;;5230:47;;;;;;-1:-1:-1;5230:47:1;;825:18296;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;825:18296:1;3425:5;825:18296;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:::o;:::-;-1:-1:-1;3425:5:1;-1:-1:-1;825:18296:1;;;;;-1:-1:-1;;;;;;;;;;;825:18296:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;;;825:18296:1;;;;;;:::o;:::-;;;-1:-1:-1;;;;;825:18296:1;;;;;;:::o;3640:524::-;4133:6;825:18296;;;-1:-1:-1;;;4133:24:1;;825:18296;4133:24;;825:18296;;4133:24;;825:18296;;-1:-1:-1;;;;;825:18296:1;4133:24;;;;;;-1:-1:-1;;;;;;;;;;;;;;;;;;;;4133:24:1;;;3640:524;825:18296;;-1:-1:-1;;;;;825:18296:1;;4096:24;825:18296;4058:24;825:18296;;3955:21;825:18296;;;;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;;;;;;;3955:21;825:18296;3910:31;825:18296;10164:1922:59;;;;;825:18296:1;-1:-1:-1;;;;;825:18296:1;;3866:30;825:18296;;;3866:30;825:18296;3822:30;825:18296;10164:1922:59;;;;;825:18296:1;-1:-1:-1;;;;;825:18296:1;;3779:29;825:18296;;;3779:29;825:18296;-1:-1:-1;;;;;825:18296:1;3698:12;825:18296;;;;;;;;;;;;;3698:12;825:18296;3640:524::o;4133:24::-;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;825:18296;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;;-1:-1:-1;;;;;825:18296:1;;;;;:::i;:::-;;;;;;:::i;:::-;;;;;;;;;;4133:24;;;;;;;;;;;;;;;;;-1:-1:-1;4133:24:1;;1181:103:49;;;825:18296:1;1181:103:49;;;:::i;:::-;10164:1922:59;825:18296:1;-1:-1:-1;;;;;825:18296:1;5636:20;;:44;;;;1181:103:49;5635:162:1;;;5714:49;;;;;5700:9;:64;;5635:162;825:18296;;;5879:52;6214:37;;;:::i;:::-;6408:68:59;;6164:87:1;;;;6290:18;6408:68:59;;6395:78:1;6355:253;6395:142;:78;;;;:::i;:::-;6500:37;6506:31;825:18296;6500:37;:::i;:::-;6395:142;;:::i;:::-;6572:36;6578:30;825:18296;6572:36;:::i;:::-;6355:253;;:::i;:::-;6630:36;;;1102:7;;6736:69;;;;;:::i;:::-;6819:35;6884:7;;;;;:::i;:::-;7363:11;;1102:7;;7506:25;;7478:54;7506:25;;:::i;:::-;7478:54;:::i;:::-;10164:1922:59;;;;;;;7542:6:1;825:18296;;1201:5;-1:-1:-1;;;;;7740:15:1;1201:5;825:18296;1149:5;1201;;1149;1201;;;1149;1055:8;;;;;;1149:5;1055:8;;;;;;;27089:32:13;27088:44;1055:8:1;;5636:6;1055:8;27089:32:13;;:::i;:::-;27088:44;:::i;:::-;7542:243:1;;;;;;825:18296;-1:-1:-1;;;;;825:18296:1;7542:243;825:18296;;;;;;;;;;;;;;7542:243;;825:18296;7542:243;;;825:18296;;1149:5;;;825:18296;1149:5;;;825:18296;1149:5;;;825:18296;1149:5;;;;825:18296;7542:243;;;;;;;;6286:1060;-1:-1:-1;10164:1922:59;825:18296:1;7847:6;825:18296;-1:-1:-1;;;;;825:18296:1;;;;10164:1922:59;825:18296:1;8456:21;;;;;;;;;8493:13;825:18296;-1:-1:-1;;;;;825:18296:1;8493:78;;;;;825:18296;;-1:-1:-1;;;8493:78:1;;8543:4;7542:243;8493:78;;825:18296;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;;;;;;;;;;;;;8493:78;;;;;;;;8452:297;;;;8763:21;;;8759:301;;;8763:21;;;8800:13;825:18296;-1:-1:-1;;;;;825:18296:1;8800:80;;;;;825:18296;;-1:-1:-1;;;8800:80:1;;8851:4;7542:243;8800:80;;825:18296;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;;;;;;;;;;;;;8800:80;;;;;;;;8759:301;;;;8205:62;8759:301;8205:62;8759:301;;7984:108;;;8009:9;:23;:9;;:23;:::i;:::-;8106:17;8102:89;;7984:108;-1:-1:-1;825:18296:1;;;;;;1149:5;;;825:18296;;;;1149:5;;;825:18296;;;-1:-1:-1;;;;;825:18296:1;;;1149:5;;;825:18296;;;;1149:5;;;;;8205:62;;;;3556:68:51;-1:-1:-1;;;;;;;;;;;3556:68:51;1181:103:49:o;8102:89:1:-;8166:13;8139:10;;8166:13;:::i;:::-;8102:89;;;7984:108;8037:54;;;8062:24;:9;;:24;:::i;:::-;7984:108;;8037:54;;7984:108;;8800:80;;;;;;;;:::i;:::-;825:18296;;8800:80;;;;;825:18296;;;;8759:301;8963:12;8956:4;;8936:10;8963:12;;:::i;:::-;8990:13;825:18296;-1:-1:-1;;;;;825:18296:1;8990:59;;;;;825:18296;;-1:-1:-1;;;8990:59:1;;8956:4;7542:243;8990:59;;825:18296;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;;;;;;;;;;;;;8990:59;;;;;;;;8759:301;;;;8205:62;8759:301;8205:62;8759:301;;;8990:59;;;;;;;;:::i;:::-;825:18296;;8990:59;;;;;8493:78;;;;;:::i;:::-;825:18296;;8493:78;;;;8452:297;8654:11;8647:4;;8627:10;8654:11;;:::i;:::-;8680:13;825:18296;-1:-1:-1;;;;;825:18296:1;8680:58;;;;;825:18296;;-1:-1:-1;;;8680:58:1;;8647:4;7542:243;8680:58;;825:18296;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;;;;;;;;;;;;;8680:58;;;;;;;;8452:297;;;;;8680:58;;;;;:::i;:::-;825:18296;;8680:58;;;;7542:243;;;;;825:18296;7542:243;;:::i;:::-;825:18296;7542:243;;;;1055:8;825:18296;;;;1055:8;;;;;825:18296;1055:8;1102:7;;;;825:18296;1102:7;;825:18296;1102:7;;;;;825:18296;1102:7;;825:18296;1102:7;6286:1060;6956:26;;;;;;;:::i;:::-;30193:2941:13;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5636:6:1;30193:2941:13;;;;;;;;;5636:6:1;30193:2941:13;;;;;5636:6:1;30193:2941:13;;;;;5636:6:1;30193:2941:13;;;;;5636:6:1;30193:2941:13;;;;;5636:6:1;30193:2941:13;;;;;5636:6:1;30193:2941:13;;;;;5636:6:1;30193:2941:13;;;;;;;;10164:1922:59;;;;;;30193:2941:13;825:18296:1;;17884:946:59;;1102:7:1;17884:946:59;;;;;;;;;;;;825:18296:1;17884:946:59;;;;1102:7:1;17884:946:59;;;;;1102:7:1;17884:946:59;;;;;;825:18296:1;-1:-1:-1;;;;;;;;;;;17884:946:59;;;-1:-1:-1;;1102:7:1;;;;;;;7170:51;7055:24;7109:7;7055:24;7109:7;;:::i;:::-;7185:36;7191:30;825:18296;7185:36;:::i;7170:51::-;1055:8;;;;;;;;;;;;;;;825:18296;7168:137;7150:185;825:18296;7268:37;7274:31;825:18296;7268:37;:::i;7150:185::-;6286:1060;;;17884:946:59;;825:18296:1;17884:946:59;;;;825:18296:1;;;;;;;;;5714:49;;;;5635:162;5783:9;;:14;5635:162;;5636:44;-1:-1:-1;5660:6:1;825:18296;-1:-1:-1;;;;;825:18296:1;5660:20;5636:44;;825:18296;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;825:18296:1;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;825:18296:1;;;;;:::i;:::-;;;;;;;;;;;;;-1:-1:-1;825:18296:1;;;;;;;;;;;;;;:::o;1055:8::-;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;:::o;:::-;825:18296;;;1055:8;;;;;;;;13331:1098;6408:68:59;;13526:18:1;;;13522:70;;14052:142;:78;13922:61;13810:84;13653:13;;14012:253;13653:13;;;:::i;:::-;13711:37;;;;;:::i;:::-;13810:84;;;;;;:::i;:::-;13922:61;;;:::i;:::-;14052:78;;;:::i;14012:253::-;13331:1098;:::o;13522:70::-;13564:13;;13543:1;13564:13;13543:1;13564:13;:::o;1201:5::-;;;;;;;;;;:::o;11827:873::-;12211:6;825:18296;;;-1:-1:-1;;;12211:26:1;;825:18296;11827:873;12211:26;;825:18296;;12211:26;;825:18296;;-1:-1:-1;;;;;825:18296:1;12211:26;;;;;;;-1:-1:-1;12211:26:1;;;11827:873;1201:5;-1:-1:-1;;;;;12263:15:1;1201:5;825:18296;1201:5;;;;;;;;;;12211:26;825:18296;12140:187;825:18296;-1:-1:-1;;;;;12163:150:1;825:18296;;12163:150;:::i;:::-;12140:187;:::i;:::-;12211:6;825:18296;;;-1:-1:-1;;;12554:21:1;;825:18296;12211:26;;825:18296;;;;;-1:-1:-1;;;;;825:18296:1;12554:21;;;;;;;-1:-1:-1;12554:21:1;;;11827:873;825:18296;12594:60;12593:91;825:18296;-1:-1:-1;;;;;12618:36:1;12624:30;825:18296;12618:36;:::i;:::-;825:18296;;12594:60;:::i;:::-;-1:-1:-1;;;;;12624:12:1;825:18296;12211:26;825:18296;;12593:91;;:::i;:::-;12585:108;11827:873;:::o;12554:21::-;;;;12211:26;12554:21;;12211:26;12554:21;;;;;;825:18296;12554:21;;;:::i;:::-;;;825:18296;;;;;;12594:60;12554:21;;;;;-1:-1:-1;12554:21:1;;12211:26;;;;;;;;;;;;;;;;;;:::i;:::-;;;825:18296;;;;;;;:::i;:::-;12211:26;;;;;;;-1:-1:-1;12211:26:1;;12788:537;13169:62;12788:537;13040:77;13245:63;12788:537;;12992:13;;:::i;:::-;13040:77;;:::i;:::-;6408:68:59;;;;13169:62:1;;;:::i;:::-;13245:63;;:::i;:::-;44135:70:13;;;;;;;;12788:537:1;:::o;1290:377:49:-;-1:-1:-1;;;;;;;;;;;3327:69:51;1444:93:49;;1655:4;-1:-1:-1;;;;;;;;;;;3556:68:51;1290:377:49:o;1444:93::-;1496:30;;;-1:-1:-1;1496:30:49;;-1:-1:-1;1496:30:49;1102:7:1;;;;;;;;;;:::o;16262:936:63:-;16388:804;;;16262:936;;16388:804;16262:936;16388:804;;825:18296:1;;16388:804:63;;;;;;;;;;;;;;;;;;;;;;;16262:936::o;16388:804::-;;;;;;;3121:117:1;3190:5;825:18296;-1:-1:-1;;;;;825:18296:1;3176:10;:19;825:18296;;3121:117::o;825:18296::-;;;;3190:5;825:18296;;3190:5;825:18296;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;15168:12:1;825:18296;;;;;;;;;;;;;10164:1922:59;;;;;825:18296:1;;;;;;;;;;;;;10164:1922:59;;;;;825:18296:1;;;;;;;;;;;;;-1:-1:-1;;;;;825:18296:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;-1:-1:-1;825:18296:1;;;;;;;;;;;;;:::o;:::-;;;;;;;-1:-1:-1;;;825:18296:1;;10164:1922:59;825:18296:1;;;-1:-1:-1;;825:18296:1;;;;;:::o;:::-;;;;;;;;;-1:-1:-1;;;825:18296:1;;;;;;;;;;;;;;;:::o;14435:3092::-;12411:6;825:18296;;;-1:-1:-1;;;12411:26:1;;825:18296;12411:26;;825:18296;;12411:26;;825:18296;;-1:-1:-1;;;;;825:18296:1;12411:26;;;;;;;14646:5;12411:26;;;14435:3092;1201:5;-1:-1:-1;;;;;12463:15:1;1201:5;825:18296;1201:5;;;;;;;;;;12411:26;825:18296;12342:185;825:18296;-1:-1:-1;;;;;12365:148:1;825:18296;;12365:148;:::i;12342:185::-;12411:6;825:18296;;;-1:-1:-1;;;12554:21:1;;10164:1922:59;;825:18296:1;;12411:26;;825:18296;;;;;-1:-1:-1;;;;;825:18296:1;12554:21;;;;;;;14646:5;12554:21;;;14435:3092;825:18296;12593:91;12594:60;12411:26;825:18296;-1:-1:-1;;;;;12618:36:1;12624:30;825:18296;12618:36;:::i;12593:91::-;12585:108;6408:68:59;14823:52:1;14696:74;6408:68:59;;14710:60:1;;;;;;:::i;:::-;14696:74;;:::i;14823:52::-;12411:6;825:18296;;;-1:-1:-1;;;15099:23:1;;825:18296;10164:1922:59;;;;;15099:23:1;;825:18296;;;;;-1:-1:-1;;;;;825:18296:1;15099:23;;;;;;;14646:5;;;;;;;15099:23;;;14435:3092;825:18296;-1:-1:-1;;;;;825:18296:1;;;:::i;:::-;15238:13;;;:::i;:::-;825:18296;;;;;-1:-1:-1;;;;;825:18296:1;;15265:37;;:78;;;;14435:3092;15261:2260;;;-1:-1:-1;;;;;825:18296:1;15403:83;15385:163;15403:83;;;;:::i;:::-;15510:37;15516:31;;;825:18296;15510:37;:::i;:::-;15385:163;;:::i;:::-;15566:27;12411:26;15566:27;;825:18296;-1:-1:-1;;;;;825:18296:1;;;15385:210;825:18296;;;;-1:-1:-1;;;825:18296:1;;-1:-1:-1;;825:18296:1;;;;;;;15787:93;15636:308;825:18296;15905:38;825:18296;-1:-1:-1;;;;;15661:95:1;1055:8;825:18296;15911:32;825:18296;15654:227;825:18296;;;;;;;15661:95;;:::i;:::-;825:18296;;;15787:93;;:::i;:::-;15654:227;;:::i;:::-;15911:32;;825:18296;15905:38;:::i;15636:308::-;825:18296;;14646:5;16067:20;;14646:5;;;16225:61;:94;16146:21;16122:47;16297:21;16146;;;;:::i;16122:47::-;16225:61;;:::i;:::-;16297:21;;:::i;:::-;16225:94;;:::i;:::-;16063:512;;14646:5;16592:21;;14646:5;;;16755:96;16674:22;16828;16674;16649:49;16755:62;16674:22;;;;:::i;16649:49::-;16755:62;;:::i;:96::-;16588:525;;17126:74;;;;17195:4;17126:74;:::o;16588:525::-;16906:44;17007:62;16906:44;;;17007:91;16906:44;;:::i;17007:62::-;:91;:::i;:::-;16588:525;;;16063:512;16471:89;16373:42;;;16471:61;16373:42;;:::i;:::-;16471:61;;:::i;:89::-;16063:512;;;15261:2260;17257:61;;;;;;;;17359:62;17257:61;;;;:::i;:::-;17359:62;;:::i;:::-;17435:75;;;14646:5;17435:75;:::o;15265:78::-;825:18296;-1:-1:-1;;;;;825:18296:1;;-1:-1:-1;;;;;825:18296:1;;15306:37;15265:78;;15099:23;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;825:18296;;;;-1:-1:-1;;;;;825:18296:1;;;;:::i;:::-;;12411:26;825:18296;;;;;;;;;:::i;:::-;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;:::i;:::-;;15099:23;;;;;;;;;;-1:-1:-1;15099:23:1;;12554:21;;;;12411:26;12554:21;;12411:26;12554:21;;;;;;825:18296;12554:21;;;:::i;:::-;;;825:18296;;;;;;12554:21;;12593:91;12554:21;;;;;-1:-1:-1;12554:21:1;;12411:26;;;;;;;;;;;;;;;;;:::i;:::-;;;825:18296;;;;;;;:::i;:::-;12411:26;;;;;;-1:-1:-1;12411:26:1;;18170:750;;;;;18465:23;825:18296;;;:::i;:::-;18465:23;;:::i;:::-;18497:32;18556:31;18465:64;18497:32;;;825:18296;18491:38;825:18296;;18491:38;:::i;18465:64::-;18556:31;;825:18296;18550:37;825:18296;;18550:37;:::i;:::-;1055:8;;;;;;;;;;;;;;;;18464:158;:146;;;;:::i;:::-;:158;:::i;:::-;1055:8;;;;;;;;;;;;;;;;;;18793:38;18691:88;18677:169;825:18296;18742:37;18793:52;825:18296;;18742:37;:::i;18691:88::-;825:18296;;18793:38;:::i;:::-;:52;:::i;25862:482:13:-;25998:340;;;;;;;;;;;;;;;;;25862:482;:::o;25998:340::-;;;;;;;26444:516;26582:372;;;;;;;;;;;1055:8:1;26582:372:13;;;;1055:8:1;26582:372:13;;;;;;;;26444:516;:::o;:::-;26582:372;;;;26444:516;26582:372;;;;;;;;;;;;;;;;;;;;26444:516;:::o;18926:158:1:-;;-1:-1:-1;;;;;825:18296:1;;19009:30;;;825:18296;;18926:158::o;825:18296::-;;;;;;;;;17717:1172:59;17884:946;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;17884:946:59;;;17717:1172::o;17533:631:1:-;;825:18296;;:::i;:::-;1201:5;-1:-1:-1;;;;;17739:15:1;1201:5;825:18296;17739:29;;;;;:::i;:::-;17771:27;17739:59;17771:27;;;825:18296;-1:-1:-1;;;;;825:18296:1;;;17739:59;;:::i;:::-;1201:5;;;;;;;;;;17962:29;:59;17869:31;17719:192;17738:163;-1:-1:-1;;;;;17869:31:1;17819:81;17869:31;18066:37;17869:31;;825:18296;17863:37;825:18296;;17863:37;:::i;17719:192::-;17962:29;;:::i;:::-;825:18296;;;17962:59;;:::i;:::-;825:18296;;18066:37;:::i;:::-;1055:8;;1201:5;1055:8;;1201:5;1055:8;;;;;17942:172;17961:143;;;;:::i;4031:342:63:-;4146:221;4031:342;;;4146:221;;;;;;;4031:342::o;4146:221::-;;;;;;;9109:1139;9254:988;;;9109:1139;;;9254:988;9109:1139;9254:988;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;9109:1139::o;9254:988::-;;;;;;;11105:254:1;;-1:-1:-1;;;;;825:18296:1;;10164:1922:59;;11268:6:1;;;;:::i;11204:149::-;13593:806:63;11225:1:1;13593:806:63;;;;;;;;;;;;;;;;;;;;11225:1:1;13593:806:63;;;;;;;11225:1:1;13593:806:63;;11105:254:1:o;13593:806:63:-;;11225:1:1;13593:806:63;;;", "linkReferences": {}, "immutableReferences": {"61731": [{"start": 3854, "length": 32}, {"start": 4031, "length": 32}]}}, "methodIdentifiers": {"DOMAIN_SEPARATOR()": "3644e515", "SPREAD_CONSTANT()": "a68ee881", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "decimals()": "313ce567", "deposit(uint256,uint256,address)": "8dbdbe6d", "initialize(address,address,address,address,address,uint96)": "d246ce16", "marginAccount()": "f742269d", "market()": "80f55605", "marketParams()": "7b9e68f2", "mint(uint256,address)": "94bf804d", "name()": "06fdde03", "nonces(address)": "7ecebe00", "owner()": "8da5cb5b", "permit(address,address,uint256,uint256,uint8,bytes32,bytes32)": "d505accf", "previewDeposit(uint256,uint256)": "7a7af70d", "previewMint(uint256)": "b3d7f6b9", "previewWithdraw(uint256)": "0a28a477", "proxiableUUID()": "52d1902d", "setMarketParams()": "7660b153", "symbol()": "95d89b41", "token1()": "d21220a7", "token2()": "25be124e", "totalAssets()": "01e1d114", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd", "transferOwnership(address)": "f2fde38b", "upgradeToAndCall(address,bytes)": "4f1ef286", "withdraw(uint256,address,address)": "b460af94"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AllowanceOverflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AllowanceUnderflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientLiquidityMinted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientQuoteToken\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidPermit\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NativeAssetMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PermitExpired\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TotalSupplyOverflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Uint96Overflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnauthorizedCallContext\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UpgradeFailed\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount1\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount2\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"userAddress\",\"type\":\"address\"}],\"name\":\"KuruVaultDeposit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount1\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount2\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"userAddress\",\"type\":\"address\"}],\"name\":\"KuruVaultWithdraw\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"Upgraded\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"DOMAIN_SEPARATOR\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"result\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SPREAD_CONSTANT\",\"outputs\":[{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"result\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"result\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"baseDeposit\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"quoteDeposit\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"deposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token1_\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token2_\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_marginAccount\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_market\",\"type\":\"address\"},{\"internalType\":\"uint96\",\"name\":\"_spreadConstant\",\"type\":\"uint96\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"marginAccount\",\"outputs\":[{\"internalType\":\"contract IMarginAccount\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"market\",\"outputs\":[{\"internalType\":\"contract IOrderBook\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"marketParams\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"pricePrecision\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"sizePrecision\",\"type\":\"uint96\"},{\"internalType\":\"address\",\"name\":\"baseAssetAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"baseAssetDecimals\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"quoteAssetAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"quoteAssetDecimals\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"tickSize\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"minSize\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"maxSize\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"takerFeeBps\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"makerFeeBps\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"mint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"nonces\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"result\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"permit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"asset1\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"asset2\",\"type\":\"uint256\"}],\"name\":\"previewDeposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"previewMint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"previewWithdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"proxiableUUID\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"setMarketParams\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"token1\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"token2\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"result\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"upgradeToAndCall\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_shares\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"}],\"name\":\"withdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"errors\":{\"AllowanceOverflow()\":[{\"details\":\"The allowance has overflowed.\"}],\"AllowanceUnderflow()\":[{\"details\":\"The allowance has underflowed.\"}],\"InsufficientAllowance()\":[{\"details\":\"Insufficient allowance.\"}],\"InsufficientBalance()\":[{\"details\":\"Thrown when balance of owner is too less\"},{\"details\":\"Insufficient balance.\"}],\"InsufficientLiquidityMinted()\":[{\"details\":\"Thrown when insufficient liquidity is minted\"}],\"InsufficientQuoteToken()\":[{\"details\":\"Thrown when amount of quote tokens passed is insufficient\"}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"InvalidPermit()\":[{\"details\":\"The permit is invalid.\"}],\"NativeAssetMismatch()\":[{\"details\":\"Thrown when native token passed as argument and msg.value does not match\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"PermitExpired()\":[{\"details\":\"The permit has expired.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}],\"TotalSupplyOverflow()\":[{\"details\":\"The total supply has overflowed.\"}],\"Uint96Overflow()\":[{\"details\":\"Thrown when safe cast to uint96 fails\"}],\"Unauthorized()\":[{\"details\":\"Thrown when a user is not the owner and tries to execute a privileged function\"}],\"UnauthorizedCallContext()\":[{\"details\":\"The call is from an unauthorized call context.\"}],\"UpgradeFailed()\":[{\"details\":\"The upgrade failed.\"}]},\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when `amount` tokens is approved by `owner` to be used by `spender`.\"},\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `amount` tokens is transferred from `from` to `to`.\"},\"Upgraded(address)\":{\"details\":\"Emitted when the proxy's implementation is upgraded.\"}},\"kind\":\"dev\",\"methods\":{\"DOMAIN_SEPARATOR()\":{\"details\":\"Returns the EIP-712 domain separator for the EIP-2612 permit.\"},\"allowance(address,address)\":{\"details\":\"Returns the amount of tokens that `spender` can spend on behalf of `owner`.\"},\"approve(address,uint256)\":{\"details\":\"Sets `amount` as the allowance of `spender` over the caller's tokens. Emits a {Approval} event.\"},\"balanceOf(address)\":{\"details\":\"Returns the amount of tokens owned by `owner`.\"},\"decimals()\":{\"details\":\"Returns the decimals places of the token.\"},\"deposit(uint256,uint256,address)\":{\"details\":\"Deposits token1 and token2 into the vault and mints shares to the receiver.\"},\"mint(uint256,address)\":{\"details\":\"Mints shares to the receiver in exchange for depositing token1 and token2.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"nonces(address)\":{\"details\":\"Returns the current nonce for `owner`. This value is used to compute the signature for EIP-2612 permit.\"},\"permit(address,address,uint256,uint256,uint8,bytes32,bytes32)\":{\"details\":\"Sets `value` as the allowance of `spender` over the tokens of `owner`, authorized by a signed approval by `owner`. Emits a {Approval} event.\"},\"previewDeposit(uint256,uint256)\":{\"details\":\"Previews the amount of shares to be minted for a given deposit of token1 and token2.\"},\"previewMint(uint256)\":{\"details\":\"Previews the amount of token1 and token2 to be received for a given amount of shares to be minted.\"},\"previewWithdraw(uint256)\":{\"details\":\"Previews the amount of token1 and token2 to be received for a given amount of shares to be burned.\"},\"proxiableUUID()\":{\"details\":\"Returns the storage slot used by the implementation, as specified in [ERC1822](https://eips.ethereum.org/EIPS/eip-1822). Note: The `notDelegated` modifier prevents accidental upgrades to an implementation that is a proxy contract.\"},\"setMarketParams()\":{\"details\":\"Fetches and stores the market params from the market contract.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token.\"},\"totalAssets()\":{\"details\":\"Returns the total assets of the vault in token1 and token2.\"},\"totalSupply()\":{\"details\":\"Returns the amount of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"Transfer `amount` tokens from the caller to `to`. Requirements: - `from` must at least have `amount`. Emits a {Transfer} event.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"Transfers `amount` tokens from `from` to `to`. Note: Does not update the allowance if it is the maximum uint256 value. Requirements: - `from` must at least have `amount`. - The caller must have at least `amount` of allowance to transfer the tokens of `from`. Emits a {Transfer} event.\"},\"upgradeToAndCall(address,bytes)\":{\"details\":\"Upgrades the proxy's implementation to `newImplementation`. Emits a {Upgraded} event. Note: Passing in empty `data` skips the delegatecall to `newImplementation`.\"},\"withdraw(uint256,address,address)\":{\"details\":\"Withdraws token1 and token2 from the vault by burning the specified amount of shares.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/KuruAMMVault.sol\":\"KuruAMMVault\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/KuruAMMVault.sol\":{\"keccak256\":\"0x110d6ea2c23c31eafdcbc244727243902a6bd406bf4a509851614842a5157eb4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae1964d8b700b192b2483b9b841340f145fc84b3a13fb3001198c23f00e390e4\",\"dweb:/ipfs/QmZBNTQmH4ucyQZSSfyUJ7eMNFYiHuaNGUAzkGaQdxraF4\"]},\"contracts/interfaces/IKuruAMMVault.sol\":{\"keccak256\":\"0xf0460e901fd738f2046c075784aa3045a58342c2f8ed276df83cf5386be969c7\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://54c3a5647cce41595226f4bbdc403bf6ef91177f3caa57de908e9ecaa2ac6460\",\"dweb:/ipfs/QmcWxssB8rFcX1QCZBqHzmmE3Dq2AHUFPfJvogzegQnMoS\"]},\"contracts/interfaces/IMarginAccount.sol\":{\"keccak256\":\"0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4\",\"dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo\"]},\"contracts/interfaces/IOrderBook.sol\":{\"keccak256\":\"0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d\",\"dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68\"]},\"contracts/libraries/Errors.sol\":{\"keccak256\":\"0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7\",\"dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu\"]},\"contracts/libraries/FixedPointMathLib.sol\":{\"keccak256\":\"0x90a5a4d76dd7cdbf8ee357bd0cb963ad5fb2f3d58444f213872f0e7e23ffbebd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://99f2d78bf91870c6815b4eec8aef48fec7e94de2dba2255f8fb8fe3cb9280946\",\"dweb:/ipfs/QmRjtG3C4hrXAc9hsyRpvzEZXhX3ivMktKB9hYgTaLAi4N\"]},\"lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol\":{\"keccak256\":\"0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e\",\"dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3\"]},\"lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol\":{\"keccak256\":\"0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de\",\"dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22\"]},\"node_modules/solady/src/tokens/ERC20.sol\":{\"keccak256\":\"0xb4a3f9ba8a05107f7370de42cff57f3ad26dafd438712c11531a5892de2f59e0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f0a9ca06e3cf6dea1f9a4c5599581573b7d81cd64dc3afb582f325ccf5fdd6dc\",\"dweb:/ipfs/Qmb9r5dDceNF4W8S5u6i85RsNTgE5XG9HbTXkyS25ad3C6\"]},\"node_modules/solady/src/utils/Initializable.sol\":{\"keccak256\":\"0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559\",\"dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy\"]},\"node_modules/solady/src/utils/SafeTransferLib.sol\":{\"keccak256\":\"0x583f47701d9b47bb3ef80fcabbbd62fbb58a01733b7a57e19658b4b02468883a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2523bfac005e21ef9963fdb3c08b2c61824e2b5ce2f53d1a1828b01ed995217c\",\"dweb:/ipfs/QmbBjVG9tZyeZSQH4m5GUzNBwo2iuvLoZYbmhT4gxnJc4J\"]},\"node_modules/solady/src/utils/UUPSUpgradeable.sol\":{\"keccak256\":\"0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16\",\"dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "AllowanceOverflow"}, {"inputs": [], "type": "error", "name": "AllowanceUnderflow"}, {"inputs": [], "type": "error", "name": "InsufficientAllowance"}, {"inputs": [], "type": "error", "name": "InsufficientBalance"}, {"inputs": [], "type": "error", "name": "InsufficientBalance"}, {"inputs": [], "type": "error", "name": "InsufficientLiquidityMinted"}, {"inputs": [], "type": "error", "name": "InsufficientQuoteToken"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "InvalidPermit"}, {"inputs": [], "type": "error", "name": "NativeAssetMismatch"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [], "type": "error", "name": "PermitExpired"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [], "type": "error", "name": "TotalSupplyOverflow"}, {"inputs": [], "type": "error", "name": "Uint96Overflow"}, {"inputs": [], "type": "error", "name": "Unauthorized"}, {"inputs": [], "type": "error", "name": "UnauthorizedCallContext"}, {"inputs": [], "type": "error", "name": "UpgradeFailed"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "amount1", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amount2", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}, {"internalType": "address", "name": "userAddress", "type": "address", "indexed": false}], "type": "event", "name": "KuruVaultDeposit", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "amount1", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amount2", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}, {"internalType": "address", "name": "userAddress", "type": "address", "indexed": false}], "type": "event", "name": "KuruVaultWithdraw", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address", "indexed": true}], "type": "event", "name": "Upgraded", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "result", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SPREAD_CONSTANT", "outputs": [{"internalType": "uint96", "name": "", "type": "uint96"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "uint256", "name": "baseDeposit", "type": "uint256"}, {"internalType": "uint256", "name": "quoteDeposit", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "payable", "type": "function", "name": "deposit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "address", "name": "token1_", "type": "address"}, {"internalType": "address", "name": "token2_", "type": "address"}, {"internalType": "address", "name": "_marginAccount", "type": "address"}, {"internalType": "address", "name": "_market", "type": "address"}, {"internalType": "uint96", "name": "_spreadConstant", "type": "uint96"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "marginAccount", "outputs": [{"internalType": "contract IMarginAccount", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "market", "outputs": [{"internalType": "contract IOrderBook", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "marketParams", "outputs": [{"internalType": "uint32", "name": "pricePrecision", "type": "uint32"}, {"internalType": "uint96", "name": "sizePrecision", "type": "uint96"}, {"internalType": "address", "name": "baseAssetAddress", "type": "address"}, {"internalType": "uint256", "name": "baseAssetDecimals", "type": "uint256"}, {"internalType": "address", "name": "quote<PERSON>set<PERSON>dd<PERSON>", "type": "address"}, {"internalType": "uint256", "name": "quoteAssetDecimals", "type": "uint256"}, {"internalType": "uint32", "name": "tickSize", "type": "uint32"}, {"internalType": "uint96", "name": "minSize", "type": "uint96"}, {"internalType": "uint96", "name": "maxSize", "type": "uint96"}, {"internalType": "uint256", "name": "takerFeeBps", "type": "uint256"}, {"internalType": "uint256", "name": "makerFeeBps", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "payable", "type": "function", "name": "mint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "nonces", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "permit"}, {"inputs": [{"internalType": "uint256", "name": "asset1", "type": "uint256"}, {"internalType": "uint256", "name": "asset2", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "previewDeposit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "previewMint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "previewWithdraw", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "setMarketParams"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "token1", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "token2", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "_newOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "upgradeToAndCall"}, {"inputs": [{"internalType": "uint256", "name": "_shares", "type": "uint256"}, {"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "address", "name": "_owner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "payable", "type": "receive"}], "devdoc": {"kind": "dev", "methods": {"DOMAIN_SEPARATOR()": {"details": "Returns the EIP-712 domain separator for the EIP-2612 permit."}, "allowance(address,address)": {"details": "Returns the amount of tokens that `spender` can spend on behalf of `owner`."}, "approve(address,uint256)": {"details": "Sets `amount` as the allowance of `spender` over the caller's tokens. Emits a {Approval} event."}, "balanceOf(address)": {"details": "Returns the amount of tokens owned by `owner`."}, "decimals()": {"details": "Returns the decimals places of the token."}, "deposit(uint256,uint256,address)": {"details": "Deposits token1 and token2 into the vault and mints shares to the receiver."}, "mint(uint256,address)": {"details": "Mints shares to the receiver in exchange for depositing token1 and token2."}, "name()": {"details": "Returns the name of the token."}, "nonces(address)": {"details": "Returns the current nonce for `owner`. This value is used to compute the signature for EIP-2612 permit."}, "permit(address,address,uint256,uint256,uint8,bytes32,bytes32)": {"details": "Sets `value` as the allowance of `spender` over the tokens of `owner`, authorized by a signed approval by `owner`. Emits a {Approval} event."}, "previewDeposit(uint256,uint256)": {"details": "Previews the amount of shares to be minted for a given deposit of token1 and token2."}, "previewMint(uint256)": {"details": "Previews the amount of token1 and token2 to be received for a given amount of shares to be minted."}, "previewWithdraw(uint256)": {"details": "Previews the amount of token1 and token2 to be received for a given amount of shares to be burned."}, "proxiableUUID()": {"details": "Returns the storage slot used by the implementation, as specified in [ERC1822](https://eips.ethereum.org/EIPS/eip-1822). Note: The `notDelegated` modifier prevents accidental upgrades to an implementation that is a proxy contract."}, "setMarketParams()": {"details": "Fetches and stores the market params from the market contract."}, "symbol()": {"details": "Returns the symbol of the token."}, "totalAssets()": {"details": "Returns the total assets of the vault in token1 and token2."}, "totalSupply()": {"details": "Returns the amount of tokens in existence."}, "transfer(address,uint256)": {"details": "Transfer `amount` tokens from the caller to `to`. Requirements: - `from` must at least have `amount`. Emits a {Transfer} event."}, "transferFrom(address,address,uint256)": {"details": "Transfers `amount` tokens from `from` to `to`. Note: Does not update the allowance if it is the maximum uint256 value. Requirements: - `from` must at least have `amount`. - The caller must have at least `amount` of allowance to transfer the tokens of `from`. Emits a {Transfer} event."}, "upgradeToAndCall(address,bytes)": {"details": "Upgrades the proxy's implementation to `newImplementation`. Emits a {Upgraded} event. Note: Passing in empty `data` skips the delegatecall to `newImplementation`."}, "withdraw(uint256,address,address)": {"details": "Withdraws token1 and token2 from the vault by burning the specified amount of shares."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/KuruAMMVault.sol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/KuruAMMVault.sol": {"keccak256": "0x110d6ea2c23c31eafdcbc244727243902a6bd406bf4a509851614842a5157eb4", "urls": ["bzz-raw://ae1964d8b700b192b2483b9b841340f145fc84b3a13fb3001198c23f00e390e4", "dweb:/ipfs/QmZBNTQmH4ucyQZSSfyUJ7eMNFYiHuaNGUAzkGaQdxraF4"], "license": "BUSL-1.1"}, "contracts/interfaces/IKuruAMMVault.sol": {"keccak256": "0xf0460e901fd738f2046c075784aa3045a58342c2f8ed276df83cf5386be969c7", "urls": ["bzz-raw://54c3a5647cce41595226f4bbdc403bf6ef91177f3caa57de908e9ecaa2ac6460", "dweb:/ipfs/QmcWxssB8rFcX1QCZBqHzmmE3Dq2AHUFPfJvogzegQnMoS"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IMarginAccount.sol": {"keccak256": "0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2", "urls": ["bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4", "dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IOrderBook.sol": {"keccak256": "0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b", "urls": ["bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d", "dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68"], "license": "GPL-2.0-or-later"}, "contracts/libraries/Errors.sol": {"keccak256": "0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f", "urls": ["bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7", "dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu"], "license": "BUSL-1.1"}, "contracts/libraries/FixedPointMathLib.sol": {"keccak256": "0x90a5a4d76dd7cdbf8ee357bd0cb963ad5fb2f3d58444f213872f0e7e23ffbebd", "urls": ["bzz-raw://99f2d78bf91870c6815b4eec8aef48fec7e94de2dba2255f8fb8fe3cb9280946", "dweb:/ipfs/QmRjtG3C4hrXAc9hsyRpvzEZXhX3ivMktKB9hYgTaLAi4N"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol": {"keccak256": "0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3", "urls": ["bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e", "dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol": {"keccak256": "0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946", "urls": ["bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de", "dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22"], "license": "MIT"}, "node_modules/solady/src/tokens/ERC20.sol": {"keccak256": "0xb4a3f9ba8a05107f7370de42cff57f3ad26dafd438712c11531a5892de2f59e0", "urls": ["bzz-raw://f0a9ca06e3cf6dea1f9a4c5599581573b7d81cd64dc3afb582f325ccf5fdd6dc", "dweb:/ipfs/Qmb9r5dDceNF4W8S5u6i85RsNTgE5XG9HbTXkyS25ad3C6"], "license": "MIT"}, "node_modules/solady/src/utils/Initializable.sol": {"keccak256": "0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec", "urls": ["bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559", "dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy"], "license": "MIT"}, "node_modules/solady/src/utils/SafeTransferLib.sol": {"keccak256": "0x583f47701d9b47bb3ef80fcabbbd62fbb58a01733b7a57e19658b4b02468883a", "urls": ["bzz-raw://2523bfac005e21ef9963fdb3c08b2c61824e2b5ce2f53d1a1828b01ed995217c", "dweb:/ipfs/QmbBjVG9tZyeZSQH4m5GUzNBwo2iuvLoZYbmhT4gxnJc4J"], "license": "MIT"}, "node_modules/solady/src/utils/UUPSUpgradeable.sol": {"keccak256": "0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e", "urls": ["bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16", "dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV"], "license": "MIT"}}, "version": 1}, "id": 1}