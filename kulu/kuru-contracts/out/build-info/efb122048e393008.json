{"id": "efb122048e393008", "source_id_to_path": {"0": "contracts/KuruForwarder.sol", "1": "contracts/interfaces/IOrderBook.sol", "2": "contracts/libraries/Errors.sol", "3": "lib/forge-std/src/Base.sol", "4": "lib/forge-std/src/StdAssertions.sol", "5": "lib/forge-std/src/StdChains.sol", "6": "lib/forge-std/src/StdCheats.sol", "7": "lib/forge-std/src/StdConstants.sol", "8": "lib/forge-std/src/StdError.sol", "9": "lib/forge-std/src/StdInvariant.sol", "10": "lib/forge-std/src/StdJson.sol", "11": "lib/forge-std/src/StdMath.sol", "12": "lib/forge-std/src/StdStorage.sol", "13": "lib/forge-std/src/StdStyle.sol", "14": "lib/forge-std/src/StdToml.sol", "15": "lib/forge-std/src/StdUtils.sol", "16": "lib/forge-std/src/Test.sol", "17": "lib/forge-std/src/Vm.sol", "18": "lib/forge-std/src/console.sol", "19": "lib/forge-std/src/console2.sol", "20": "lib/forge-std/src/interfaces/IMulticall3.sol", "21": "lib/forge-std/src/safeconsole.sol", "22": "lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol", "23": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "24": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol", "25": "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "26": "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "27": "lib/openzeppelin-contracts/contracts/utils/Address.sol", "28": "lib/openzeppelin-contracts/contracts/utils/Errors.sol", "29": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "30": "node_modules/solady/src/auth/Ownable.sol", "31": "node_modules/solady/src/utils/ECDSA.sol", "32": "node_modules/solady/src/utils/EIP712.sol", "33": "node_modules/solady/src/utils/Initializable.sol", "34": "node_modules/solady/src/utils/UUPSUpgradeable.sol", "35": "test/ETHStrandingBugPOC.t.sol"}, "language": "Solidity"}