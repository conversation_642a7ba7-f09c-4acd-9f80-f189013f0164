{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "SPREAD_CONSTANT", "inputs": [], "outputs": [{"name": "", "type": "uint96", "internalType": "uint96"}], "stateMutability": "view"}, {"type": "function", "name": "addBuyOrder", "inputs": [{"name": "_price", "type": "uint32", "internalType": "uint32"}, {"name": "size", "type": "uint96", "internalType": "uint96"}, {"name": "_postOnly", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addFlipBuyOrder", "inputs": [{"name": "_price", "type": "uint32", "internalType": "uint32"}, {"name": "_flippedPrice", "type": "uint32", "internalType": "uint32"}, {"name": "_size", "type": "uint96", "internalType": "uint96"}, {"name": "_provisionOrRevert", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addFlipSellOrder", "inputs": [{"name": "_price", "type": "uint32", "internalType": "uint32"}, {"name": "_flippedPrice", "type": "uint32", "internalType": "uint32"}, {"name": "_size", "type": "uint96", "internalType": "uint96"}, {"name": "_provisionOrRevert", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addPairedLiquidity", "inputs": [{"name": "_bidPrice", "type": "uint32", "internalType": "uint32"}, {"name": "_askPrice", "type": "uint32", "internalType": "uint32"}, {"name": "_bidSize", "type": "uint96", "internalType": "uint96"}, {"name": "_askSize", "type": "uint96", "internalType": "uint96"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addSellOrder", "inputs": [{"name": "_price", "type": "uint32", "internalType": "uint32"}, {"name": "_size", "type": "uint96", "internalType": "uint96"}, {"name": "_postOnly", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "batchAddPairedLiquidity", "inputs": [{"name": "bidPrices", "type": "uint32[]", "internalType": "uint32[]"}, {"name": "askPrices", "type": "uint32[]", "internalType": "uint32[]"}, {"name": "bidSizes", "type": "uint96[]", "internalType": "uint96[]"}, {"name": "askSizes", "type": "uint96[]", "internalType": "uint96[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "batchCancelFlipOrders", "inputs": [{"name": "_orderIds", "type": "uint40[]", "internalType": "uint40[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "batchCancelOrders", "inputs": [{"name": "_orderIds", "type": "uint40[]", "internalType": "uint40[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "batchCancelOrdersNoRevert", "inputs": [{"name": "_orderIds", "type": "uint40[]", "internalType": "uint40[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "batchProvisionLiquidity", "inputs": [{"name": "prices", "type": "uint32[]", "internalType": "uint32[]"}, {"name": "flipPrices", "type": "uint32[]", "internalType": "uint32[]"}, {"name": "sizes", "type": "uint96[]", "internalType": "uint96[]"}, {"name": "isBuy", "type": "bool[]", "internalType": "bool[]"}, {"name": "_provisionOrRevert", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "batchUpdate", "inputs": [{"name": "buyPrices", "type": "uint32[]", "internalType": "uint32[]"}, {"name": "buySizes", "type": "uint96[]", "internalType": "uint96[]"}, {"name": "sellPrices", "type": "uint32[]", "internalType": "uint32[]"}, {"name": "sellSizes", "type": "uint96[]", "internalType": "uint96[]"}, {"name": "orderIdsToCancel", "type": "uint40[]", "internalType": "uint40[]"}, {"name": "postOnly", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "bestBidAsk", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "collectFees", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getL2Book", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "getL2Book", "inputs": [{"name": "_bidPricePoints", "type": "uint32", "internalType": "uint32"}, {"name": "_askPricePoints", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "getMarketParams", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}, {"name": "", "type": "uint96", "internalType": "uint96"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint32", "internalType": "uint32"}, {"name": "", "type": "uint96", "internalType": "uint96"}, {"name": "", "type": "uint96", "internalType": "uint96"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getVaultParams", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint96", "internalType": "uint96"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint96", "internalType": "uint96"}, {"name": "", "type": "uint96", "internalType": "uint96"}, {"name": "", "type": "uint96", "internalType": "uint96"}, {"name": "", "type": "uint96", "internalType": "uint96"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_owner", "type": "address", "internalType": "address"}, {"name": "_type", "type": "uint8", "internalType": "enum IOrderBook.OrderBookType"}, {"name": "_baseAssetAddress", "type": "address", "internalType": "address"}, {"name": "_baseAssetDecimals", "type": "uint256", "internalType": "uint256"}, {"name": "_quote<PERSON><PERSON><PERSON>dd<PERSON>", "type": "address", "internalType": "address"}, {"name": "_quoteAssetDecimals", "type": "uint256", "internalType": "uint256"}, {"name": "_marginAccountAddress", "type": "address", "internalType": "address"}, {"name": "_sizePrecision", "type": "uint96", "internalType": "uint96"}, {"name": "_pricePrecision", "type": "uint32", "internalType": "uint32"}, {"name": "_tickSize", "type": "uint32", "internalType": "uint32"}, {"name": "_minSize", "type": "uint96", "internalType": "uint96"}, {"name": "_maxSize", "type": "uint96", "internalType": "uint96"}, {"name": "_takerFeeBps", "type": "uint256", "internalType": "uint256"}, {"name": "_makerFeeBps", "type": "uint256", "internalType": "uint256"}, {"name": "_kuruAmmVault", "type": "address", "internalType": "address"}, {"name": "_kuruAmmSpread", "type": "uint96", "internalType": "uint96"}, {"name": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isTrusted<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "forwarder", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "kuruAmmVault", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "marketState", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "enum IOrderBook.MarketState"}], "stateMutability": "view"}, {"type": "function", "name": "placeAndExecuteMarketBuy", "inputs": [{"name": "_quoteSize", "type": "uint96", "internalType": "uint96"}, {"name": "_minAmountOut", "type": "uint256", "internalType": "uint256"}, {"name": "_isMargin", "type": "bool", "internalType": "bool"}, {"name": "_isFillOrKill", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "placeAndExecuteMarketSell", "inputs": [{"name": "_size", "type": "uint96", "internalType": "uint96"}, {"name": "_minAmountOut", "type": "uint256", "internalType": "uint256"}, {"name": "_isMargin", "type": "bool", "internalType": "bool"}, {"name": "_isFillOrKill", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "s_buyPricePoints", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "head", "type": "uint40", "internalType": "uint40"}, {"name": "tail", "type": "uint40", "internalType": "uint40"}], "stateMutability": "view"}, {"type": "function", "name": "s_buyTree", "inputs": [], "outputs": [{"name": "level0", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "s_orderIdCounter", "inputs": [], "outputs": [{"name": "", "type": "uint40", "internalType": "uint40"}], "stateMutability": "view"}, {"type": "function", "name": "s_orders", "inputs": [{"name": "", "type": "uint40", "internalType": "uint40"}], "outputs": [{"name": "owner<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "size", "type": "uint96", "internalType": "uint96"}, {"name": "prev", "type": "uint40", "internalType": "uint40"}, {"name": "next", "type": "uint40", "internalType": "uint40"}, {"name": "flippedId", "type": "uint40", "internalType": "uint40"}, {"name": "price", "type": "uint32", "internalType": "uint32"}, {"name": "flippedPrice", "type": "uint32", "internalType": "uint32"}, {"name": "isBuy", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "s_sellPricePoints", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "head", "type": "uint40", "internalType": "uint40"}, {"name": "tail", "type": "uint40", "internalType": "uint40"}], "stateMutability": "view"}, {"type": "function", "name": "s_sellTree", "inputs": [], "outputs": [{"name": "level0", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "toggleMarket", "inputs": [{"name": "_state", "type": "uint8", "internalType": "enum IOrderBook.MarketState"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "_newOwner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateVaultOrdSz", "inputs": [{"name": "_vaultAskOrderSize", "type": "uint96", "internalType": "uint96"}, {"name": "_vaultBidOrderSize", "type": "uint96", "internalType": "uint96"}, {"name": "_askPrice", "type": "uint256", "internalType": "uint256"}, {"name": "_bidPrice", "type": "uint256", "internalType": "uint256"}, {"name": "_nullifyPartialFills", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "vaultAskOrderSize", "inputs": [], "outputs": [{"name": "", "type": "uint96", "internalType": "uint96"}], "stateMutability": "view"}, {"type": "function", "name": "vaultBestAsk", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "event", "name": "F<PERSON><PERSON><PERSON><PERSON>Created", "inputs": [{"name": "orderId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "flippedId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "owner", "type": "address", "indexed": false, "internalType": "address"}, {"name": "size", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "price", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "flippedPrice", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "isBuy", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "FlipOrderUpdated", "inputs": [{"name": "orderId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "size", "type": "uint96", "indexed": false, "internalType": "uint96"}], "anonymous": false}, {"type": "event", "name": "FlipOrdersCanceled", "inputs": [{"name": "orderIds", "type": "uint40[]", "indexed": false, "internalType": "uint40[]"}, {"name": "owner", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Flipped<PERSON><PERSON><PERSON>Created", "inputs": [{"name": "orderId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "flippedId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "owner", "type": "address", "indexed": false, "internalType": "address"}, {"name": "size", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "price", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "flippedPrice", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "isBuy", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "MarketStateUpdated", "inputs": [{"name": "previousState", "type": "uint8", "indexed": false, "internalType": "enum IOrderBook.MarketState"}, {"name": "newState", "type": "uint8", "indexed": false, "internalType": "enum IOrderBook.MarketState"}], "anonymous": false}, {"type": "event", "name": "OrderCanceled", "inputs": [{"name": "orderId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "owner", "type": "address", "indexed": false, "internalType": "address"}, {"name": "price", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "size", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "isBuy", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "OrderCreated", "inputs": [{"name": "orderId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "owner", "type": "address", "indexed": false, "internalType": "address"}, {"name": "size", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "price", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "isBuy", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "OrdersCanceled", "inputs": [{"name": "orderId", "type": "uint40[]", "indexed": false, "internalType": "uint40[]"}, {"name": "owner", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Trade", "inputs": [{"name": "orderId", "type": "uint40", "indexed": false, "internalType": "uint40"}, {"name": "makerAddress", "type": "address", "indexed": false, "internalType": "address"}, {"name": "isBuy", "type": "bool", "indexed": false, "internalType": "bool"}, {"name": "price", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "updatedSize", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "taker<PERSON><PERSON><PERSON>", "type": "address", "indexed": false, "internalType": "address"}, {"name": "txOrigin", "type": "address", "indexed": false, "internalType": "address"}, {"name": "filledSize", "type": "uint96", "indexed": false, "internalType": "uint96"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "VaultParamsUpdated", "inputs": [{"name": "_vaultAskOrderSize", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "_vaultAskPartiallyFilledSize", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "_vaultBidOrderSize", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "_vaultBidPartiallyFilledSize", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "_askPrice", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "_bidPrice", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "InsufficientLiquidity", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidSpread", "inputs": []}, {"type": "error", "name": "LengthMismatch", "inputs": []}, {"type": "error", "name": "MarketFeeError", "inputs": []}, {"type": "error", "name": "MarketSizeError", "inputs": []}, {"type": "error", "name": "MarketStateError", "inputs": []}, {"type": "error", "name": "NativeAssetInsufficient", "inputs": []}, {"type": "error", "name": "NativeAssetNotRequired", "inputs": []}, {"type": "error", "name": "NativeAssetSurplus", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OnlyOwnerAllowedError", "inputs": []}, {"type": "error", "name": "Only<PERSON>aultAllowed", "inputs": []}, {"type": "error", "name": "OrderAlreadyFilledOrCancelled", "inputs": []}, {"type": "error", "name": "Post<PERSON>nly<PERSON><PERSON>r", "inputs": []}, {"type": "error", "name": "PriceError", "inputs": []}, {"type": "error", "name": "ProvisionError", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "SizeError", "inputs": []}, {"type": "error", "name": "SlippageExceeded", "inputs": []}, {"type": "error", "name": "TickSizeError", "inputs": []}, {"type": "error", "name": "Uint32Overflow", "inputs": []}, {"type": "error", "name": "Uint96Overflow", "inputs": []}, {"type": "error", "name": "Unauthorized", "inputs": []}, {"type": "error", "name": "UnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UpgradeFailed", "inputs": []}, {"type": "error", "name": "WrongOrderTypeCancel", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "834:63047:4:-:0;;;;;;;1525:4:64;1501:31;;-1:-1:-1;;6669:609:62;;;;;;2001:66;6669:609;-1:-1:-1;;;;;;6669:609:62;;;-1:-1:-1;834:63047:4;;;;;;1501:31:64;834:63047:4;;;;;;;;;;;6669:609:62;-1:-1:-1;;;;;;;6669:609:62;-1:-1:-1;;;;;6669:609:62;;;;;;;;;;;;-1:-1:-1;6669:609:62;;;;834:63047:4;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "834:63047:4:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;;:::i;:::-;5388:112;;:::i;:::-;5476:17;834:63047;;-1:-1:-1;;;;;;834:63047:4;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;;:::i;:::-;;;:::i;:::-;;60284:248;;;;;;;834:63047;60284:248;;;;834:63047;60284:248;60619:52;;:::i;:::-;60654:16;834:63047;;60654:16;60688:25;;;:39;;;60681:1211;60688:63;;;60681:1211;60688:63;;;791:8:0;;60784:16:4;60284:248;791:8:0;834:63047:4;;791:8:0;;834:63047:4;;;60826:15;60855:134;;834:63047;60862:12;;;;;60894:30;834:63047;;;;60902:8;60284:248;834:63047;;;;;;;60894:30;;:::i;:::-;834:63047;;;60902:8;60284:248;834:63047;;;60952:22;834:63047;;;60952:22;834:63047;;;;60855:134;;60862:12;;;;;;;;;834:63047;61002:786;;;;;;60855:134;61002:786;834:63047;61002:786;61809:41;61002:786;;61864:17;61002:786;;60284:248;61002:786;;;;;;834:63047;61002:786;61809:41;:::i;:::-;61864:17;;:::i;:::-;60681:1211;;;;61002:786;;;;;;;;;;;;;;-1:-1:-1;;61002:786:4;;;834:63047;61002:786;;;;;;;;;;;60284:248;61002:786;;;60688:63;;;;834:63047;61902:591;;;;;;60681:1211;61902:591;;;60284:248;61902:591;;;;834:63047;61902:591;62511:37;;:::i;:::-;62558:1174;;60654:16;834:63047;;60654:16;62565:25;;;:39;;;62558:1174;62565:63;;;62558:1174;62565:63;;;791:8:0;;62661:17:4;60284:248;791:8:0;834:63047:4;;791:8:0;;834:63047:4;;62704:15;834:63047;62704:15;62733:134;;834:63047;62740:12;;;;;62772:30;834:63047;;;;60902:8;60284:248;834:63047;;;;;;;62772:30;;:::i;:::-;834:63047;;;60902:8;60284:248;834:63047;;;60952:22;834:63047;;;62830:22;834:63047;;;;62733:134;;62740:12;;;;;;834:63047;62880:748;;;;;;62733:134;62880:748;834:63047;62880:748;63649:41;62880:748;;63704:17;62880:748;;60284:248;62880:748;;;;;;834:63047;62880:748;63649:41;:::i;:::-;63704:17;;:::i;:::-;62558:1174;;62880:748;;;;;;;;;;;;;;-1:-1:-1;;62880:748:4;;;834:63047;62880:748;;;;;;;;;;;60284:248;62880:748;;;62565:63;63742:131;;;-1:-1:-1;;63742:131:4;;;834:63047;;;;62565:63;834:63047;;:::i;:::-;;;;62565:63;834:63047;60654:16;834:63047;;62608:20;;62565:63;;:39;62594:10;;;;62565:39;;61902:591;;;;;;;;;;;;;-1:-1:-1;;61902:591:4;;60284:248;61902:591;;;;;;;;;;;60284:248;61902:591;;;60688:63;834:63047;60654:16;834:63047;;60731:20;;60688:63;;:39;60717:10;;;;60688:39;;834:63047;;;;;;;-1:-1:-1;;834:63047:4;;;;;;;;;;;1092:70;834:63047;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;;;;;;:::i;:::-;27956:34;;;;834:63047;;28043:28;;;834:63047;;28124:29;;;834:63047;;28203:13;28218:17;;;;;;834:63047;;;28237:3;28260:8;;;;;:::i;:::-;834:63047;;;;;;;;28260:8;;;28304:9;;;;;;:::i;:::-;;:::i;:::-;28315:13;;;;;;:::i;:::-;28330:8;;;;;;:::i;:::-;;:::i;:::-;2081:11;834:63047;;;;;;;;;;;:::i;:::-;;;;1181:103:49;;1246:1;1181:103;;;;;:::i;:::-;1246:1;:::i;:::-;3556:68:51;-1:-1:-1;;;;;;;;;;;3556:68:51;28256:229:4;834:63047;28203:13;;834:63047;-1:-1:-1;;;834:63047:4;;;;;28256:229;28415:9;;;;;;:::i;:::-;28426:13;;;;;;:::i;:::-;28441:8;;;;;;:::i;:::-;2081:11;834:63047;;;;;;;;;;;:::i;:::-;;;;1181:103:49;;1246:1;1181:103;;;;;:::i;:::-;1246:1;:::i;:::-;3556:68:51;-1:-1:-1;;;;;;;;;;;3556:68:51;28256:229:4;;834:63047;;;;;-1:-1:-1;;;;;;834:63047:4;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;36754:16;834:63047;;36809:17;834:63047;;;;;;;;;;36897:13;834:63047;36921:9;834:63047;36951:10;834:63047;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;36897:85;;;;;834:63047;36897:85;834:63047;;;;;;;;;;;;;36897:85;;834:63047;36897:85;;834:63047;;;;;36921:9;834:63047;;;;;;;36897:85;;;;;;;;834:63047;;;36897:85;;;;834:63047;36897:85;;:::i;:::-;834:63047;36897:85;834:63047;;;;;;;;;36897:85;834:63047;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;;:::i;:::-;;;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;-1:-1:-1;;3207:622:62;;834:63047:4;3207:622:62;;2001:66;;3207:622;;;;834:63047:4;;3448:14;834:63047;;;;;;3481:28;;:61;;;834:63047;891:5:0;;;-1:-1:-1;;;;;3612:2:4;834:63047;;;891:5:0;834:63047:4;3595:24;:46;;;834:63047;3595:70;;;834:63047;891:5:0;;;-1:-1:-1;;;;;834:63047:4;;3717:12;;:35;;;834:63047;891:5:0;;;834:63047:4;;;;;;:::i;:::-;-1:-1:-1;;;;;;891:5:0;834:63047:4;;;;;-1:-1:-1;;;;;834:63047:4;891:5:0;834:63047:4;;891:5:0;;;3448:14:4;891:5:0;834:63047:4;891:5:0;;-1:-1:-1;;;;;;891:5:0;-1:-1:-1;;;;;834:63047:4;;;;891:5:0;;;;;;3868:31:4;891:5:0;834:63047:4;;;;;;891:5:0;;834:63047:4;3982:24;;;:::i;:::-;3958:48;834:63047;;;4016:40;834:63047;4091:25;834:63047;;4091:25;:::i;:::-;4066:50;834:63047;;;;;;;-1:-1:-1;;;;;891:5:0;;4127:62:4;891:5:0;;;4127:62:4;891:5:0;;4241:30:4;891:5:0;;834:63047:4;891:5:0;;;;;;;3448:14:4;891:5:0;;;;;;;;4241:30:4;891:5:0;834:63047:4;;;;;;;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;891:5:0;;;3868:31:4;891:5:0;-1:-1:-1;;;;;4353:18:4;834:63047;;;;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;4353:18;834:63047;;;4409:26;834:63047;;;4445:26;834:63047;;;;;;;-1:-1:-1;;;;;891:5:0;;834:63047:4;891:5:0;;;834:63047:4;891:5:0;834:63047:4;;;-1:-1:-1;;;;;834:63047:4;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;13981:17:0;;4561:32:4;834:63047;;;;;;;-1:-1:-1;;;;;891:5:0;;4603:38:4;891:5:0;;;4603:38:4;891:5:0;3892:296:62;;834:63047:4;3892:296:62;4561:32:4;3892:296:62;;834:63047:4;;3892:296:62;;834:63047:4;3892:296:62;;834:63047:4;891:5:0;;;;834:63047:4;891:5:0;834:63047:4;;891:5:0;3717:35:4;834:63047;-1:-1:-1;;;;;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;3733:19;3717:35;;891:5:0;;;;834:63047:4;891:5:0;834:63047:4;;891:5:0;3595:70:4;834:63047;3662:3;-1:-1:-1;;;;;834:63047:4;;3645:20;3595:70;;:46;834:63047;-1:-1:-1;;;;;834:63047:4;;3623:18;;3595:46;;891:5:0;;;;834:63047:4;891:5:0;834:63047:4;;891:5:0;3481:61:4;834:63047;891:5:0;834:63047:4;;3513:29;3481:61;;3207:622:62;834:63047:4;3207:622:62;;;;;;;;;;;;;;;;;;;;;;834:63047:4;3207:622:62;834:63047:4;3207:622:62;;834:63047:4;;;;;;-1:-1:-1;;834:63047:4;;;;;1245:36;834:63047;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;2248:23;834:63047;2233:11;834:63047;;;;;;;:::i;:::-;2233:38;834:63047;;;19251:20;;;;;;19378:12;19348:43;19378:12;19348:43;19378:12;;:::i;:::-;834:63047;;19348:43;;;;;:::i;:::-;;;;834:63047;19273:3;19309:12;;;;;;:::i;:::-;;:::i;:::-;1181:103:49;;;:::i;:::-;834:63047:4;;;;;20853:8;834:63047;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;20890:43;;;;:::i;:::-;834:63047;;21001:12;;:::i;:::-;834:63047;;-1:-1:-1;;;;;834:63047:4;;;;;21001:35;834:63047;;;;;21096:24;834:63047;;;;;21336:6;834:63047;;;21175:40;21171:131;;19273:3;21336:6;;:::i;:::-;834:63047;-1:-1:-1;;;;;;;;;;;3556:68:51;834:63047:4;19236:13;;21171:131;834:63047;21231:60;834:63047;;;20853:8;834:63047;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;21231:60;:::i;:::-;21171:131;;;834:63047;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;57240:9;;:::i;:::-;57282;;;;:::i;:::-;834:63047;;;;;;;;;-1:-1:-1;834:63047:4;;;;;;;-1:-1:-1;;834:63047:4;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;;:::i;:::-;;;:::i;:::-;;;:::i;:::-;;;2081:11;834:63047;;;;;;;:::i;:::-;;;1246:1:49;1181:103;;;:::i;:::-;1246:1;:::i;:::-;834:63047:4;-1:-1:-1;;;;;;;;;;;3556:68:51;834:63047:4;;;;;;;-1:-1:-1;;834:63047:4;;;;;56775:14;791:8:0;834:63047:4;;;;;56830:9;834:63047;;;;-1:-1:-1;;;;;56884:10:4;834:63047;;56908:18;834:63047;;56962:7;834:63047;;57004:11;834:63047;;57029:11;834:63047;;;;;791:8:0;;;;;834:63047:4;;;791:8:0;;834:63047:4;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;12743:12:0;834:63047:4;-1:-1:-1;;;;;834:63047:4;;;;12831:12:0;834:63047:4;12857:22:0;834:63047:4;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;2081:11;834:63047;;;;;;;;;;:::i;:::-;;;;1181:103:49;31091:2256:4;1181:103:49;;;:::i;:::-;31091:2256:4;:::i;:::-;834:63047;-1:-1:-1;;;;;;;;;;;3556:68:51;834:63047:4;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;;;;;;;;5006:241;;:::i;:::-;5108:11;834:63047;;;;;;;;;:::i;:::-;;;;:::i;:::-;5098:21;;834:63047;;;;;;;5201:39;834:63047;;:::i;:::-;;;;;;;;;;;;5108:11;834:63047;;;;;;;;;;;:::i;:::-;;;;;;:::i;:::-;;;;;5201:39;834:63047;;;;;;;;:::i;:::-;;;2081:11;834:63047;;;;;;;:::i;:::-;;;1246:1:49;1181:103;;;:::i;834:63047:4:-;;;;;;-1:-1:-1;;834:63047:4;;;;;;;:::i;:::-;2453:17;834:63047;;;-1:-1:-1;;;;;834:63047:4;;;;;;;2440:30;834:63047;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;2248:23;834:63047;2233:11;834:63047;;;;;;;:::i;:::-;2233:38;834:63047;;29354:35;;;834:63047;;29513:37;;;834:63047;;29631:37;;;:::i;:::-;29717:13;;834:63047;29732:27;;;;;;30068:13;;;;834:63047;30083:20;;;;;;30236:13;;;;;834:63047;30251:21;;;;;;834:63047;;;30414:109;;834:63047;30414:109;30466:46;30499:12;;;:::i;:::-;834:63047;30466:46;834:63047;;30466:46;;;;;:::i;30274:3::-;30306:13;;;;;;:::i;:::-;30321:12;;;;;;;:::i;:::-;834:63047;2233:11;834:63047;;;;;;;:::i;:::-;;;1181:103:49;1246:1;1181:103;834:63047:4;1181:103:49;;;:::i;:::-;1246:1;:::i;:::-;834:63047:4;-1:-1:-1;;;;;;;;;;;3556:68:51;834:63047:4;30236:13;;30105:3;30136:12;;;;;;:::i;:::-;30150:11;;;;;;;:::i;:::-;834:63047;2233:11;834:63047;;;;;;;:::i;:::-;;;1181:103:49;1246:1;1181:103;834:63047:4;1181:103:49;;;:::i;1246:1::-;834:63047:4;-1:-1:-1;;;;;;;;;;;3556:68:51;834:63047:4;30068:13;;29761:3;29812:19;834:63047;29812:19;21485:639;29812:19;;;;;;:::i;:::-;1181:103:49;;:::i;:::-;21485:639:4;:::i;:::-;834:63047;-1:-1:-1;;;;;;;;;;;3556:68:51;29799:40:4;;;29910:19;29888:41;29910:19;;;834:63047;29910:19;;;;:::i;:::-;29888:41;;:::i;:::-;834:63047;;;;29853:153;834:63047;29717:13;;;;29853:153;834:63047;29968:23;;;;:::i;:::-;834:63047;29853:153;;834:63047;;;;;;;;;;;;;:::i;:::-;;;2081:11;834:63047;;;;;;;;;;:::i;:::-;;;;1181:103:49;33891:1943:4;1181:103:49;;;:::i;:::-;33891:1943:4;:::i;834:63047::-;;;;;;-1:-1:-1;;834:63047:4;;;;6466:184:64;6407:6;6466:184;;;834:63047:4;;;2619:66:64;834:63047:4;;;6466:184:64;;834:63047:4;6466:184:64;834:63047:4;6466:184:64;;834:63047:4;;;;;;-1:-1:-1;;834:63047:4;;;;;-1:-1:-1;;;;;1091:31:0;834:63047:4;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;5771:446:64;5712:6;5771:446;;;4849:89:4;;:::i;:::-;834:63047;;;;;4259:1327:64;;;;;834:63047:4;4259:1327:64;834:63047:4;4259:1327:64;;;;;;;;;;834:63047:4;4259:1327:64;;;;;;;;834:63047:4;4259:1327:64;;834:63047:4;4259:1327:64;834:63047:4;;4259:1327:64;834:63047:4;;4259:1327:64;;;;;;;;;;834:63047:4;4259:1327:64;;834:63047:4;4259:1327:64;;;;;;;;;834:63047:4;4259:1327:64;;834:63047:4;;;;;;;:::i;:::-;;;2081:11;834:63047;;;;;;;:::i;:::-;;;1246:1:49;1181:103;;;:::i;834:63047:4:-;;;;;;-1:-1:-1;;834:63047:4;;;;;1014:27:0;834:63047:4;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;59768:16;60113:3766;834:63047;60284:248;;;;;;;834:63047;60284:248;;;;834:63047;60284:248;60619:52;;:::i;:::-;59768:16;834:63047;;59768:16;60688:25;;;:39;;;60681:1211;60688:63;;;60681:1211;60688:63;;;834:63047;791:8:0;60784:16:4;60284:248;791:8:0;834:63047:4;;;791:8:0;834:63047:4;;;;60855:134;;834:63047;60862:12;;;;;60894:30;834:63047;;;;60902:8;60284:248;834:63047;;;;;;;60894:30;;:::i;:::-;834:63047;;;60902:8;60284:248;834:63047;;;60952:22;834:63047;;;60952:22;834:63047;;;;60855:134;;60862:12;;;;;;;;;834:63047;61002:786;;;;;;60855:134;61002:786;834:63047;61002:786;61809:41;61002:786;;61864:17;61002:786;;60284:248;61002:786;;;;;;834:63047;61002:786;61809:41;:::i;61864:17::-;60681:1211;;;;61002:786;;;;;;;834:63047;61002:786;;;;;;-1:-1:-1;;61002:786:4;;;834:63047;61002:786;;;;;;;;;;;60284:248;61002:786;;;60688:63;;;;;834:63047;61902:591;;;;;;60681:1211;61902:591;;834:63047;60284:248;61902:591;;;;;834:63047;61902:591;62511:37;;:::i;:::-;62558:1174;;59768:16;834:63047;;59768:16;62565:25;;;:39;;;62558:1174;62565:63;;;62558:1174;62565:63;;;834:63047;791:8:0;62661:17:4;60284:248;791:8:0;834:63047:4;;;791:8:0;834:63047:4;;62704:15;834:63047;;62733:134;;834:63047;62740:12;;;;;62772:30;834:63047;;;;60902:8;60284:248;834:63047;;;;;;;62772:30;;:::i;:::-;834:63047;;;60902:8;60284:248;834:63047;;;60952:22;834:63047;;;62830:22;834:63047;;;;62733:134;;62740:12;;;;;;834:63047;62880:748;;;;;;62733:134;62880:748;834:63047;62880:748;63649:41;62880:748;;63704:17;62880:748;;60284:248;62880:748;;;;;;834:63047;62880:748;63649:41;:::i;:::-;63704:17;;:::i;:::-;62558:1174;;62880:748;;;;;;;834:63047;62880:748;;;;;;-1:-1:-1;;62880:748:4;;;834:63047;62880:748;;;;;;;;;;;60284:248;62880:748;;;62565:63;63742:131;;;-1:-1:-1;;63742:131:4;;;834:63047;;;;62565:63;834:63047;;:::i;62565:63::-;834:63047;59768:16;834:63047;;62608:20;;62565:63;;:39;62594:10;;;;62565:39;;61902:591;;;;;;834:63047;61902:591;;;;;;-1:-1:-1;;61902:591:4;;60284:248;61902:591;;;;;;;;;;;60284:248;61902:591;;;60688:63;834:63047;59768:16;834:63047;;60731:20;;60688:63;;:39;60717:10;;;;60688:39;;834:63047;;;;;;-1:-1:-1;;834:63047:4;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;;2248:23;834:63047;2233:11;834:63047;;;;;;;:::i;:::-;2233:38;834:63047;;20217:30;;;:::i;:::-;20262:13;834:63047;20277:20;;;;;;20557:47;20591:12;;;:::i;20299:3::-;20350:12;21485:639;20350:12;;834:63047;20350:12;;;;:::i;21485:639::-;834:63047;-1:-1:-1;;;;;;;;;;;3556:68:51;20337:33:4;;;20442:12;;;;;;:::i;:::-;834:63047;20419:35;;;;:::i;:::-;834:63047;;;;20384:148;834:63047;20262:13;;20384:148;834:63047;20493:24;;;;:::i;:::-;834:63047;20384:148;;834:63047;;;;;;-1:-1:-1;;834:63047:4;;;;;;:::i;:::-;;;:::i;:::-;;;:::i;:::-;;;2081:11;834:63047;;;;;;;:::i;:::-;;;1246:1:49;1181:103;;;:::i;834:63047:4:-;;;;;;-1:-1:-1;;834:63047:4;;;;;;:::i;:::-;;;:::i;:::-;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;2081:11;834:63047;;;;;;;;;;:::i;:::-;;;1181:103:49;;:::i;:::-;834:63047:4;;;12984:13;;;;;;:38;;;834:63047;12984:70;;;834:63047;;;;13115:8;834:63047;;;;;;;;13103:20;;;;:::i;:::-;834:63047;13103:25;:54;;;;834:63047;;;;;13220:7;834:63047;;-1:-1:-1;;;;;834:63047:4;;13209:18;;;;:40;;;;834:63047;;;;;13297:18;;:40;;;;834:63047;;;;;13414:9;;:::i;:::-;13460;;;;;:::i;:::-;791:8:0;;;;;;;;;;;;;;;;13510:57:4;834:63047;791:8:0;;;;834:63047:4;13510:57;;;:::i;:::-;13509:70;13508:91;;;;;834:63047;791:8:0;;;;834:63047:4;;;791:8:0;;;;;;;;;;;;;;;13704:57:4;;;;;:::i;:::-;13703:70;13702:127;;;;;834:63047;791:8:0;;;;14063:12:4;14170;13965:42;:84;14754:15;13965:42;:67;:42;;;;:::i;:::-;-1:-1:-1;;;;;14010:22:4;834:63047;;;13965:67;:::i;:::-;:84;:::i;:::-;14063:12;;:::i;:::-;834:63047;-1:-1:-1;;;;;834:63047:4;14063:12;:::i;:::-;14120:48;:32;834:63047;;;;;;;;;14131:21;834:63047;14120:32;;:::i;:::-;-1:-1:-1;;;;;2081:11:4;791:8:0;;;834:63047:4;14120:48;;:::i;:::-;14170:12;;:::i;:::-;;;:::i;:::-;14635:15;2081:11;834:63047;;14215:20;834:63047;;;14215:20;:::i;:::-;14266:15;;;;:::i;:::-;834:63047;;;;791:8:0;834:63047:4;791:8:0;;;2081:11:4;791:8:0;834:63047:4;791:8:0;14386:16:4;834:63047;791:8:0;14357:70:4;791:8:0;834:63047:4;;791:8:0;14357:70:4;:::i;:::-;791:8:0;834:63047:4;791:8:0;14491:17:4;834:63047;791:8:0;;;14462:71:4;791:8:0;834:63047:4;;791:8:0;14462:71:4;:::i;:::-;14589:12;;;:::i;:::-;14635:15;;;;:::i;:::-;14707:12;;:::i;:::-;14754:15;;:::i;791:8:0:-;;;;834:63047:4;791:8:0;834:63047:4;;791:8:0;13702:127:4;-1:-1:-1;;13799:29:4;;-1:-1:-1;13702:127:4;;;791:8:0;;;;834:63047:4;791:8:0;;834:63047:4;791:8:0;834:63047:4;;791:8:0;13508:91:4;13585:13;;;13508:91;;;834:63047;;;;;;;;;13297:40;-1:-1:-1;;;;;834:63047:4;;;;;13319:18;;13297:40;;;13209;834:63047;;-1:-1:-1;;;;;834:63047:4;;;;-1:-1:-1;13209:40:4;;;834:63047;;;;;;;;;13103:54;834:63047;13132:20;;;;;;:::i;:::-;834:63047;13132:25;13103:54;;;834:63047;;;;;;;;;12984:70;834:63047;;;;;13026:28;12984:70;;:38;834:63047;;;;;13001:21;12984:38;;834:63047;;;;;;-1:-1:-1;;834:63047:4;;;;;;1331:30;834:63047;;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;26899:35;;;;;;:74;;;834:63047;;;;;27047:20;;;;;;834:63047;27069:3;27107:12;;;;;;:::i;:::-;27121;;;;;;;:::i;:::-;27135:11;;;;;;:::i;:::-;27148;;;;;;:::i;:::-;834:63047;2081:11;834:63047;;;;;;;;;:::i;:::-;;;1181:103:49;;:::i;:::-;834:63047:4;;;12984:13;;;;;;:38;;;27069:3;12984:70;;;27069:3;834:63047;;;13115:8;834:63047;;;;;;;;13103:20;;;;:::i;:::-;834:63047;13103:25;:54;;;;27069:3;834:63047;;;;13220:7;834:63047;;-1:-1:-1;;;;;834:63047:4;;;-1:-1:-1;;;;;834:63047:4;;13209:18;;;:40;;;;27069:3;834:63047;;;;-1:-1:-1;;;;;834:63047:4;;13297:18;;;:40;;;;27069:3;834:63047;;;;13414:9;;:::i;:::-;13460;;;;;:::i;:::-;791:8:0;;;;;;;;;;;;;;;;13510:57:4;834:63047;791:8:0;;;;834:63047:4;13510:57;;;:::i;:::-;13509:70;13508:91;;;;;27069:3;791:8:0;;;;834:63047:4;;;791:8:0;;;;;;;;;;;;;;;13704:57:4;;;;;:::i;:::-;13703:70;13702:127;;;;;27069:3;791:8:0;;;;834:63047:4;13965:42;14063:12;14170;13965:42;:84;14754:15;13965:42;:67;:42;;;;:::i;:84::-;14063:12;;:::i;:::-;834:63047;;;;;;;14063:12;:::i;:::-;14120:48;:32;834:63047;;;;;;;;;14131:21;834:63047;14120:32;;:::i;14754:15::-;834:63047;-1:-1:-1;;;;;;;;;;;3556:68:51;834:63047:4;27032:13;;13702:127;-1:-1:-1;;13799:29:4;;-1:-1:-1;13981:17:0;13702:127:4;;13508:91;13585:13;;;13508:91;;;13297:40;-1:-1:-1;;;;;834:63047:4;;;;;13319:18;;13297:40;;;13209;834:63047;;-1:-1:-1;;;;;834:63047:4;;;;-1:-1:-1;13209:40:4;;;13103:54;834:63047;13132:20;;;;;;:::i;:::-;834:63047;13132:25;13103:54;;;12984:70;834:63047;;;;;13026:28;12984:70;;:38;834:63047;;;;;13001:21;12984:38;;26899:74;26938:35;;;;26899:74;;834:63047;;;;;;-1:-1:-1;;834:63047:4;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;:::i;:::-;;;;:::i;:::-;;;;;;-1:-1:-1;;;;;834:63047:4;1269:10:0;:26;;;1265:98;;1181:103:49;;:::i;:::-;2248:23:4;834:63047;2233:11;834:63047;;;;;;;:::i;:::-;2233:38;834:63047;;14144:216:0;834:63047:4;-1:-1:-1;;;;;834:63047:4;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;13834:117:0;;834:63047:4;13981:17:0;;;;;2248:23:4;834:63047;13965:33:0;13961:88;;834:63047:4;;;14062:17:0;14058:72;;834:63047:4;-1:-1:-1;;;;;834:63047:4;;;;;;;;2248:23;834:63047;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;14144:216:0;834:63047:4;-1:-1:-1;;;;;;;;;;;3556:68:51;834:63047:4;14058:72:0;834:63047:4;;;;14058:72:0;;13961:88;834:63047:4;;2248:23;834:63047;13961:88:0;;13834:117;-1:-1:-1;;;;;834:63047:4;;;;;;;;13834:117:0;;;;;1265:98;1318:34;;;834:63047:4;1318:34:0;834:63047:4;;1318:34:0;834:63047:4;;;;;;-1:-1:-1;;834:63047:4;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;:::i;:::-;2248:23;834:63047;2233:11;834:63047;;;;;;;:::i;:::-;2233:38;834:63047;;;19719:20;;;;;;19844:12;19818:39;19844:12;19818:39;19844:12;;:::i;19741:3::-;19773:12;21485:639;19773:12;;834:63047;19773:12;;;;:::i;:::-;1181:103:49;;:::i;:::-;21485:639:4;:::i;:::-;;834:63047;-1:-1:-1;;;;;;;;;;;3556:68:51;834:63047:4;19704:13;;834:63047;;;;;;-1:-1:-1;;834:63047:4;;;;;;;;;;;;;;;;1046:40;834:63047;;;;;;;1046:40;834:63047;;1046:40;;834:63047;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;;;;1168:71;834:63047;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;;1367:30;834:63047;;;;;;;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;;1287:37;834:63047;;;;;;-1:-1:-1;834:63047:4;;;:::o;:::-;791:8:0;;;834:63047:4;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;834:63047:4;;;;;;:::o;:::-;;;;;;;;;;;:::o;:::-;;;;;;;;;;;:::o;:::-;;;;;;;;;;;:::o;:::-;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;834:63047:4;;;;;;:::o;:::-;;;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;:::o;:::-;791:8:0;;;834:63047:4;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;-1:-1:-1;;834:63047:4;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;834:63047:4;891:5:0;834:63047:4;;;;-1:-1:-1;;;;;;834:63047:4;;;;;;;:::o;:::-;;;;;;;;;;:::o;:::-;;-1:-1:-1;;;;;834:63047:4;;;;;;;:::o;:::-;;;;;;;;;;;;;:::o;:::-;791:8:0;;;834:63047:4;;;;;;;;791:8:0;;891:5;791:8;;;;;;891:5;791:8;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;:::o;:::-;834:63047:4;;791:8:0;834:63047:4;791:8:0;;834:63047:4;791:8:0;;;;:::o;9268:1261:4:-;;9394:7;834:63047;-1:-1:-1;;;;;834:63047:4;;;-1:-1:-1;;;;;834:63047:4;;9386:15;;834:63047;;;;;-1:-1:-1;;;;;834:63047:4;9449:15;;834:63047;;;;9512:17;834:63047;9521:8;834:63047;;;;9512:17;;:::i;:::-;834:63047;;;;;;9586:10;;;;:39;;;9268:1261;834:63047;;;9935:50;834:63047;9743:12;834:63047;;;;;9681:9;834:63047;;9693:47;9694:29;9702:21;834:63047;9694:29;;:::i;9743:12::-;9935:50;;:::i;:::-;9995:105;;;;9268:1261;-1:-1:-1;;;;;;834:63047:4;;10114:19;834:63047;;10149:7;;;:::o;10110:413::-;9727:13;834:63047;;10204:20;834:63047;;;10204:20;:::i;:::-;834:63047;;;;791:8:0;;834:63047:4;791:8:0;;;9727:13:4;791:8:0;;-1:-1:-1;791:8:0;10398:17:4;791:8:0;;;;-1:-1:-1;791:8:0;10369:65:4;;;;:::i;:::-;15111:589;15317:32;;;:::i;:::-;;15408:12;;:::i;:::-;834:63047;791:8:0;834:63047:4;;;;;:::i;:::-;;;;;;;;;;791:8:0;15402:103:4;;834:63047;;;;791:8:0;15402:103:4;;834:63047;;;;;;;;15402:103;;-1:-1:-1;834:63047:4;;15402:103;;;-1:-1:-1;834:63047:4;;;15402:103;;834:63047;;;;15402:103;;;834:63047;-1:-1:-1;834:63047:4;;15402:103;;;834:63047;-1:-1:-1;834:63047:4;;;-1:-1:-1;834:63047:4;15369:8;791::0;834:63047:4;791:8:0;-1:-1:-1;834:63047:4;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;891:5:0;;;;;;;;834:63047:4;-1:-1:-1;;;;;834:63047:4;;;;;:::i;:::-;;;;;;;;;;791:8:0;;;;;;;834:63047:4;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;834:63047:4;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;;;;;-1:-1:-1;;;;;;;;834:63047:4;;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;15634:59;;834:63047;;15519:36;15515:105;;10110:413;15657:12;;;;:::i;:::-;791:8:0;834:63047:4;;;;;;;;;;791:8:0;834:63047:4;;;791:8:0;834:63047:4;;;;;;;-1:-1:-1;15402:103:4;834:63047;;;15634:59;9268:1261::o;15515:105::-;15571:38;834:63047;-1:-1:-1;834:63047:4;15369:8;791::0;834:63047:4;;791:8:0;-1:-1:-1;834:63047:4;15571:27;:38;:::i;:::-;15515:105;;;;9995;-1:-1:-1;;;;;834:63047:4;;10032:23;834:63047;;9995:105;;;834:63047;;;;-1:-1:-1;834:63047:4;;-1:-1:-1;834:63047:4;9586:39;9600:25;834:63047;9600:25;;9586:39;;834:63047;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;:::o;:::-;791:8:0;;;-1:-1:-1;834:63047:4;;;;;-1:-1:-1;834:63047:4;;;;63742:131;;834:63047;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;:::o;:::-;-1:-1:-1;;;;;834:63047:4;;;;;;;;;:::o;:::-;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;63742:131;834:63047;63742:131;;834:63047;;:::i;:::-;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;7631:1357;;834:63047;;;7811:10;;;;;;:31;;;7631:1357;7811:67;;;7631:1357;834:63047;;;7936:8;834:63047;;;;;;;7927:17;;;;:::i;:::-;834:63047;7927:22;:55;;;;7631:1357;834:63047;;;;;;;8034:22;;;;834:63047;;;8113:7;834:63047;;-1:-1:-1;;;;;834:63047:4;;;-1:-1:-1;;;;;834:63047:4;;8105:15;;:34;;;;7631:1357;834:63047;;;;8217:9;;:::i;:::-;791:8:0;;;;;;;;;;;;;;;;8245:54:4;834:63047;8285:14;791:8:0;;;834:63047:4;8245:54;;;:::i;:::-;8244:68;;:85;;;;7631:1357;8240:280;;;8578:36;;;;;:::i;:::-;8617:22;834:63047;8578:61;;-1:-1:-1;;;;;834:63047:4;8578:61;:::i;:::-;:78;;;;:::i;:::-;8658:12;;;:::i;:::-;;;834:63047;;-1:-1:-1;;;;;834:63047:4;8658:12;:::i;:::-;8285:14;834:63047;8709:20;834:63047;;;8709:20;:::i;:::-;834:63047;;;;791:8:0;;834:63047:4;791:8:0;;;8285:14:4;791:8:0;;7820:1:4;791:8:0;8827:16:4;791:8:0;;;;7820:1:4;791:8:0;8798:64:4;;;;:::i;:::-;8916:12;;;:::i;:::-;16548:31;;;;:::i;:::-;;791:8:0;834:63047:4;;;;;:::i;:::-;-1:-1:-1;;;;;834:63047:4;;;;;791:8:0;16695:99:4;;;834:63047;;;;;;;791:8:0;16695:99:4;;;834:63047;;;-1:-1:-1;16695:99:4;;;834:63047;;;16695:99;;;834:63047;;;;16695:99;;834:63047;;;16695:99;;;834:63047;;;;16695:99;;;834:63047;;;;;;16662:8;834:63047;;;;;;;;891:5:0;;-1:-1:-1;;;;;;891:5:0;834:63047:4;;891:5:0;;;;;;834:63047:4;;;;16695:99;;;;834:63047;;;-1:-1:-1;;;;;834:63047:4;;;:::i;:::-;8728:1;834:63047;;;;;;;;791:8:0;;;;;;;834:63047:4;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;834:63047:4;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;;;;;-1:-1:-1;;;;;;;;834:63047:4;;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;-1:-1:-1;834:63047:4;;;16662:8;791::0;834:63047:4;791:8:0;834:63047:4;;16804:38;;834:63047;;-1:-1:-1;16804:27:4;:38;:::i;:::-;791:8:0;834:63047:4;16857:84;;;;;7820:1;8728;16857:84;;;:::i;:::-;;;;;7631:1357::o;8240:280::-;8349:157;;;;;;;;;;;;;791:8:0;;;7820:1:4;8402:32;;7820:1;8402:32;8349:157;8481:7::o;8244:85::-;8316:13;;;;8244:85;;;8105:34;-1:-1:-1;;;;;834:63047:4;;;;;8124:15;;8105:34;;;7927:55;834:63047;7953:24;;;;;;:::i;:::-;834:63047;7953:29;7927:55;;;7811:67;834:63047;;;;;7846:32;7811:67;;:31;834:63047;;;;7825:17;;7811:31;;834:63047;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;;;;;;;;;:::o;:::-;-1:-1:-1;;;;;834:63047:4;;;;;-1:-1:-1;;;;;834:63047:4;;;;:::o;:::-;;-1:-1:-1;;;;;834:63047:4;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;:::o;:::-;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;;;;;;8938:22:0;834:63047:4;;;;;;;:::o;:::-;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;33891:1943::-;;34134:13;834:63047;;;-1:-1:-1;834:63047:4;;;;;;:::i;:::-;34170:28;34161:37;;;;;:51;;33891:1943;34161:81;;;33891:1943;34157:759;;;34279:41;;;:::i;:::-;34266:9;:54;834:63047;;34398:45;34433:9;;;:::i;:::-;34398:45;:::i;:::-;34266:9;34386:57;834:63047;;;34157:759;34984:41;34157:759;34984:41;:::i;:::-;35039:10;;;;:24;35035:76;;-1:-1:-1;;;;;834:63047:4;;35124:9;834:63047;;;;;;;:::i;:::-;35231:51;;;35120:591;35227:351;;;35320:41;;35413:7;35320:41;;:::i;:::-;35413:7;:::i;:::-;35728:31;;834:63047;;35806:21;33891:1943;:::o;834:63047::-;-1:-1:-1;;;834:63047:4;;;;;35227:351;35460:13;834:63047;;;;-1:-1:-1;;;;;834:63047:4;35485:12;;:::i;:::-;834:63047;35510:41;834:63047;;;;;35499:9;834:63047;;35510:41;;:::i;:::-;35460:103;;;;;;834:63047;;;;;;35460:103;834:63047;;;;;;;;;;;;35460:103;;;;;;:::i;:::-;;;;;;;;;;;;;;35227:351;;;;;35460:103;;;;;:::i;:::-;834:63047;;35460:103;;;;;834:63047;;;;;;;;;35460:103;834:63047;;;35231:51;35272:10;;;35231:51;;834:63047;-1:-1:-1;;;834:63047:4;;;;;35120:591;834:63047;;;;;;;;:::i;:::-;35594:117;35120:591;35594:117;35659:13;834:63047;35690:9;;;;-1:-1:-1;;;;;834:63047:4;35690:9;:::i;:::-;35120:591;;35035:76;35079:21;;;;;;;;:::o;834:63047::-;-1:-1:-1;;;834:63047:4;;;;;;-1:-1:-1;;;834:63047:4;;;;;34157:759;34503:10;;34499:417;;34157:759;34984:41;34157:759;34984:41;:::i;34499:417::-;34551:9;;834:63047;;34622:283;;;;;-1:-1:-1;34650:13:4;834:63047;-1:-1:-1;;;;;834:63047:4;34674:12;;:::i;:::-;34688:9;834:63047;-1:-1:-1;;;;;834:63047:4;;34699:41;;;:::i;:::-;34650:91;;;;;;;834:63047;-1:-1:-1;834:63047:4;;;;;;;;;;;;;;34650:91;;;;;;:::i;:::-;;;;;;;;;;;34622:283;;;34984:41;34622:283;;34499:417;;;;34650:91;;;;;-1:-1:-1;34650:91:4;;:::i;:::-;-1:-1:-1;;834:63047:4;34650:91;;34622:283;34760:9;834:63047;34984:41;;834:63047;34846:41;;-1:-1:-1;;;;;834:63047:4;34808:12;;:::i;:::-;34830:13;834:63047;-1:-1:-1;;;;;834:63047:4;;34846:41;;;:::i;:::-;;;:::i;:::-;34622:283;;834:63047;;;;-1:-1:-1;834:63047:4;;-1:-1:-1;834:63047:4;34161:81;34217:10;;:24;;34161:81;;:51;34202:10;;;34161:51;;11064:1313;;834:63047;;;11245:10;;;;;;:31;;;11064:1313;11245:60;;;11064:1313;834:63047;;;;11363:8;834:63047;;;;;11354:17;;;;:::i;:::-;834:63047;11354:22;:55;;;;11064:1313;834:63047;;;;;;;11461:22;;;;834:63047;;;11540:7;834:63047;;-1:-1:-1;;;;;834:63047:4;;;-1:-1:-1;;;;;834:63047:4;;11532:15;;:34;;;;11064:1313;834:63047;;;;11644:9;;:::i;:::-;791:8:0;;;;;;;;;;;;;;;;11672:54:4;11712:14;791:8:0;;834:63047:4;791:8:0;;;834:63047:4;11672:54;;:::i;:::-;11671:68;;:101;;;;11064:1313;11667:296;;;-1:-1:-1;11997:9:4;834:63047;12016:21;834:63047;-1:-1:-1;;;;;834:63047:4;;;;;12008:29;;;;:::i;:::-;791:8:0;;;-1:-1:-1;;;;;834:63047:4;12008:45;;;:::i;:::-;12055:12;;:::i;:::-;;;;;:::i;:::-;11712:14;834:63047;12096:20;834:63047;;;12096:20;:::i;:::-;834:63047;;;;791:8:0;;834:63047:4;791:8:0;;;11712:14:4;791:8:0;;11254:1:4;791:8:0;12214:17:4;791:8:0;;;;11254:1:4;791:8:0;12185:65:4;;;;:::i;:::-;12304:12;;;:::i;:::-;16273:675;16610:32;;;:::i;:::-;;791:8:0;834:63047:4;;;;;:::i;:::-;-1:-1:-1;;;;;834:63047:4;;;;;791:8:0;16695:99:4;;;834:63047;;;;;;;791:8:0;16695:99:4;;;834:63047;;;-1:-1:-1;16695:99:4;;;834:63047;;;16695:99;;;834:63047;;;;16695:99;;834:63047;;;16695:99;;;834:63047;;;16695:99;;;834:63047;;;;;;16662:8;834:63047;;;;;;;;891:5:0;;-1:-1:-1;;;;;;891:5:0;834:63047:4;;891:5:0;;;;;;834:63047:4;;;;16695:99;;;;834:63047;;;-1:-1:-1;;;;;834:63047:4;;;:::i;:::-;;;;;;;;;;791:8:0;;;;;;;834:63047:4;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;834:63047:4;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;;;;;-1:-1:-1;;;;;;;;834:63047:4;;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;-1:-1:-1;834:63047:4;;;16662:8;791::0;834:63047:4;791:8:0;834:63047:4;;16804:38;;834:63047;;-1:-1:-1;16804:27:4;:38;:::i;:::-;791:8:0;834:63047:4;16857:84;;;;;11254:1;16857:84;;;;:::i;11667:296::-;11792:157;;;;;;;;;;;;791:8:0;;;11254:1:4;11845:32;;11254:1;11845:32;11671:101;-1:-1:-1;;11743:29:4;;;-1:-1:-1;13981:17:0;11671:101:4;;11532:34;-1:-1:-1;;;;;834:63047:4;;;;;11551:15;;11532:34;;;11354:55;834:63047;11380:24;;;;;;:::i;:::-;834:63047;11380:29;11354:55;;;11245:60;11280:25;834:63047;11280:25;;11245:60;;:31;834:63047;;;;11259:17;;11245:31;;31091:2256;;31355:13;834:63047;;;-1:-1:-1;834:63047:4;;;;;;:::i;:::-;31392:29;31383:38;;;;;:52;;31091:2256;31383:82;;;31091:2256;31379:850;;;31519:48;;;:::i;:::-;31506:9;:61;834:63047;;31675:52;31712:14;;;:::i;:::-;31675:52;:::i;:::-;31506:9;31663:64;834:63047;;;31379:850;32312:38;31379:850;32312:38;:::i;:::-;32364:10;;;;:24;32360:81;;-1:-1:-1;;;;;834:63047:4;;32454:14;834:63047;;;;;;;:::i;:::-;32566:52;;;32450:763;32562:517;;;32768:48;;32868:7;32768:48;;:::i;32562:517::-;32915:13;834:63047;;;;-1:-1:-1;;;;;834:63047:4;32961:12;;:::i;:::-;834:63047;32987:48;834:63047;;;;;32975:10;834:63047;;32987:48;;:::i;32566:52::-;32608:10;;;32566:52;;31379:850;31800:10;;31796:433;;31379:850;32312:38;31379:850;32312:38;:::i;31796:433::-;31848:9;;834:63047;;31919:299;;;;;-1:-1:-1;31947:13:4;834:63047;-1:-1:-1;;;;;834:63047:4;31971:12;;:::i;:::-;31985:10;834:63047;-1:-1:-1;;;;;834:63047:4;;31997:48;;;:::i;:::-;31947:99;;;;;;;834:63047;-1:-1:-1;834:63047:4;;;;;;;;;;;;;;31947:99;;;;;;:::i;:::-;;;;;;;;;;;31919:299;;;32312:38;31919:299;;31796:433;;;;31947:99;;;;;-1:-1:-1;31947:99:4;;:::i;:::-;-1:-1:-1;;834:63047:4;31947:99;;31919:299;32065:10;834:63047;32312:38;;834:63047;32152:48;;-1:-1:-1;;;;;834:63047:4;32114:12;;:::i;:::-;32136:13;834:63047;-1:-1:-1;;;;;834:63047:4;;32152:48;;;:::i;:::-;31919:299;;31383:82;31440:10;;:24;;31383:82;;:52;31425:10;;;31383:52;;5776:1322;;;834:63047;;;5892:10;;;;:39;;;5776:1322;834:63047;;;5987:7;834:63047;;-1:-1:-1;;;;;834:63047:4;;;-1:-1:-1;;;;;834:63047:4;;5980:14;;834:63047;;;;;-1:-1:-1;;;;;834:63047:4;6042:14;;834:63047;;;;6104:17;834:63047;6113:8;834:63047;;;;6104:17;;:::i;:::-;834:63047;;;6543:12;6319:49;6379:63;6319:49;;;:::i;:::-;6397:45;;;;;;;:::i;:::-;6379:63;;:::i;:::-;6480:60;6481:41;834:63047;;;;;6113:8;834:63047;;;-1:-1:-1;;;;;6500:22:4;834:63047;;;6481:41;:::i;:::-;834:63047;6526:14;791:8:0;;;834:63047:4;6480:60;;:::i;6543:12::-;6566:104;;5776:1322;-1:-1:-1;;;;;;834:63047:4;;6683:19;834:63047;;6718:7;;;:::o;6679:413::-;6526:14;834:63047;;6773:20;834:63047;;;6773:20;:::i;:::-;834:63047;;;;791:8:0;;834:63047:4;791:8:0;;;6526:14:4;791:8:0;;5901:1:4;791:8:0;6968:16:4;791:8:0;;;;5901:1:4;791:8:0;6939:64:4;;;;:::i;:::-;15255:31;;;;:::i;:::-;;15408:12;;:::i;:::-;834:63047;791:8:0;834:63047:4;;;;;:::i;:::-;;;;;;;;;;791:8:0;15402:103:4;;834:63047;;;;791:8:0;15402:103:4;;834:63047;;;;;;;;15402:103;;5901:1;834:63047;;15402:103;;;5901:1;834:63047;;;15402:103;;834:63047;;;;15402:103;;;834:63047;5901:1;834:63047;;15402:103;;;834:63047;6792:1;834:63047;;;5901:1;834:63047;15369:8;791::0;834:63047:4;791:8:0;5901:1:4;834:63047;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;891:5:0;;;;;;;;834:63047:4;-1:-1:-1;;;;;834:63047:4;;;;;:::i;:::-;6792:1;834:63047;;;;;;;;791:8:0;;;;;;;834:63047:4;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;834:63047:4;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;;;;;-1:-1:-1;;;;;;;;834:63047:4;;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;15634:59;;834:63047;;15519:36;15515:105;;6679:413;15657:12;;;;:::i;:::-;791:8:0;834:63047:4;;;;;;;;;;791:8:0;834:63047:4;;;791:8:0;834:63047:4;;;;;;;6792:1;15402:103;834:63047;;;15634:59;5776:1322::o;15515:105::-;15571:38;834:63047;5901:1;834:63047;15369:8;791::0;834:63047:4;6792:1;791:8:0;5901:1:4;834:63047;15571:27;:38;:::i;:::-;15515:105;;;;6566:104;-1:-1:-1;;;;;834:63047:4;;6603:22;834:63047;;6566:104;;;5892:39;5906:25;834:63047;5906:25;;5892:39;;891:5:0;;;;;;;;;:::o;834:63047:4:-;;;;;;;-1:-1:-1;;834:63047:4;;:::o;21485:639::-;834:63047;;;-1:-1:-1;834:63047:4;21616:8;834:63047;;;-1:-1:-1;834:63047:4;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;21652:12;;:::i;:::-;834:63047;;-1:-1:-1;;;;;834:63047:4;;;;;21652:35;834:63047;;;;;;;21826:43;;;;:::i;:::-;21822:233;;22089:6;;;:::i;:::-;834:63047;21485:639;:::o;:::-;834:63047;;;;;21616:8;834:63047;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;21652:12;;:::i;:::-;834:63047;;-1:-1:-1;;;;;834:63047:4;;;;;21652:35;834:63047;;;;;;;21826:43;;;;:::i;:::-;21822:233;;22089:6;;;:::i;21822:233::-;21885:160;;;22018:12;:::o;834:63047::-;;;;;;;;;;:::o;869:404:11:-;2453:17:4;834:63047;-1:-1:-1;;;;;834:63047:4;1068:10:11;2440:30:4;;1049:71:11;;869:404;1045:222;;;834:63047:4;;958:8:11;834:63047:4;958:8:11;834:63047:4;;;;;;;;1136:72:11:o;1045:222::-;1068:10;1239:17;:::o;1049:71::-;958:8;1457:2;958:8;1083:37;;1049:71;;1290:377:49;-1:-1:-1;;;;;;;;;;;3327:69:51;1444:93:49;;1655:4;-1:-1:-1;;;;;;;;;;;3556:68:51;1290:377:49:o;1444:93::-;1496:30;;;-1:-1:-1;1496:30:49;;-1:-1:-1;1496:30:49;58605:749:4;58728:16;58693:52;;:::i;:::-;834:63047;791:8:0;;;;;;;;;;;;;;;58693:91:4;58728:16;58770:14;791:8:0;;;834:63047:4;58693:91;;;:::i;:::-;58812:55;791:8:0;;;;;;58798:69:4;;58794:468;;-1:-1:-1;;834:63047:4;;59278:17;;;;791:8:0;59271:76:4;58605:749;:::o;59278:69::-;-1:-1:-1;;;;;13981:17:0;58605:749:4:o;58794:468::-;834:63047;-1:-1:-1;834:63047:4;58887:17;58883:329;;59225:26;-1:-1:-1;59225:26:4;;:::o;58883:329::-;58928:26;;;;58924:99;;44639:70:13;;;;;;;;;59047:65:4;;;;:150;791:8:0;59040:157:4;;:::o;59047:150::-;-1:-1:-1;;;59047:150:4;59040:157::o;57908:691::-;834:63047;57995:37;;:::i;:::-;834:63047;791:8:0;;;;;;;;;;;;;;;57995:76:4;791:8:0;834:63047:4;58057:14;791:8:0;;;834:63047:4;57995:76;;:::i;:::-;58085:14;58081:424;;-1:-1:-1;58522:12:4;834:63047;;-1:-1:-1;;58522:33:4;;13981:17:0;;58560:4:4;58514:78;57908:691;:::o;58521:71::-;58030:1;;-1:-1:-1;58030:1:4;;57908:691::o;58081:424::-;834:63047;58119:12;834:63047;13981:17:0;;58119:33:4;;58115:341;;58469:25;58030:1;58469:25;;:::o;58115:341::-;58176:25;;;;58172:97;;44135:70:13;;;;;;;;;58293:64:4;;;;:148;58381:4;58286:155;;:::o;53576:213::-;53768:14;53576:213;53695:56;53576:213;834:63047;-1:-1:-1;;;;;791:8:0;53737:13:4;791:8:0;;;834:63047:4;;;;;53695:56;:::i;:::-;53768:14;:::i;:::-;53576:213;:::o;18611:178::-;18718:13;834:63047;18611:178;;;;-1:-1:-1;;;;;834:63047:4;18718:64;;;;;;834:63047;-1:-1:-1;834:63047:4;;;;;;;;;;;;;;18718:64;;;;;;:::i;:::-;;;;;;;;;;;18611:178;:::o;18718:64::-;-1:-1:-1;18718:64:4;;;:::i;834:63047::-;;;;;;;;;;;;;;;;:::o;540:361:14:-;834:63047:4;;;;;;;684:18:14;834:63047:4;;791:8:0;;-1:-1:-1;;791:8:0;834:63047:4;;;791:8:0;;;785:20:14;;;:::i;680:187::-;836:20;;;:::i;834:63047:4:-;;;;;;;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;16273:675::-;;;;;;;;16548:31;;;:::i;:::-;;834:63047;;;;;;:::i;:::-;;;;;;;;;;;16695:99;;;834:63047;-1:-1:-1;;;;;834:63047:4;;;;;16695:99;;834:63047;;;;;;;16695:99;;;352:1:14;834:63047:4;;16695:99;;;834:63047;;;;;16695:99;;;834:63047;;;;;;16695:99;;;834:63047;;;;;;16695:99;;;834:63047;;;;;;;352:1:14;834:63047:4;16662:8;16695:99;834:63047;;352:1:14;834:63047:4;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;891:5:0;;;;;;;;834:63047:4;-1:-1:-1;;;;;834:63047:4;;;;;:::i;:::-;;;;;;;;;;791:8:0;;;;;;;834:63047:4;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;834:63047:4;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;;;;;-1:-1:-1;;;;;;;;834:63047:4;;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;-1:-1:-1;834:63047:4;;;16662:8;16695:99;834:63047;;;;16804:38;;834:63047;;-1:-1:-1;16804:27:4;:38;:::i;:::-;834:63047;;16857:84;;;;;834:63047;16857:84;;;:::i;16273:675::-;;;;;;;;16610:32;;;:::i;:::-;;834:63047;;;;;;:::i;:::-;;;;;;;;;;;16695:99;;;834:63047;-1:-1:-1;;;;;834:63047:4;;;;;16695:99;;834:63047;;;;;;;16695:99;;;834:63047;;;16695:99;;;834:63047;;;;;16695:99;;;834:63047;;;;;;16695:99;;;834:63047;;;;;;16695:99;;;834:63047;;;;;;;;;16662:8;16695:99;834:63047;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;891:5:0;;;;;;;;834:63047:4;-1:-1:-1;;;;;834:63047:4;;;;;:::i;:::-;;;;;;;;;;791:8:0;;;;;;;834:63047:4;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;834:63047:4;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;;;;;-1:-1:-1;;;;;;;;834:63047:4;;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;-1:-1:-1;834:63047:4;;;16662:8;16695:99;834:63047;;;;16804:38;;834:63047;;-1:-1:-1;16804:27:4;:38;:::i;:::-;834:63047;;16857:84;;;;;834:63047;16857:84;;;:::i;834:63047::-;;-1:-1:-1;;;;;834:63047:4;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;:::o;:::-;;791:8:0;834:63047:4;;;;;;;:::o;:::-;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;834:63047:4;;;;;;;;;;;;-1:-1:-1;834:63047:4;;;63742:131;;834:63047;;;;;;:::i;43621:3185::-;;;834:63047;;43815:29;834:63047;43854:19;43923:9;;:::i;:::-;43883:49;;;43942:25;834:63047;791:8:0;834:63047:4;44002:14;791:8:0;;;834:63047:4;791:8:0;;;;;;;;;;;;;;;;44040:51:4;;;;;;;;;;;;;;;:::i;:::-;44101:1346;;-1:-1:-1;;;;;834:63047:4;;44108:23;;:50;;;44101:1346;44108:83;;;44101:1346;44108:83;;;44207:50;;44336:982;;;44501:11;;;;;:::i;:::-;:26;:58;;44530:11;;:::i;:::-;834:63047;;;;;-1:-1:-1;;;;;8918:17:0;834:63047:4;;;9980:4;834:63047;;8918:42:0;834:63047:4;;;8918:42:0;;:::i;:::-;8970:47;9027:16;;834:63047:4;-1:-1:-1;;;;;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;9057:26:0;;9053:83;;44501:58:4;9146:1078:0;;9153:20;;;:36;;;9146:1078;9153:36;;;-1:-1:-1;;;;;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;9209:25:0;;9205:1009;834:63047:4;;;;;;9406:79:0;834:63047:4;;9449:25:0;834:63047:4;9306:82:0;9980:4:4;834:63047;891:5:0;-1:-1:-1;;;;;891:5:0;;9254:34;834:63047:4;;;;9254:34:0;:::i;:::-;834:63047:4;;;;;;;;;;;;;9980:4;834:63047;9325:63:0;-1:-1:-1;;;;;44002:14:4;791:8:0;;;834:63047:4;-1:-1:-1;;;;;834:63047:4;;9325:63:0;;:::i;:::-;9306:82;;:::i;:::-;9449:25;;:::i;:::-;9406:79;;:::i;:::-;834:63047:4;9533:5:0;9146:1078;834:63047:4;;10237:22:0;;10233:463;;9146:1078;10745:16;;45346:39:4;10745:16:0;;;44599:38:4;10745:16:0;;:::i;:::-;834:63047:4;;;;;9980:4;834:63047;;12099:141:0;12154:70;:48;834:63047:4;;;;;56390:9;834:63047;;;-1:-1:-1;;;;;12173:29:0;56154:17:4;834:63047;12173:29:0;:::i;:::-;834:63047:4;;12154:48:0;:::i;:70::-;834:63047:4;12099:141:0;56154:17:4;834:63047;12099:141:0;;;;;;;;:::i;:::-;;63742:131:4;;12099:141:0;;;;;;:::i;:::-;12086:154;891:5;12284:93;791:8;12285:49;12304:30;56276:18:4;834:63047;12304:30:0;:::i;:::-;12285:49;;:::i;:::-;791:8;56039:11:4;834:63047;12284:93:0;;:::i;:::-;791:8;12418:13;;12414:142;;9146:1078;44411:170:4;;;;44599:38;;:::i;:::-;44336:982;45346:39;:::i;:::-;45427:9;;;:::i;:::-;44101:1346;;;;;;;45400:36;44101:1346;;12414:142:0;12460:85;834:63047:4;;12485:59:0;834:63047:4;12485:59:0;834:63047:4;;;;;56496:10;834:63047;;;56154:17;834:63047;12485:59:0;;;12099:141;12485:59;;;:::i;:::-;12460:85;:::i;:::-;12414:142;;;;;10233:463;834:63047:4;;;;;;8918:17:0;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;;;891:5:0;834:63047:4;;;891:5:0;834:63047:4;;;891:5:0;27089:32:13;:18;-1:-1:-1;;;;;27089:18:13;;;;;:::i;:::-;:32;:::i;:::-;791:8:0;27120:1:13;834:63047:4;;;;;;;;;8918:17:0;834:63047:4;;;;846:5:0;834:63047:4;;;846:5:0;834:63047:4;;;25998:340:13;846:5:0;25998:340:13;;;846:5:0;25998:340:13;;;;;;;;;;;;44599:38:4;25998:340:13;10500:185:0;45346:39:4;25998:340:13;10745:16:0;25998:340:13;;10500:185:0;:::i;:::-;10480:205;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;10480:205:0;834:63047:4;10233:463:0;;;;;;;;25998:340:13;;834:63047:4;25998:340:13;8918:17:0;25998:340:13;;9205:1009:0;9577:26;;9640:69;9577:26;;9621:88;9577:26;;;;;;;:::i;:::-;791:8;-1:-1:-1;;;;;44002:14:4;791:8:0;;;834:63047:4;9640:69:0;;;:::i;9621:88::-;834:63047:4;-1:-1:-1;;;;;8918:17:0;834:63047:4;;;;;;846:5:0;834:63047:4;;;846:5:0;834:63047:4;;;-1:-1:-1;;;;;834:63047:4;25998:340:13;;;;;;;;;;;846:5:0;25998:340:13;;;;9750:201:0;846:5;9969:61;25998:340:13;;9750:201:0;:::i;:::-;834:63047:4;;;;;;;-1:-1:-1;;;;;834:63047:4;9969:61:0;:::i;:::-;10048:37;834:63047:4;891:5:0;834:63047:4;;891:5:0;834:63047:4;;;27089:32:13;:18;27088:44;27089:18;;:::i;:::-;791:8:0;9980:4:4;791:8:0;27089:32:13;;:::i;27088:44::-;9205:1009:0;9146:1078;;9153:36;;;;;;;;834:63047:4;-1:-1:-1;;;;;834:63047:4;;9177:12:0;;9153:36;;9053:83;-1:-1:-1;;;;;834:63047:4;;;9053:83:0;;;44501:58:4;-1:-1:-1;;834:63047:4;;;;;;44501:58;834:63047;791:8:0;;;834:63047:4;791:8:0;;;;;834:63047:4;791:8:0;44336:982:4;44789:26;;;;;;;;;;;:::i;:::-;791:8:0;;;44783:55:4;;;:::i;:::-;44877:26;;;;;:::i;:::-;791:8:0;;;834:63047:4;;44860:16;834:63047;;;;;44713:272;;;:::i;:::-;44676:309;;;;;45100:43;;;:::i;:::-;45175:26;;;;:::i;:::-;791:8:0;;;45169:55:4;;;:::i;:::-;44002:14;791:8:0;;;-1:-1:-1;;;;;834:63047:4;;;;;-1:-1:-1;;;;;834:63047:4;45050:235;;;;:::i;:::-;45023:280;;;:::i;:::-;45003:300;;;:::i;:::-;44336:982;45346:39;44336:982;45346:39;:::i;44108:83::-;;;;;;-1:-1:-1;;;;;44108:83:4;;;;;834:63047;45461:21;;;:51;;;44101:1346;45457:113;;45677:64;:154;834:63047;45678:44;45627:22;834:63047;45678:44;;;:::i;45677:64::-;791:8:0;45761:47:4;;;;:::i;:::-;791:8:0;45677:154:4;;:::i;:::-;45849:25;;;;:53;;;44101:1346;45845:252;;44101:1346;834:63047;;46133:11;834:63047;46162:16;46158:343;;44101:1346;46518:10;;46514:224;;44101:1346;46758:41;;;43621:3185;:::o;46514:224::-;46562:13;834:63047;-1:-1:-1;;;;;834:63047:4;;46616:89;;46654:12;;:::i;:::-;834:63047;46643:61;834:63047;;;;;46668:10;834:63047;;;46643:61;9980:4;834:63047;;;46643:61;;;;;;;:::i;46616:89::-;46562:161;;;;;;834:63047;;;;;;;;;;;;;;;46562:161;;;;;;:::i;:::-;;;;;;;;;;46514:224;46562:161;;;;;;;:::i;:::-;834:63047;;46562:161;46514:224;;46562:161;834:63047;;;;;;;;;46562:161;834:63047;;;46158:343;46428:57;46218:69;46429:40;46305:24;46218:69;46406:80;46218:69;;;:::i;:::-;46305:24;;;:::i;:::-;834:63047;46442:26;46457:11;834:63047;46442:26;;:::i;:::-;46429:40;;:::i;46428:57::-;46406:80;834:63047;46406:80;:::i;:::-;;834:63047;46158:343;;;45845:252;45922:13;834:63047;;;45981:10;834:63047;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;791:8:0;;45994:47:4;;834:63047;45994:47;:::i;:::-;791:8:0;45922:160:4;;;;;;;834:63047;;;;;;;;;;;;;;;;45922:160;;;;;;:::i;:::-;;;;;;;;;;;45845:252;;;;45922:160;;;;;834:63047;45922:160;;:::i;:::-;834:63047;45922:160;;;;45849:53;45878:10;;:24;;45849:53;;45457:113;45528:31;;;;;;;;834:63047;45528:31;:::o;45461:51::-;45486:26;;;45461:51;;44108:83;-1:-1:-1;;;44162:29:4;;;44108:83;;:50;44135:23;;;;;44108:50;;43621:3185;;;;-1:-1:-1;;43923:9:4;;:::i;:::-;43883:49;;;43942:25;834:63047;791:8:0;834:63047:4;44002:14;791:8:0;;;834:63047:4;791:8:0;;;-1:-1:-1;44040:51:4;791:8:0;;;;;;;;;44101:1346:4;834:63047;;-1:-1:-1;;;;;834:63047:4;45461:21;;;:51;;;44101:1346;45457:113;;45677:64;:154;834:63047;45678:44;45627:22;834:63047;45678:44;;;:::i;45677:64::-;791:8:0;45761:47:4;;;;:::i;45677:154::-;45849:25;;;;:53;;;44101:1346;45845:252;;44101:1346;834:63047;;;46133:11;834:63047;46162:16;46158:343;;44101:1346;46518:10;;46514:224;;44101:1346;46758:41;;;;43621:3185;:::o;46514:224::-;46562:13;834:63047;-1:-1:-1;;;;;834:63047:4;;46616:89;;834:63047;46643:61;46654:12;;:::i;:::-;46643:61;834:63047;;;;;46668:10;834:63047;;;;;;46643:61;;;;;;;:::i;46616:89::-;46562:161;;;;;;834:63047;;;;;;;;;;;;;;;46562:161;;;;;;:::i;:::-;;;;;;;;;;;46514:224;;;;46562:161;;;;;;:::i;:::-;834:63047;;46562:161;;;46158:343;46428:57;46218:69;46429:40;46305:24;46218:69;46406:80;46218:69;;;:::i;:::-;46305:24;;;:::i;:::-;834:63047;46442:26;46457:11;834:63047;46442:26;;:::i;46406:80::-;;834:63047;46158:343;;;45845:252;45922:13;834:63047;;;45981:10;834:63047;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;791:8:0;;45994:47:4;;834:63047;45994:47;:::i;:::-;791:8:0;45922:160:4;;;;;;;834:63047;-1:-1:-1;834:63047:4;;;;;;;;;;;;;;45922:160;;;;;;:::i;:::-;;;;;;;;;;;45845:252;;;;;45922:160;;;;;-1:-1:-1;45922:160:4;;:::i;:::-;-1:-1:-1;45922:160:4;;;;45849:53;45878:10;;:24;;45849:53;;45457:113;45528:31;;;;;;;;;;-1:-1:-1;45528:31:4;:::o;45461:51::-;45486:26;;;45461:51;;44101:1346;-1:-1:-1;;;;;834:63047:4;;;;;;44108:23;;:50;;;44101:1346;44108:83;;;44101:1346;44108:83;;;44207:50;;44336:982;;;44501:11;;;;:::i;:::-;-1:-1:-1;44501:58:4;;;;44530:11;;:::i;:::-;-1:-1:-1;834:63047:4;;-1:-1:-1;834:63047:4;-1:-1:-1;;;;;8918:17:0;834:63047:4;;;;;8918:42:0;834:63047:4;;;8918:42:0;;:::i;:::-;8970:47;9027:16;;834:63047:4;-1:-1:-1;;;;;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;9057:26:0;;9053:83;;44501:58:4;9146:1078:0;;;791:8;;;9146:1078;834:63047:4;;;-1:-1:-1;834:63047:4;10237:22:0;;10233:463;;9146:1078;10745:16;;45346:39:4;10745:16:0;;;44599:38:4;10745:16:0;;:::i;:::-;834:63047:4;;;;;;;;12099:141:0;12154:70;:48;834:63047:4;;;;;56390:9;834:63047;;;-1:-1:-1;;;;;12173:29:0;56154:17:4;834:63047;12173:29:0;:::i;12099:141::-;12086:154;891:5;12284:93;791:8;12285:49;12304:30;56276:18:4;834:63047;12304:30:0;:::i;12284:93::-;791:8;12418:13;;12414:142;;9146:1078;44411:170:4;;;;44599:38;;:::i;:::-;44336:982;45346:39;:::i;:::-;45427:9;;;;:::i;:::-;44101:1346;;;12414:142:0;12460:85;834:63047:4;;12485:59:0;834:63047:4;12485:59:0;834:63047:4;;;;;56496:10;834:63047;;;56154:17;834:63047;12485:59:0;;;12099:141;12485:59;;;:::i;12460:85::-;12414:142;;;;;10233:463;834:63047:4;-1:-1:-1;834:63047:4;;;;8918:17:0;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;;;891:5:0;834:63047:4;;;891:5:0;834:63047:4;;;891:5:0;27089:32:13;:18;-1:-1:-1;;;;;27089:18:13;;;;;:::i;:32::-;791:8:0;27120:1:13;834:63047:4;;;;;;;;;8918:17:0;834:63047:4;;;;846:5:0;834:63047:4;;;846:5:0;834:63047:4;;;25998:340:13;846:5:0;25998:340:13;;;846:5:0;25998:340:13;;;;;;;;;;;;44599:38:4;25998:340:13;10500:185:0;45346:39:4;25998:340:13;10745:16:0;25998:340:13;;10500:185:0;:::i;:::-;10480:205;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;10480:205:0;834:63047:4;10233:463:0;;;;;;;;9146:1078;9153:20;;;;;;;;;:36;;;9146:1078;9153:36;;;-1:-1:-1;;;;;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;9209:25:0;;9205:1009;834:63047:4;;;;;;;9449:25:0;834:63047:4;9306:82:0;9406:79;834:63047:4;;;;891:5:0;-1:-1:-1;;;;;891:5:0;;9254:34;834:63047:4;;;;9254:34:0;:::i;:::-;834:63047:4;;;;;;;;;;;;;;;9325:63:0;-1:-1:-1;;;;;44002:14:4;791:8:0;;;834:63047:4;-1:-1:-1;;;;;834:63047:4;;9325:63:0;;:::i;9406:79::-;-1:-1:-1;9533:5:0;;;;;;9205:1009;9577:26;;;;;;;9621:88;9577:26;;;9640:69;9577:26;;:::i;:::-;791:8;-1:-1:-1;;;;;44002:14:4;791:8:0;;;834:63047:4;9640:69:0;;;:::i;9621:88::-;834:63047:4;-1:-1:-1;;;;;8918:17:0;834:63047:4;;;;;;846:5:0;834:63047:4;;;846:5:0;834:63047:4;;;-1:-1:-1;;;;;834:63047:4;25998:340:13;;;;;;;;;;;846:5:0;25998:340:13;;;;;;;9750:201:0;846:5;9969:61;25998:340:13;;9750:201:0;:::i;:::-;834:63047:4;;-1:-1:-1;;;;;834:63047:4;9969:61:0;:::i;:::-;10048:37;834:63047:4;891:5:0;834:63047:4;;;891:5:0;834:63047:4;;;27089:18:13;;:32;:18;27088:44;27089:18;;:::i;:::-;791:8:0;;;27089:32:13;;:::i;27088:44::-;9205:1009:0;9146:1078;;;;9153:36;;;;;;;;;;;834:63047:4;-1:-1:-1;;;;;834:63047:4;;9177:12:0;;9153:36;;9053:83;-1:-1:-1;;;;;834:63047:4;;;9053:83:0;;;44501:58:4;-1:-1:-1;;;;;44501:58:4;;44336:982;44789:26;;;;;;;;;;;:::i;:::-;791:8:0;;;44783:55:4;;;:::i;:::-;44877:26;;;;;:::i;:::-;791:8:0;;;-1:-1:-1;834:63047:4;44860:16;834:63047;;;-1:-1:-1;834:63047:4;44713:272;;;:::i;:::-;44676:309;;;;;45100:43;;;:::i;:::-;45175:26;;;;:::i;:::-;791:8:0;;;45169:55:4;;;:::i;:::-;44002:14;791:8:0;;;-1:-1:-1;;;;;834:63047:4;;;;;-1:-1:-1;;;;;834:63047:4;45050:235;;;;:::i;:::-;45023:280;;;:::i;:::-;45003:300;;;:::i;:::-;44336:982;45346:39;44336:982;45346:39;:::i;44108:83::-;;;;;;;;;-1:-1:-1;;;44162:29:4;;;44108:83;;:50;;;;;36389:170;36497:38;36496:56;36389:170;-1:-1:-1;;;;;36514:21:4;834:63047;;;36497:38;:::i;9109:1139:63:-;9254:988;;;9109:1139;;;9254:988;9109:1139;9254:988;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;9109:1139::o;9254:988::-;;;;;;;4031:342;4146:221;4031:342;;;4146:221;;;;;;;4031:342::o;4146:221::-;;;;;;;36009:192:4;36096:13;834:63047;36186:7;;36009:192;36127:19;;-1:-1:-1;;;;;834:63047:4;36127:19;36009:192;36127:9;:19;:::i;:::-;;;:::i;:::-;36157:12;;:::i;:::-;36186:7;:::i;4654:114::-;4723:5;834:63047;;;-1:-1:-1;;;;;834:63047:4;4709:10;:19;834:63047;;4654:114::o;834:63047::-;;;;-1:-1:-1;834:63047:4;;-1:-1:-1;834:63047:4;36207:176;36318:40;36317:59;36207:176;-1:-1:-1;;;;;36336:22:4;834:63047;;;36318:40;:::i;834:63047::-;;-1:-1:-1;;;;;834:63047:4;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;:::o;40227:3151::-;;834:63047;;40366:27;834:63047;40403:25;834:63047;791:8:0;40463:14:4;791:8:0;;-1:-1:-1;;;;;834:63047:4;791:8:0;;;834:63047:4;791:8:0;;;834:63047:4;40575:9;;;:::i;:::-;40595:1560;;-1:-1:-1;;;;;834:63047:4;;40602:14;;:31;;;40595:1560;40602:31;;;40687:1339;;;40819:11;;;:::i;:::-;834:63047;;-1:-1:-1;;;;;2301:12:0;834:63047:4;;;791:8:0;;;;;;;;;;;;;;;;40463:14:4;791:8:0;;2345:55;834:63047:4;791:8:0;;;834:63047:4;2345:55:0;;:::i;:::-;834:63047:4;2440:17:0;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;;;;2502:22:0;834:63047:4;;;;2668:71:0;-1:-1:-1;;;;;2558:45:0;834:63047:4;;;2558:45:0;;:::i;:::-;791:8;;;834:63047:4;-1:-1:-1;;;;;834:63047:4;;2668:71:0;;:::i;:::-;2653:86;;;2649:143;;40687:1339:4;2802:1466:0;;2809:20;;;:39;;;2802:1466;2809:39;;;-1:-1:-1;;;;;40463:14:4;791:8:0;;;834:63047:4;;2894:71:0;834:63047:4;-1:-1:-1;;;;;834:63047:4;;2894:71:0;;:::i;:::-;2983:33;;;;;;3070;;3064:49;3070:42;:33;;3243:98;3070:33;;3131:32;3070:33;;:::i;3064:49::-;3131:32;;;:::i;:::-;834:63047:4;2440:17:0;834:63047:4;-1:-1:-1;;;;;3181:44:0;834:63047:4;;;;3181:44:0;:::i;:::-;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;;2440:17:0;834:63047:4;;3285:35:0;834:63047:4;;;;;;2502:22:0;834:63047:4;;3285:35:0;;:::i;:::-;3243:98;;:::i;:::-;834:63047:4;3392:5:0;2802:1466;2301:12;834:63047:4;4281:22:0;;4277:463;;2802:1466;-1:-1:-1;;2502:22:0;834:63047:4;;-1:-1:-1;;;;;834:63047:4;891:5:0;834:63047:4;;;;-1:-1:-1;;;;;;834:63047:4;;;;;;;40463:14;791:8:0;;;834:63047:4;;25998:340:13;;;;;;;;;;;;;791:8:0;25998:340:13;;;;;4945:108:0;4982:69;42054:39:4;25998:340:13;4982:55:0;40861:35:4;25998:340:13;4821:87:0;791:8;40914:32:4;25998:340:13;;4821:87:0;:::i;:::-;791:8;4982:55;:::i;:::-;:69;:::i;:::-;4945:108;;:::i;:::-;40759:84:4;40861:35;;:::i;:::-;40914:32;;:::i;:::-;40687:1339;42054:39;:::i;:::-;42135:9;;;:::i;:::-;40595:1560;;;;;;;;42108:36;40595:1560;;4277:463:0;834:63047:4;2301:12:0;834:63047:4;4440:15:0;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;;;;;891:5:0;834:63047:4;;891:5:0;834:63047:4;;;27089:32:13;:18;27088:44;27089:18;;:::i;27088:44::-;834:63047:4;;2440:17:0;834:63047:4;;-1:-1:-1;;;;834:63047:4;;;;;-1:-1:-1;;;834:63047:4;;;;846:5:0;834:63047:4;;;;-1:-1:-1;;;;;834:63047:4;;;-1:-1:-1;834:63047:4;;25998:340:13;;;;;;;;;;;846:5:0;25998:340:13;;;;4544:185:0;846:5;-1:-1:-1;;;;;25998:340:13;;4544:185:0;:::i;:::-;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;;4440:15:0;834:63047:4;4277:463:0;;;;2979:1279;3436:28;;;;;3482:34;3436:28;;;;;;;;;:::i;:::-;3482:34;;:::i;:::-;834:63047:4;-1:-1:-1;;;;;3703:15:0;834:63047:4;;;;;;846:5:0;834:63047:4;;;846:5:0;834:63047:4;;;-1:-1:-1;;;;;834:63047:4;25998:340:13;846:5:0;25998:340:13;;;846:5:0;25998:340:13;;;;;;;;;;;3776:60:0;25998:340:13;3557:201:0;25998:340:13;;3557:201:0;:::i;:::-;834:63047:4;;;;;;;-1:-1:-1;;;;;834:63047:4;3776:60:0;:::i;:::-;3854:37;834:63047:4;891:5:0;834:63047:4;;;891:5:0;834:63047:4;;;891:5:0;27089:32:13;:18;;-1:-1:-1;;;;;27089:18:13;;:::i;:32::-;791:8:0;834:63047:4;;25998:340:13;;891:5:0;25998:340:13;;;891:5:0;25998:340:13;;;;;;;;;;;4051:192:0;25998:340:13;;4051:192:0;:::i;:::-;2979:1279;2802:1466;;2809:39;;;;;;;;;2833:15;;;;2809:39;;2649:143;-1:-1:-1;;834:63047:4;2440:17:0;834:63047:4;2649:143:0;;;40687:1339:4;41149:27;;;;;;-1:-1:-1;;;;;41149:27:4;;:::i;:::-;834:63047;791:8:0;;;;;;;;;;;;;;;;41881:26:4;41202;41697:76;41202:26;;41398:281;41143:87;41149:80;41719:53;41202:26;;;;;:::i;:::-;41149:80;;:::i;41143:87::-;41475:26;791:8:0;41565:26:4;41475;41469:55;41475:26;;;;;;:::i;:::-;791:8:0;41469:55:4;:::i;:::-;41565:26;;:::i;:::-;791:8:0;834:63047:4;;41546:17;834:63047;;;;;41398:281;;:::i;:::-;41356:323;;;;41719:53;;:::i;41697:76::-;41881:26;;:::i;:::-;791:8:0;;;;;;;;;;;;;;;42054:39:4;834:63047;41804:207;834:63047;-1:-1:-1;;;;;41831:162:4;834:63047;;41831:162;;:::i;41804:207::-;40687:1339;42054:39;:::i;40602:31::-;;;;;;;;;;;-1:-1:-1;;;;;834:63047:4;42169:22;;42165:75;;-1:-1:-1;;;;;42355:61:4;834:63047;42356:42;42297:21;834:63047;42356:42;;;:::i;42355:61::-;834:63047;;42434:24;;;;:52;;;40595:1560;42430:243;;40595:1560;834:63047;;;42709:11;834:63047;42738:16;42734:345;;40595:1560;43096:10;;43092:223;;43328:33;;;;40227:3151;:::o;43092:223::-;43140:13;834:63047;-1:-1:-1;;;;;834:63047:4;;43194:88;;834:63047;43221:60;43232:12;;:::i;:::-;43221:60;834:63047;;;;;43246:9;834:63047;;;;;;43221:60;;;;;;;:::i;42734:345::-;43006:57;42794:70;43007:40;42882:25;42794:70;42985:79;42794:70;;;:::i;43006:57::-;42985:79;834:63047;42985:79;:::i;:::-;;834:63047;42734:345;;;42430:243;42506:13;834:63047;;;42565:9;834:63047;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;42576:64;;834:63047;;42577:45;;834:63047;42577:45;:::i;42576:64::-;42506:152;;;;;;;834:63047;;;;;;;;;;;;;;;;42506:152;;;;;;:::i;:::-;;;;;;;;;;;42430:243;;;;;42506:152;;;;;834:63047;42506:152;;:::i;:::-;834:63047;42506:152;;;;42434:52;42462:10;;:24;;42434:52;;42165:75;42207:22;;;;;;;;;834:63047;42207:22;:::o;40602:31::-;40620:13;;;;40602:31;;36995:3082;;834:63047;;;37211:20;834:63047;37241:25;834:63047;37316:9;;;:::i;:::-;791:8:0;834:63047:4;37360:14;791:8:0;;;834:63047:4;791:8:0;;;;;;;;;;;;;;;;37398:51:4;;;;;;;;;;;;;;;:::i;:::-;37460:1431;;-1:-1:-1;;;;;834:63047:4;;37467:19;;:46;;;37460:1431;37467:63;;;37460:1431;37467:63;;;37546:45;37640:24;37679:657;;;;;37850:11;;;:::i;:::-;:25;:57;;;37892:1;834:63047;;;;;;;37850:57;5726:12:0;834:63047:4;5748:16:0;;5805:17;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;;;;5867:22:0;834:63047:4;;;5935:22:0;834:63047:4;;5991:45:0;-1:-1:-1;;;;;834:63047:4;;5991:45:0;;:::i;:::-;834:63047:4;-1:-1:-1;;;;;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;6050:26:0;;6046:83;;37850:57:4;6138:1316:0;;6145:20;;;:36;;;6138:1316;6145:36;;;-1:-1:-1;;;;;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;6201:25:0;;6197:1247;834:63047:4;;;;;;6298:83:0;6399:78;834:63047:4;5805:17:0;834:63047:4;-1:-1:-1;;;;;6246:34:0;834:63047:4;;;;6246:34:0;:::i;:::-;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;;5805:17:0;834:63047:4;6316:65:0;-1:-1:-1;;;;;37360:14:4;791:8:0;;;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;6316:65:0;:::i;6298:83::-;834:63047:4;;6441:25:0;834:63047:4;;;;;;5867:22:0;834:63047:4;;6441:25:0;;:::i;6399:78::-;834:63047:4;6525:5:0;;;6138:1316;5726:12;834:63047:4;7467:22:0;;7463:463;;6138:1316;834:63047:4;;5867:22:0;834:63047:4;891:5:0;-1:-1:-1;;;;;891:5:0;;834:63047:4;;;;;;;;;;;;;5867:22:0;834:63047:4;37360:14;791:8:0;;;834:63047:4;;8017:85:0;;;;:::i;:::-;8011:92;;;:::i;:::-;8152:16;;;;:::i;:::-;8128:57;;;;:::i;:::-;37759:166:4;37943:36;;;;;;:::i;:::-;37679:657;38422:31;37679:657;38370:38;37679:657;38370:38;:::i;:::-;38422:31;;;:::i;:::-;38467:295;;;;38503:34;38790:39;38503:34;;;;;:::i;38790:39::-;38871:9;;;:::i;:::-;38844:36;37460:1431;;;;38844:36;;37460:1431;;;38467:295;38642:74;38593:154;38790:39;38642:74;;38636:81;38642:74;38576:171;38642:74;;;;;;:::i;:::-;38636:81;:::i;:::-;38593:154;:::i;38576:171::-;38467:295;38790:39;:::i;7463:463:0:-;834:63047:4;;;;5726:12:0;834:63047:4;7626:15:0;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;;;;;891:5:0;834:63047:4;;891:5:0;834:63047:4;;;27089:32:13;:18;27088:44;27089:18;;:::i;27088:44::-;834:63047:4;;5805:17:0;834:63047:4;;-1:-1:-1;;;;834:63047:4;;;;;-1:-1:-1;;;834:63047:4;;;;846:5:0;834:63047:4;;;;-1:-1:-1;;;;;834:63047:4;;;-1:-1:-1;834:63047:4;;25998:340:13;;;;;;;;;;;846:5:0;25998:340:13;;;;-1:-1:-1;;;;;7730:185:0;846:5;25998:340:13;;;7730:185:0;:::i;:::-;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;;7626:15:0;834:63047:4;7463:463:0;;;;6197:1247;6569:26;;6631:71;6569:26;;6613:89;6569:26;;;;;;;;:::i;:::-;791:8;;-1:-1:-1;;;;;37360:14:4;791:8:0;;;834:63047:4;6631:71:0;;:::i;6613:89::-;834:63047:4;-1:-1:-1;;;;;6889:15:0;834:63047:4;;;;;;846:5:0;834:63047:4;;;846:5:0;834:63047:4;;;-1:-1:-1;;;;;834:63047:4;25998:340:13;846:5:0;25998:340:13;;;846:5:0;25998:340:13;;;;;;;;;;;6962:60:0;25998:340:13;6743:201:0;25998:340:13;;6743:201:0;:::i;:::-;834:63047:4;;;;;;;-1:-1:-1;;;;;834:63047:4;6962:60:0;:::i;:::-;7040:37;834:63047:4;891:5:0;834:63047:4;;;891:5:0;834:63047:4;;;891:5:0;27089:32:13;:18;;-1:-1:-1;;;;;27089:18:13;;:::i;:32::-;791:8:0;834:63047:4;;25998:340:13;;891:5:0;25998:340:13;;;891:5:0;25998:340:13;;;;;;;;;;;7237:192:0;25998:340:13;;7237:192:0;:::i;:::-;6197:1247;6138:1316;;6145:36;;;;;;;;;;834:63047:4;-1:-1:-1;;;;;834:63047:4;;6169:12:0;;6145:36;;6046:83;-1:-1:-1;;834:63047:4;5805:17:0;834:63047:4;6046:83:0;;;37850:57:4;37896:11;;:::i;:::-;37850:57;;37679:657;38422:31;38051:270;38370:38;38128:26;38122:55;38128:26;;;791:8:0;38128:26:4;;;;:::i;38122:55::-;791:8:0;38217:26:4;;;;:::i;38051:270::-;38018:303;;37679:657;;38370:38;:::i;37467:63::-;;;;;;;;;;;-1:-1:-1;;;;;37467:63:4;;834:63047;38905:22;;38901:80;;-1:-1:-1;;;;;37360:14:4;791:8:0;;;834:63047:4;;-1:-1:-1;;;;;39146:61:4;834:63047;39147:42;39088:21;834:63047;39147:42;;;:::i;39146:61::-;834:63047;;39225:24;;39221:215;;37460:1431;834:63047;;;39913:83;834:63047;39472:11;834:63047;39501:16;39497:353;;37460:1431;-1:-1:-1;39863:13:4;834:63047;-1:-1:-1;;;;;834:63047:4;;39940:55;39951:12;;:::i;:::-;39965:9;834:63047;;;;;;39940:55;;-1:-1:-1;;;;;834:63047:4;;39940:55;;;;:::i;39913:83::-;39863:147;;;;;;834:63047;;;;;;;;;;;;;;;39863:147;;;;;;:::i;:::-;;;;;;;;;;;40031:39;;;36995:3082;:::o;39497:353::-;39777:57;39557:70;39778:40;39645:25;39557:70;39756:79;39557:70;;;:::i;:::-;39645:25;;;:::i;:::-;834:63047;39791:26;39806:11;834:63047;39791:26;;:::i;39756:79::-;;834:63047;39497:353;;;39221:215;39269:13;834:63047;;;39328:9;834:63047;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;39339:64;;834:63047;;39340:45;;834:63047;39340:45;:::i;39339:64::-;39269:152;;;;;;;834:63047;;;;;;;;;;;;;;;;39269:152;;;;;;:::i;:::-;;;;;;;;;;;39221:215;;;;;39269:152;;;;;834:63047;39269:152;;:::i;:::-;834:63047;;39913:83;39269:152;;37467:63;37517:13;;;;37467:63;;:46;37490:23;;;;;37467:46;;3379:2340:15;834:63047:4;-1:-1:-1;834:63047:4;3642:11:15;834:63047:4;;3694:29:15;834:63047:4;;-1:-1:-1;834:63047:4;;3694:29:15;:::i;:::-;3742:31;;;3738:85;;834:63047:4;;-1:-1:-1;834:63047:4;3970:11:15;834:63047:4;;4022:29:15;834:63047:4;;-1:-1:-1;834:63047:4;;4022:29:15;:::i;:::-;4070:31;;;4066:256;;834:63047:4;;-1:-1:-1;834:63047:4;4468:11:15;834:63047:4;;4520:29:15;834:63047:4;;-1:-1:-1;834:63047:4;;4520:29:15;:::i;:::-;4568:31;;;4564:394;;834:63047:4;5116:29:15;834:63047:4;60643:9;834:63047;5116:29:15;:::i;:::-;5164:31;;;5160:509;;5689:23;60654:16:4;3379:2340:15;:::o;5160:509::-;834:63047:4;-1:-1:-1;834:63047:4;4468:11:15;834:63047:4;;;5342:36:15;834:63047:4;-1:-1:-1;834:63047:4;;5342:36:15;:::i;:::-;834:63047:4;;3543:1:15;834:63047:4;5319:59:15;834:63047:4;-1:-1:-1;834:63047:4;3970:11:15;834:63047:4;;;5480:36:15;834:63047:4;-1:-1:-1;834:63047:4;;5480:36:15;:::i;:::-;834:63047:4;;3543:1:15;834:63047:4;5457:59:15;834:63047:4;-1:-1:-1;834:63047:4;3642:11:15;834:63047:4;;60654:16;834:63047;5617:36:15;834:63047:4;-1:-1:-1;834:63047:4;;5617:36:15;:::i;:::-;834:63047:4;;3543:1:15;834:63047:4;;;5580:74:15;:::o;4564:394::-;834:63047:4;4634:33:15;834:63047:4;-1:-1:-1;834:63047:4;3970:11:15;834:63047:4;;;4769:36:15;834:63047:4;-1:-1:-1;834:63047:4;;4769:36:15;:::i;4066:256::-;834:63047:4;4136:33:15;834:63047:4;-1:-1:-1;834:63047:4;3642:11:15;834:63047:4;;60654:16;834:63047;4270:36:15;834:63047:4;-1:-1:-1;834:63047:4;;4270:36:15;:::i;3738:85::-;834:63047:4;3789:33:15;60654:16:4;834:63047;;3775:48:15:o;3379:2340::-;834:63047:4;3543:1:15;834:63047:4;3573:20:15;834:63047:4;3573:20:15;;3609:8;3605:229;;3379:2340;834:63047:4;;;;;;3890:31:15;834:63047:4;3890:31:15;;3937:8;3933:399;;3379:2340;834:63047:4;;;;;;;;4388:31:15;;4435:8;4431:537;;3379:2340;4990:31;834:63047:4;4990:31:15;;;5037:8;5033:646;;3379:2340;5689:23;834:63047:4;3379:2340:15;:::o;5033:646::-;5116:29;834:63047:4;60643:9;834:63047;5116:29:15;:::i;:::-;5164:31;;;5160:509;5033:646;5160:509;834:63047:4;-1:-1:-1;834:63047:4;5268:11:15;834:63047:4;;;5342:36:15;834:63047:4;-1:-1:-1;834:63047:4;;5342:36:15;:::i;4431:537::-;4520:29;834:63047:4;;-1:-1:-1;834:63047:4;4468:11:15;834:63047:4;;;-1:-1:-1;834:63047:4;;4520:29:15;:::i;:::-;13981:17:0;4568:31:15;;;4564:394;4431:537;4564:394;834:63047:4;;;;;;;;4634:33:15;834:63047:4;-1:-1:-1;834:63047:4;4695:11:15;834:63047:4;;;4769:36:15;834:63047:4;-1:-1:-1;834:63047:4;;4769:36:15;:::i;3933:399::-;4022:29;834:63047:4;;-1:-1:-1;834:63047:4;3970:11:15;834:63047:4;;;-1:-1:-1;834:63047:4;;4022:29:15;:::i;:::-;13981:17:0;4070:31:15;;;4066:256;3933:399;4066:256;834:63047:4;;;;;;;4136:33:15;834:63047:4;-1:-1:-1;834:63047:4;4197:11:15;834:63047:4;;;;4270:36:15;834:63047:4;-1:-1:-1;834:63047:4;;4270:36:15;:::i;3605:229::-;3694:29;834:63047:4;;;;-1:-1:-1;834:63047:4;3642:11:15;834:63047:4;;;-1:-1:-1;834:63047:4;;3694:29:15;:::i;:::-;3742:31;;;3738:85;3605:229;3738:85;834:63047:4;;;;;;;;3775:48:15;-1:-1:-1;3775:48:15:o;6011:2382::-;834:63047:4;;;6287:11:15;834:63047:4;;6339:28:15;834:63047:4;;;;;6339:28:15;:::i;:::-;6386:31;;;6382:85;;834:63047:4;;;;6628:11:15;834:63047:4;;6680:28:15;834:63047:4;;;;;6680:28:15;:::i;:::-;6727:31;;;6723:257;;834:63047:4;;;;7140:11:15;834:63047:4;;7192:28:15;834:63047:4;;;;;7192:28:15;:::i;:::-;7239:31;;;7235:396;;834:63047:4;7803:28:15;834:63047:4;62534:10;834:63047;7803:28:15;:::i;:::-;7850:31;;;7846:512;;8378:8;834:63047:4;6011:2382:15;:::o;7846:512::-;834:63047:4;;;7140:11:15;834:63047:4;;;8028:37:15;834:63047:4;;;;8028:37:15;:::i;:::-;834:63047:4;;6174:1:15;834:63047:4;8005:60:15;834:63047:4;;;6628:11:15;834:63047:4;;;8167:37:15;834:63047:4;;;;8167:37:15;:::i;:::-;834:63047:4;;6174:1:15;834:63047:4;8144:60:15;834:63047:4;;;6287:11:15;834:63047:4;;;;8305:37:15;834:63047:4;;;;8305:37:15;:::i;7235:396::-;834:63047:4;;;6628:11:15;834:63047:4;;;7440:37:15;834:63047:4;;;;7440:37:15;:::i;6723:257::-;834:63047:4;;;6287:11:15;834:63047:4;;;;6927:37:15;834:63047:4;;;;6927:37:15;:::i;6382:85::-;834:63047:4;;6419:48:15;:::o;6011:2382::-;834:63047:4;6174:1:15;834:63047:4;6204:20:15;834:63047:4;6204:20:15;;834:63047:4;6240:22:15;;6236:242;;6011:2382;834:63047:4;;;;;;6534:31:15;834:63047:4;6534:31:15;;834:63047:4;6581:22:15;;6577:413;;6011:2382;834:63047:4;;;;;;;;7046:31:15;;834:63047:4;7093:22:15;;7089:552;;6011:2382;7663:31;834:63047:4;7663:31:15;;;834:63047:4;7710:22:15;;7706:662;;8378:8;-1:-1:-1;6011:2382:15;:::o;7706:662::-;7803:28;834:63047:4;62534:10;834:63047;7803:28:15;:::i;7089:552::-;7192:28;834:63047:4;;-1:-1:-1;834:63047:4;7140:11:15;834:63047:4;;;-1:-1:-1;834:63047:4;;7192:28:15;:::i;:::-;13981:17:0;7239:31:15;;;7235:396;7089:552;7235:396;834:63047:4;;;;;;;;7305:33:15;834:63047:4;-1:-1:-1;834:63047:4;7366:11:15;834:63047:4;;;7440:37:15;834:63047:4;-1:-1:-1;834:63047:4;;7440:37:15;:::i;6577:413::-;6680:28;834:63047:4;;-1:-1:-1;834:63047:4;6628:11:15;834:63047:4;;;-1:-1:-1;834:63047:4;;6680:28:15;:::i;:::-;13981:17:0;6727:31:15;;;6723:257;6577:413;6723:257;834:63047:4;;;;;;;6793:33:15;834:63047:4;-1:-1:-1;834:63047:4;6854:11:15;834:63047:4;;;;6927:37:15;834:63047:4;-1:-1:-1;834:63047:4;;6927:37:15;:::i;6236:242::-;6339:28;834:63047:4;;;;-1:-1:-1;834:63047:4;6287:11:15;834:63047:4;;;-1:-1:-1;834:63047:4;;6339:28:15;:::i;:::-;6386:31;;;6382:85;6236:242;6382:85;834:63047:4;;;;;;;;6419:48:15;-1:-1:-1;6419:48:15:o;24962:1301:4:-;25078:11;;;834:63047;;24962:1301;;;834:63047;;25078:35;834:63047;;;;;;;;25133:8;834:63047;;;;25133:26;25078:11;834:63047;;25133:26;834:63047;;;;;;25133:38;;;25129:88;;25234:12;;;834:63047;;;25427:12;;834:63047;25234:12;;25427;834:63047;;;791:8:0;25410:16:4;834:63047;791:8:0;834:63047:4;25078:11;834:63047;791:8:0;834:63047:4;;25467:16;;;:49;;;;;25230:628;25463:107;;;834:63047;25587:12;:::o;25467:49::-;25487:29;;;25467:49;;;25230:628;25671:12;834:63047;;;791:8:0;25653:17:4;834:63047;791:8:0;834:63047:4;25078:11;834:63047;791:8:0;834:63047:4;;25711:16;;;:49;;;;;25707:107;;;834:63047;25831:12;:::o;25129:88::-;25191:11;;25133:26;25191:11;:::o;25074:1183::-;-1:-1:-1;25892:12:4;;;834:63047;;;25945:12;;834:63047;25892:12;;25945;834:63047;;;791:8:0;25928:16:4;791:8:0;;834:63047:4;791:8:0;25078:11:4;834:63047;791:8:0;834:63047:4;;;;25928:47;25924:105;;834:63047;26046:12;:::o;25888:359::-;26119:12;834:63047;;;791:8:0;26101:17:4;791:8:0;;834:63047:4;791:8:0;25078:11:4;834:63047;791:8:0;834:63047:4;;;;26101:48;26097:106;;834:63047;26220:12;:::o;22352:2115::-;;22475:11;;;834:63047;;;-1:-1:-1;834:63047:4;;22475:35;22471:106;;22352:2115;22590:11;;;;834:63047;;;;;22590:35;22586:106;;22352:2115;-1:-1:-1;22706:12:4;;;834:63047;;22706:12;;834:63047;;22706:12;;22872;;;834:63047;;;;;791:8:0;;22855:16:4;791:8:0;;834:63047:4;22475:11;791:8:0;;834:63047:4;;;;;22843:47;22839:528;834:63047;;;;;;;;791:8:0;;22855:16:4;791:8:0;;22910:71:4;834:63047;22475:11;791:8:0;;834:63047:4;;;22910:71;;;:::i;:::-;23003:35;22999:228;;22839:528;23381:13;834:63047;;;23460:10;834:63047;791:8:0;23498:11:4;;834:63047;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;23498:26;;834:63047;;;-1:-1:-1;;;;;834:63047:4;23498:26;:::i;:::-;23528:13;791:8:0;;-1:-1:-1;;;;;791:8:0;;;834:63047:4;;;;;;23491:76;:51;-1:-1:-1;;;;;23490:95:4;834:63047;;;;;;;;23491:51;:::i;:76::-;791:8:0;;;834:63047:4;23490:95;;:::i;:::-;23381:242;;;;;;834:63047;23381:242;834:63047;;;;;22475:11;834:63047;;;;;;;;;;;23381:242;;;;;;:::i;:::-;;;;;;;;;;;22839:528;22702:1658;;;24420:12;22702:1658;24375:85;22702:1658;;834:63047;;;;;;;;24420:12;-1:-1:-1;;;;;24434:11:4;834:63047;24420:12;;;834:63047;;24434:11;;834:63047;;;;;;;;22475:11;834:63047;;;;;24434:11;834:63047;;;22475:11;834:63047;;;22590:11;834:63047;;;;;;;24375:85;22352:2115::o;23381:242::-;;;;;;:::i;:::-;834:63047;;23381:242;;;;834:63047;;;;-1:-1:-1;;;834:63047:4;;;;;;791:8:0;834:63047:4;22999:228;23168:40;834:63047;;;;23168:40;:::i;:::-;;22999:228;;22839:528;834:63047;;;;;791:8:0;;22855:16:4;791:8:0;;834:63047:4;791:8:0;22475:11:4;791:8:0;;834:63047:4;;;;;;1005:12:14;1001:60;;22839:528:4;;;;;1001:60:14;1033:17;;;:::i;:::-;1001:60;;;;22702:1658:4;23688:12;;;;;;;;;834:63047;;;;;-1:-1:-1;791:8:0;23670:17:4;791:8:0;;834:63047:4;22475:11;-1:-1:-1;791:8:0;834:63047:4;;;;;23658:48;23654:532;834:63047;;;;;;;;-1:-1:-1;791:8:0;23670:17:4;791:8:0;;23726:72:4;834:63047;22475:11;-1:-1:-1;791:8:0;834:63047:4;;;23726:72;;;:::i;:::-;23820:35;23816:229;;23654:532;;;24200:13;834:63047;;;24263:9;834:63047;791:8:0;24276:11:4;;834:63047;24290:21;834:63047;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;24275:53;;24276:35;;834:63047;-1:-1:-1;;;;;834:63047:4;24276:35;:::i;24275:53::-;24200:149;;;;;;;834:63047;-1:-1:-1;834:63047:4;;22475:11;834:63047;;;;;;;;;;;24200:149;;;;;;:::i;:::-;;;;;;;;;;24375:85;24200:149;24420:12;24200:149;;;23654:532;22702:1658;;;24200:149;-1:-1:-1;24200:149:4;;;:::i;:::-;-1:-1:-1;24200:149:4;;23816:229;834:63047;23985:41;834:63047;;;23985:41;:::i;:::-;;23816:229;;;23654:532;834:63047;;;;;-1:-1:-1;791:8:0;23670:17:4;791:8:0;;834:63047:4;791:8:0;22475:11:4;-1:-1:-1;791:8:0;834:63047:4;;;;;;1005:12:14;1001:60;;23654:532:4;;;;;1001:60:14;1033:17;;;:::i;:::-;1001:60;;;;22586:106:4;834:63047;;;;;-1:-1:-1;834:63047:4;22641:8;834:63047;;;22641:26;22475:11;-1:-1:-1;834:63047:4;22641:26;834:63047;;;791:8:0;;;;;;;22586:106:4;;;22471;22526:40;22555:11;834:63047;22555:11;;;834:63047;;;-1:-1:-1;834:63047:4;22526:8;834:63047;;22526:26;22475:11;-1:-1:-1;834:63047:4;22526:26;:40;:::i;:::-;22471:106;;;26444:516:13;26582:372;;;;;;;;;;;891:5:0;26582:372:13;;;;891:5:0;26582:372:13;;;;;;;;26444:516;:::o;:::-;26582:372;;;;;;;;;;;791:8:0;26582:372:13;;;;791:8:0;26582:372:13;;;;;;;;26444:516;:::o;:::-;26582:372;;;;26444:516;26582:372;;;;;;;;;;;;;;;;;;;;26444:516;:::o;14534:155:0:-;;-1:-1:-1;;;;;834:63047:4;;14617:30:0;;;834:63047:4;;14534:155:0:o;834:63047:4:-;;;;;;;;;754:1012:15;834:63047:4;875:1:15;834:63047:4;;;;;;;;905:11:15;834:63047:4;;;;;;975:20:15;969:1;834:63047:4;975:20:15;;834:63047:4;952:45:15;;1012:19;;;;1008:729;;1747:12;;;;;834:63047:4;754:1012:15;:::o;1008:729::-;834:63047:4;;905:11:15;834:63047:4;;;;;;1095:11:15;1091:610;;1715:11;;969:1;1715:11;:::o;1091:610::-;834:63047:4;;;;;;;;;;1177:11:15;834:63047:4;;;;;;;969:1:15;834:63047:4;1256:31:15;;;834:63047:4;;;;1233:56:15;;834:63047:4;;;1091:610:15;1308:379;834:63047:4;;;;;;;;;;1402:11:15;834:63047:4;;;;;;;969:1:15;1485:31;;;;834:63047:4;;;;1462:56:15;;834:63047:4;;;1545:11:15;1541:128;;1308:379;;1091:610;;1541:128;834:63047:4;969:1:15;1613:31;;834:63047:4;16623:10;834:63047;1584:62:15;16623:10:4;834:63047;1541:128:15;;;754:1012;834:63047:4;875:1:15;834:63047:4;;;;;;;;905:11:15;834:63047:4;;;;;;975:20:15;969:1;834:63047:4;975:20:15;;834:63047:4;952:45:15;;1012:19;;;;1008:729;;1747:12;;;;;834:63047:4;754:1012:15;:::o;1008:729::-;834:63047:4;;905:11:15;834:63047:4;;;;;;1095:11:15;1091:610;;1715:11;;969:1;1715:11;:::o;1091:610::-;834:63047:4;;;;;;;;;;1177:11:15;834:63047:4;;;;;;;969:1:15;834:63047:4;1256:31:15;;;834:63047:4;;;;1233:56:15;;834:63047:4;;;1091:610:15;1308:379;834:63047:4;;;;;;;;;;1402:11:15;834:63047:4;;;;;;;969:1:15;1485:31;;;;834:63047:4;;;;1462:56:15;;834:63047:4;;;1545:11:15;1541:128;;1308:379;1091:610;;1541:128;834:63047:4;969:1:15;1613:31;;834:63047:4;16561:9;834:63047;1584:62:15;16561:9:4;834:63047;1541:128:15;;;14373:155:0;;834:63047:4;;;14456:30:0;;;834:63047:4;;14373:155:0:o;834:63047:4:-;;;;;;;;;47172:1014;;834:63047;;;;;;47456:525;;-1:-1:-1;;;;;834:63047:4;;47463:9;;;;:45;;;47456:525;47463:45;;;834:63047;;;;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;49286:23;;;834:63047;;;;;;;;;;;;;;;;;49501:47;-1:-1:-1;834:63047:4;;;49553:47;;;;:::i;:::-;49500:105;;-1:-1:-1;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;49664:31;49660:200;;;;49500:105;834:63047;;;-1:-1:-1;834:63047:4;49286:8;834:63047;;;49286:23;834:63047;-1:-1:-1;834:63047:4;49873:31;834:63047;;;;49873:36;49869:345;;49500:105;50224:221;;49500:105;834:63047;-1:-1:-1;834:63047:4;49286:8;834:63047;;;49286:23;834:63047;-1:-1:-1;834:63047:4;50459:31;834:63047;;;;50455:265;;49500:105;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;;;;;50908:24;;;;834:63047;-1:-1:-1;;;;;834:63047:4;;;;;791:8:0;834:63047:4;;;;;;791:8:0;;;;-1:-1:-1;834:63047:4;;;;50907:25;;791:8:0;;;;;;;;;;47931:39:4;791:8:0;51067:52:4;791:8:0;50946:65:4;51067:52;791:8:0;834:63047:4;50997:14;791:8:0;;;834:63047:4;50946:65;;:::i;:::-;51067:52;;:::i;:::-;;;:::i;:::-;47742:60;47931:39;;:::i;:::-;47456:525;;;50455:265;50577:52;;;;:::i;:::-;-1:-1:-1;;;;;834:63047:4;;;;49286:23;50647:48;;;834:63047;-1:-1:-1;834:63047:4;49286:8;834:63047;;;49286:23;834:63047;-1:-1:-1;834:63047:4;51282:28;834:63047;;;;51282:52;51278:1790;834:63047;;;;;-1:-1:-1;834:63047:4;49286:8;834:63047;;;-1:-1:-1;834:63047:4;;49286:23;834:63047;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;50997:14;834:63047;51464:20;834:63047;;;51464:20;:::i;:::-;834:63047;;;;791:8:0;;834:63047:4;791:8:0;;;50997:14:4;791:8:0;834:63047:4;-1:-1:-1;834:63047:4;49286:8;834:63047;;;49286:23;834:63047;-1:-1:-1;834:63047:4;51581:24;834:63047;;;;51580:25;51576:419;834:63047;;;51639:78;51633:85;834:63047;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;51639:78;:::i;51633:85::-;834:63047;;;;;-1:-1:-1;791:8:0;51780:16:4;834:63047;791:8:0;834:63047:4;791:8:0;51751:87:4;791:8:0;834:63047:4;-1:-1:-1;791:8:0;51751:87:4;:::i;:::-;51576:419;;52012:8;;52008:90;;51576:419;834:63047;;;;;;;;;;;;;;52320:41;;;;;-1:-1:-1;52320:41:4;;834:63047;;;17851:131;;;;17877:31;;;:::i;:::-;;17851:131;834:63047;;;;;;:::i;:::-;;;;;18024:99;;834:63047;-1:-1:-1;;;;;834:63047:4;;;;;18024:99;;834:63047;;;;;;;;18024:99;;-1:-1:-1;834:63047:4;;;18024:99;;834:63047;;;;;;;18024:99;;834:63047;;;;;18024:99;;834:63047;;;;;18024:99;;834:63047;;;;-1:-1:-1;834:63047:4;49286:8;834:63047;;;-1:-1:-1;834:63047:4;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;891:5:0;;;;;;;;834:63047:4;-1:-1:-1;;;;;834:63047:4;;;;;:::i;:::-;49286:23;834:63047;;;;;;;;791:8:0;;;;;;;834:63047:4;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;834:63047:4;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;;;;;-1:-1:-1;;;;;;;;834:63047:4;;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;18133:38;;834:63047;;-1:-1:-1;18133:27:4;:38;:::i;:::-;834:63047;;18186:87;;;;;;;:::i;:::-;;;;;50455:265;;17851:131;17939:32;;;:::i;:::-;;17851:131;;52320:41;;;;;52008:90;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;49286:23;52040:28;834:63047;;-1:-1:-1;;;;834:63047:4;;;;;-1:-1:-1;;;834:63047:4;;;;52008:90;;51576:419;834:63047;;;;;-1:-1:-1;791:8:0;51921:17:4;834:63047;791:8:0;834:63047:4;791:8:0;51892:88:4;791:8:0;834:63047:4;-1:-1:-1;791:8:0;51892:88:4;:::i;:::-;51576:419;;;51278:1790;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;;;49286:23;52523:28;834:63047;;;;;;;;;52899:44;;52789:35;;834:63047;;;;52569:25;52565:189;;51278:1790;834:63047;;-1:-1:-1;834:63047:4;49286:8;834:63047;;;-1:-1:-1;834:63047:4;;;;52789:35;:::i;:::-;834:63047;-1:-1:-1;834:63047:4;49286:8;834:63047;;52838:42;834:63047;;-1:-1:-1;834:63047:4;52838:42;:::i;:::-;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;52899:44;52957:101;;51278:1790;;50455:265;;52957:101;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;49286:23;52988:32;834:63047;;-1:-1:-1;;;;834:63047:4;;;52957:101;;52565:189;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;49286:23;52680:24;834:63047;52642:97;;-1:-1:-1;52648:90:4;;834:63047;;;;;;;;;;;;-1:-1:-1;;;;;834:63047:4;52648:90;:::i;52642:97::-;52565:189;;;50647:48;-1:-1:-1;50647:48:4;;;50224:221;-1:-1:-1;;;;;834:63047:4;;50275:92;;50224:221;834:63047;-1:-1:-1;834:63047:4;49286:8;834:63047;;50380:54;834:63047;;-1:-1:-1;834:63047:4;50380:54;:::i;:::-;50224:221;;50275:92;50332:20;;;50275:92;;;49869:345;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;49286:23;49971:24;;834:63047;;;;;55170:78;;-1:-1:-1;;;;;834:63047:4;;;;;;49970:25;50062:85;;;;;;;834:63047;-1:-1:-1;834:63047:4;49286:8;834:63047;;;49286:23;834:63047;-1:-1:-1;834:63047:4;50165:24;834:63047;;;;54387:37;;;;;;54402:10;834:63047;-1:-1:-1;;;;;834:63047:4;;54387:37;54463:105;;;;54478:38;834:63047;-1:-1:-1;;;;;834:63047:4;;54478:38;:::i;:::-;54463:105;;54664:320;;;;834:63047;891:5:0;54743:63:4;54744:47;54745:29;834:63047;;;;;54707:9;834:63047;;;-1:-1:-1;;;;;54753:21:4;834:63047;;;54745:29;:::i;54744:47::-;54795:11;834:63047;54743:63;;:::i;:::-;791:8:0;54664:320:4;;55048:13;55044:109;;54664:320;834:63047;;55195:52;;834:63047;;;;55195:52;;;834:63047;55195:52;;;:::i;55170:78::-;49869:345;;;55044:109;55195:52;834:63047;;;55090:52;55195;834:63047;55090:52;834:63047;;55090:52;;;;834:63047;55090:52;;;:::i;:::-;;63742:131;;55090:52;;;;;;:::i;:::-;55044:109;;;54664:320;54866:10;834:63047;-1:-1:-1;;;;;834:63047:4;;891:5:0;;54903:52:4;;:38;;834:63047;-1:-1:-1;;;;;834:63047:4;54903:38;:::i;:52::-;791:8:0;54664:320:4;;;54463:105;54520:47;54521:29;54529:21;834:63047;-1:-1:-1;;;;;834:63047:4;;54521:29;:::i;54520:47::-;54463:105;;;54387:37;54415:9;834:63047;-1:-1:-1;;;;;834:63047:4;;54387:37;;50062:85;834:63047;-1:-1:-1;834:63047:4;49286:8;834:63047;;;-1:-1:-1;834:63047:4;;;;50062:85;;;49660:200;49802:47;;;;;:::i;:::-;49660:200;;;;;49500:105;-1:-1:-1;49500:105:4;;;47463:45;;;;834:63047;47463:45;;;;48030:8;47463:45;48030:8;:::i;:::-;834:63047;48053:32;48049:93;;48151:28;;47172:1014;:::o;48049:93::-;48101:30;;44752:9;48101:30;:::i;:::-;;48151:28;47172:1014;:::o;47463:45::-;834:63047;;;;47476:32;;47463:45;;47172:1014;;834:63047;;;;;;47456:525;;-1:-1:-1;;;;;834:63047:4;;47463:9;;;;:45;;;47456:525;47463:45;;;834:63047;;;;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;49286:23;;;834:63047;;;;;;;;;;;;;;;;;49501:47;-1:-1:-1;834:63047:4;;;49553:47;;;;:::i;:::-;49500:105;;-1:-1:-1;834:63047:4;;-1:-1:-1;;;;;834:63047:4;;49664:31;49660:200;;;;49500:105;834:63047;;;-1:-1:-1;834:63047:4;49286:8;834:63047;;;49286:23;834:63047;-1:-1:-1;834:63047:4;49873:31;834:63047;;;;49873:36;49869:345;;49500:105;50224:221;;49500:105;834:63047;-1:-1:-1;834:63047:4;49286:8;834:63047;;;49286:23;834:63047;-1:-1:-1;834:63047:4;50459:31;834:63047;;;;50455:265;;49500:105;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;;;;;50908:24;;;;834:63047;-1:-1:-1;;;;;834:63047:4;;;;;791:8:0;834:63047:4;;;;;;791:8:0;;;;-1:-1:-1;834:63047:4;;;;50907:25;;791:8:0;;;;;;;;;;47931:39:4;791:8:0;51067:52:4;791:8:0;50946:65:4;51067:52;791:8:0;834:63047:4;50997:14;791:8:0;;;834:63047:4;50946:65;;:::i;47931:39::-;47456:525;;;50455:265;50577:52;;;;:::i;:::-;-1:-1:-1;;;;;834:63047:4;;;;49286:23;50647:48;;;834:63047;-1:-1:-1;834:63047:4;49286:8;834:63047;;;49286:23;834:63047;-1:-1:-1;834:63047:4;51282:28;834:63047;;;;51282:52;51278:1790;834:63047;;;;;-1:-1:-1;834:63047:4;49286:8;834:63047;;;-1:-1:-1;834:63047:4;;49286:23;834:63047;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;50997:14;834:63047;51464:20;834:63047;;;51464:20;:::i;:::-;834:63047;;;;791:8:0;;834:63047:4;791:8:0;;;50997:14:4;791:8:0;834:63047:4;-1:-1:-1;834:63047:4;49286:8;834:63047;;;49286:23;834:63047;-1:-1:-1;834:63047:4;51581:24;834:63047;;;;51580:25;51576:419;834:63047;;;51639:78;51633:85;834:63047;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;51639:78;:::i;51633:85::-;834:63047;;;;;-1:-1:-1;791:8:0;51780:16:4;834:63047;791:8:0;834:63047:4;791:8:0;51751:87:4;791:8:0;834:63047:4;-1:-1:-1;791:8:0;51751:87:4;:::i;:::-;51576:419;;52012:8;;52008:90;;51576:419;834:63047;;;;;;;;;;;;;;52320:41;;;;;-1:-1:-1;52320:41:4;;834:63047;;;17851:131;;;;17877:31;;;:::i;:::-;;17851:131;834:63047;;;;;;:::i;:::-;;;;;18024:99;;834:63047;-1:-1:-1;;;;;834:63047:4;;;;;18024:99;;834:63047;;;;;;;;18024:99;;-1:-1:-1;834:63047:4;;;18024:99;;834:63047;;;;;;;18024:99;;834:63047;;;;;18024:99;;834:63047;;;;;18024:99;;834:63047;;;;-1:-1:-1;834:63047:4;49286:8;834:63047;;;-1:-1:-1;834:63047:4;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;891:5:0;;;;;;;;834:63047:4;-1:-1:-1;;;;;834:63047:4;;;;;:::i;:::-;49286:23;834:63047;;;;;;;;791:8:0;;;;;;;834:63047:4;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;834:63047:4;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;;;;;-1:-1:-1;;;;;;;;834:63047:4;;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;;;;-1:-1:-1;;;834:63047:4;;;;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;18133:38;;834:63047;;-1:-1:-1;18133:27:4;:38;:::i;:::-;834:63047;;18186:87;;;;;;;:::i;:::-;;;;;50455:265;;17851:131;17939:32;;;:::i;:::-;;17851:131;;52320:41;;;;;52008:90;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;49286:23;52040:28;834:63047;;-1:-1:-1;;;;834:63047:4;;;;;-1:-1:-1;;;834:63047:4;;;;52008:90;;51576:419;834:63047;;;;;-1:-1:-1;791:8:0;51921:17:4;834:63047;791:8:0;834:63047:4;791:8:0;51892:88:4;791:8:0;834:63047:4;-1:-1:-1;791:8:0;51892:88:4;:::i;:::-;51576:419;;;51278:1790;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;;;49286:23;52523:28;834:63047;;;;;;;;;52899:44;;52789:35;;834:63047;;;;52569:25;52565:189;;834:63047;;-1:-1:-1;834:63047:4;49286:8;834:63047;;;-1:-1:-1;834:63047:4;;;;52789:35;:::i;:::-;834:63047;-1:-1:-1;834:63047:4;49286:8;834:63047;;52838:42;834:63047;;-1:-1:-1;834:63047:4;52838:42;:::i;:::-;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;52899:44;52957:101;;51278:1790;;50455:265;;52957:101;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;49286:23;52988:32;834:63047;;-1:-1:-1;;;;834:63047:4;;;52957:101;;50647:48;-1:-1:-1;50647:48:4;;;50224:221;-1:-1:-1;;;;;834:63047:4;;50275:92;;50224:221;834:63047;-1:-1:-1;834:63047:4;49286:8;834:63047;;50380:54;834:63047;;-1:-1:-1;834:63047:4;50380:54;:::i;:::-;50224:221;;50275:92;50332:20;;;50275:92;;;49869:345;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;49286:23;49971:24;;834:63047;;;;;55170:78;;-1:-1:-1;;;;;834:63047:4;;;;;;49970:25;50062:85;;;;-1:-1:-1;834:63047:4;;;49286:8;834:63047;;;;;49286:23;50165:24;834:63047;;;;;;50062:85;54387:37;;;;54402:10;834:63047;-1:-1:-1;;;;;834:63047:4;;54463:105;;;;54478:38;834:63047;-1:-1:-1;;;;;834:63047:4;;54478:38;:::i;55170:78::-;49869:345;;;49660:200;49802:47;;;;;:::i;:::-;49660:200;;;;;49500:105;-1:-1:-1;49500:105:4;;;47463:45;;;;834:63047;47463:45;;;;48030:8;47463:45;48030:8;:::i;:::-;834:63047;48053:32;48049:93;;48151:28;;47172:1014;:::o;48049:93::-;48101:30;;41437:10;48101:30;:::i;47463:45::-;834:63047;;;;47476:32;;47463:45;;25862:482:13;25998:340;;;;;;;;;;;;;;;;;25862:482;:::o;57616:286:4:-;57734:16;57699:52;;:::i;:::-;834:63047;57734:16;57765:30;;57761:117;;57887:8;834:63047;57616:286;:::o;57761:117::-;791:8:0;;;;;;;;;;;;;;;57818:49:4;791:8:0;57734:16:4;57853:14;791:8:0;;;834:63047:4;57818:49;;:::i;57341:269::-;834:63047;57423:37;;:::i;:::-;834:63047;57474:14;;;;57470:100;;13981:17:0;;;;57341:269:4;:::o;57470:100::-;791:8:0;;;;;;;;;;;;57511:48:4;791:8:0;834:63047:4;57545:14;791:8:0;;;834:63047:4;57511:48;;:::i;8726:182:15:-;-1:-1:-1;;834:63047:4;;;;;;;;;;;;;828:6:10;;;13981:17:0;;;;8726:182:15;:::o;827:60:10:-;858:21;834:63047:4;858:21:10;;:::i;:::-;834:63047:4;;8726:182:15;:::o;1712:971:10:-;;834:63047:4;1795:882:10;;;;;;1712:971;1795:882;-1:-1:-1;;;;;1795:882:10;;;;;1712:971;1795:882;;;;;1712:971;1795:882;;;;;1712:971;1795:882;;;;;1712:971;1795:882;;;;;1712:971;1795:882;;;;;1712:971;1795:882;;;1712:971::o;1795:882::-;;;;;1712:971::o;1795:882::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1795:882:10;;;;9243:180:15;9404:1;834:63047:4;;;;;;;;;1410:6:10;;;13981:17:0;;;;9243:180:15;:::o;1409:59:10:-;1440:22;834:63047:4;1440:22:10;;:::i;:::-;834:63047:4;;9243:180:15;:::o;2914:1171:10:-;834:63047:4;2998:1081:10;;;;;;;2914:1171;2998:1081;;;;;;;2914:1171;2998:1081;;;;;;;2914:1171;2998:1081;;;;;;;2914:1171;2998:1081;;;;;;;2914:1171;2998:1081;;;;;;;2914:1171;2998:1081;;;;;;;2914:1171;2998:1081;;;;;2914:1171;2998:1081;;2914:1171;:::o;2998:1081::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1257:212:14;;834:63047:4;1257:212:14;834:63047:4;;;1342:15:14;1338:94;;1257:212;834:63047:4;;791:8:0;;;;;;;1257:212:14:o;1338:94::-;791:8:0;;;;;-1:-1:-1;;834:63047:4;;;1338:94:14;;2034:1040:15;834:63047:4;2158:1:15;834:63047:4;;;;;;;;2188:11:15;834:63047:4;;;;;;2259:20:15;2253:1;834:63047:4;2259:20:15;;834:63047:4;2244:37:15;2235:46;;2296:19;;;2292:753;;3055:12;;;;834:63047:4;2034:1040:15;:::o;2292:753::-;834:63047:4;;2188:11:15;834:63047:4;;;;;;;2379:14:15;2375:634;;3023:11;;2253:1;3023:11;:::o;2375:634::-;834:63047:4;;;;;;;;;2467:11:15;834:63047:4;;2253:1:15;834:63047:4;;;;;2502:31:15;;834:63047:4;2487:48:15;2467:68;834:63047:4;;;2467:11:15;834:63047:4;;;;;;;2375:634:15;2602:393;834:63047:4;;;;;;;;;2702:11:15;834:63047:4;;2253:1:15;834:63047:4;;;;;2737:31:15;;834:63047:4;2722:48:15;2702:68;834:63047:4;;;2702:11:15;834:63047:4;;;;;;;2849:14:15;2845:132;;2602:393;2375:634;;2845:132;834:63047:4;2253:1:15;2921:31;;834:63047:4;2906:48:15;24001:10:4;834:63047;2891:63:15;24001:10:4;834:63047;2845:132:15;;;2034:1040;834:63047:4;2158:1:15;834:63047:4;;;;;;;;2188:11:15;834:63047:4;;;;;;2259:20:15;2253:1;834:63047:4;2259:20:15;;834:63047:4;2244:37:15;2235:46;;2296:19;;;2292:753;;3055:12;;;;834:63047:4;2034:1040:15;:::o;2292:753::-;834:63047:4;;2188:11:15;834:63047:4;;;;;;;2379:14:15;2375:634;;3023:11;;2253:1;3023:11;:::o;2375:634::-;834:63047:4;;;;;;;;;2467:11:15;834:63047:4;;2253:1:15;834:63047:4;;;;;2502:31:15;;834:63047:4;2487:48:15;2467:68;834:63047:4;;;2467:11:15;834:63047:4;;;;;;;2375:634:15;2602:393;834:63047:4;;;;;;;;;2702:11:15;834:63047:4;;2253:1:15;834:63047:4;;;;;2737:31:15;;834:63047:4;2722:48:15;2702:68;834:63047:4;;;2702:11:15;834:63047:4;;;;;;;2849:14:15;2845:132;;2602:393;2375:634;;2845:132;834:63047:4;2253:1:15;2921:31;;834:63047:4;2906:48:15;23184:9:4;834:63047;2891:63:15;23184:9:4;834:63047;2845:132:15;;;2034:1040;;834:63047:4;2158:1:15;834:63047:4;;;;;2188:11:15;;;834:63047:4;;;;;;;;;;2259:20:15;2253:1;834:63047:4;2259:20:15;;834:63047:4;2244:37:15;2235:46;;2296:19;;;2292:753;;3055:12;;;;;;834:63047:4;2034:1040:15;:::o;2292:753::-;834:63047:4;;;;;;;;;2379:14:15;2375:634;;2292:753;3023:11;;;2253:1;3023:11;:::o;2375:634::-;834:63047:4;;;;;2467:11:15;;;;834:63047:4;;;;;;;2253:1:15;834:63047:4;;;;;2502:31:15;;834:63047:4;2487:48:15;2467:68;834:63047:4;;;;;;;;;;;2375:634:15;2602:393;834:63047:4;;;;;;;;;;2253:1:15;2702:11;;;834:63047:4;;;;;;;;2737:31:15;;;;834:63047:4;2722:48:15;2702:68;;;834:63047:4;;;;2849:14:15;2845:132;;2602:393;;2375:634;;2845:132;834:63047:4;2253:1:15;2921:31;;834:63047:4;2906:48:15;834:63047:4;;2891:63:15;834:63047:4;;2845:132:15;;;;53080:310:4;-1:-1:-1;;;;;;;;;;;;;;;;53080:310:4;834:63047;53080:310;53347:12;;:::i;:::-;834:63047;;;;;;;-1:-1:-1;;;;;834:63047:4;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;53361:9;834:63047;;;;;;;;;53291:92;53080:310::o;:::-;;-1:-1:-1;;;;;;;;;;;53080:310:4;-1:-1:-1;;;;;53080:310:4;;834:63047;53080:310;53347:12;;:::i;:::-;834:63047;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;53361:9;834:63047;;;;;;;;;53291:92;53080:310::o;:::-;-1:-1:-1;;;;;;;;;;;;;;;;53080:310:4;834:63047;53080:310;53347:12;;:::i;:::-;834:63047;;;;;;;;;;;;;;;;;2502:22:0;834:63047:4;;;;;;;;;;;;;;;;;;;;;;;53361:9;834:63047;;;;;;;;;53291:92;53080:310::o;:::-;;-1:-1:-1;;;;;;;;;;;53080:310:4;-1:-1:-1;;;;;53080:310:4;;834:63047;53080:310;53347:12;;:::i;:::-;834:63047;;;;;;;;;;;;;;;;;2502:22:0;834:63047:4;;;;;;;;;;;;;;;;;;;;;;;53361:9;834:63047;;;;;;;;;53291:92;53080:310::o;:::-;;;-1:-1:-1;;;;;834:63047:4;53080:310;-1:-1:-1;;;;;;;;;;;53080:310:4;;;;53347:12;;:::i;:::-;834:63047;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;53361:9;834:63047;;;;;;;;;53291:92;53080:310::o;11029:614:0:-;;;834:63047:4;;;;;11225:12:0;834:63047:4;;;11201:137:0;791:8;11257:43;834:63047:4;;;;;56496:10;834:63047;;;11270:30:0;56276:18:4;834:63047;11270:30:0;:::i;11257:43::-;791:8;834:63047:4;11201:137:0;834:63047:4;;11201:137:0;;;;;;;;:::i;:::-;891:5;11381:87;11382:65;:43;11188:150;834:63047:4;-1:-1:-1;;;;;11396:29:0;834:63047:4;;11396:29:0;:::i;11381:87::-;791:8;11500:13;;11496:141;;11029:614;;;:::o;11496:141::-;11542:84;834:63047:4;;;11567:58:0;834:63047:4;11567:58:0;834:63047:4;;;;;56390:9;834:63047;;;;;11567:58:0;;;11201:137;11567:58;;;:::i;55418:195:4:-;55532:14;55418:195;834:63047;55530:76;55418:195;834:63047;55532:14;:::i;:::-;834:63047;55530:59;55531:32;55550:13;791:8:0;;-1:-1:-1;;;;;791:8:0;;;834:63047:4;55531:32;;:::i;:::-;55567:22;834:63047;55530:59;;:::i", "linkReferences": {}, "immutableReferences": {"61731": [{"start": 5194, "length": 32}, {"start": 5410, "length": 32}]}}, "methodIdentifiers": {"SPREAD_CONSTANT()": "a68ee881", "addBuyOrder(uint32,uint96,bool)": "a09e9040", "addFlipBuyOrder(uint32,uint32,uint96,bool)": "4792b6ea", "addFlipSellOrder(uint32,uint32,uint96,bool)": "6d1016b0", "addPairedLiquidity(uint32,uint32,uint96,uint96)": "2e2822b7", "addSellOrder(uint32,uint96,bool)": "40e79b1b", "batchAddPairedLiquidity(uint32[],uint32[],uint96[],uint96[])": "2819fc0d", "batchCancelFlipOrders(uint40[])": "ba42d8bd", "batchCancelOrders(uint40[])": "23afbff3", "batchCancelOrdersNoRevert(uint40[])": "43c1eed6", "batchProvisionLiquidity(uint32[],uint32[],uint96[],bool[],bool)": "cca97a3c", "batchUpdate(uint32[],uint96[],uint32[],uint96[],uint40[],bool)": "5339c59f", "bestBidAsk()": "b4de8b70", "collectFees()": "c8796572", "getL2Book()": "46fdfbb1", "getL2Book(uint32,uint32)": "e7e10737", "getMarketParams()": "90c9427c", "getVaultParams()": "88bb4f60", "initialize(address,uint8,address,uint256,address,uint256,address,uint96,uint32,uint32,uint96,uint96,uint256,uint256,address,uint96,address)": "c827fdcc", "isTrustedForwarder(address)": "572b6c05", "kuruAmmVault()": "a65ddd09", "marketState()": "08fb1b77", "placeAndExecuteMarketBuy(uint96,uint256,bool,bool)": "7c51d6cf", "placeAndExecuteMarketSell(uint96,uint256,bool,bool)": "532c46db", "proxiableUUID()": "52d1902d", "s_buyPricePoints(uint256)": "e6d29b51", "s_buyTree()": "badc92d1", "s_orderIdCounter()": "2bf1360e", "s_orders(uint40)": "1c0d9c22", "s_sellPricePoints(uint256)": "0e2e2ffe", "s_sellTree()": "02b90c65", "toggleMarket(uint8)": "732295ca", "transferOwnership(address)": "f2fde38b", "updateVaultOrdSz(uint96,uint96,uint256,uint256,bool)": "27688f0f", "upgradeToAndCall(address,bytes)": "4f1ef286", "vaultAskOrderSize()": "51130ac6", "vaultBestAsk()": "474b848c"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"InsufficientLiquidity\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidSpread\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LengthMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MarketFeeError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MarketSizeError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MarketStateError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NativeAssetInsufficient\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NativeAssetNotRequired\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NativeAssetSurplus\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OnlyOwnerAllowedError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OnlyVaultAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OrderAlreadyFilledOrCancelled\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PostOnlyError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PriceError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ProvisionError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SizeError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SlippageExceeded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TickSizeError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Uint32Overflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Uint96Overflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnauthorizedCallContext\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UpgradeFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"WrongOrderTypeCancel\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"orderId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"flippedId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"size\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"price\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"flippedPrice\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isBuy\",\"type\":\"bool\"}],\"name\":\"FlipOrderCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"orderId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"size\",\"type\":\"uint96\"}],\"name\":\"FlipOrderUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40[]\",\"name\":\"orderIds\",\"type\":\"uint40[]\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"FlipOrdersCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"orderId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"flippedId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"size\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"price\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"flippedPrice\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isBuy\",\"type\":\"bool\"}],\"name\":\"FlippedOrderCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"enum IOrderBook.MarketState\",\"name\":\"previousState\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"enum IOrderBook.MarketState\",\"name\":\"newState\",\"type\":\"uint8\"}],\"name\":\"MarketStateUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"orderId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"price\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"size\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isBuy\",\"type\":\"bool\"}],\"name\":\"OrderCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"orderId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"size\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"price\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isBuy\",\"type\":\"bool\"}],\"name\":\"OrderCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40[]\",\"name\":\"orderId\",\"type\":\"uint40[]\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OrdersCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"orderId\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"makerAddress\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isBuy\",\"type\":\"bool\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"price\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"updatedSize\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"takerAddress\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"txOrigin\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"filledSize\",\"type\":\"uint96\"}],\"name\":\"Trade\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"Upgraded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"_vaultAskOrderSize\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"_vaultAskPartiallyFilledSize\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"_vaultBidOrderSize\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"_vaultBidPartiallyFilledSize\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_askPrice\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_bidPrice\",\"type\":\"uint256\"}],\"name\":\"VaultParamsUpdated\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"SPREAD_CONSTANT\",\"outputs\":[{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_price\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"size\",\"type\":\"uint96\"},{\"internalType\":\"bool\",\"name\":\"_postOnly\",\"type\":\"bool\"}],\"name\":\"addBuyOrder\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_price\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_flippedPrice\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"_size\",\"type\":\"uint96\"},{\"internalType\":\"bool\",\"name\":\"_provisionOrRevert\",\"type\":\"bool\"}],\"name\":\"addFlipBuyOrder\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_price\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_flippedPrice\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"_size\",\"type\":\"uint96\"},{\"internalType\":\"bool\",\"name\":\"_provisionOrRevert\",\"type\":\"bool\"}],\"name\":\"addFlipSellOrder\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_bidPrice\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_askPrice\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"_bidSize\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"_askSize\",\"type\":\"uint96\"}],\"name\":\"addPairedLiquidity\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_price\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"_size\",\"type\":\"uint96\"},{\"internalType\":\"bool\",\"name\":\"_postOnly\",\"type\":\"bool\"}],\"name\":\"addSellOrder\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32[]\",\"name\":\"bidPrices\",\"type\":\"uint32[]\"},{\"internalType\":\"uint32[]\",\"name\":\"askPrices\",\"type\":\"uint32[]\"},{\"internalType\":\"uint96[]\",\"name\":\"bidSizes\",\"type\":\"uint96[]\"},{\"internalType\":\"uint96[]\",\"name\":\"askSizes\",\"type\":\"uint96[]\"}],\"name\":\"batchAddPairedLiquidity\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint40[]\",\"name\":\"_orderIds\",\"type\":\"uint40[]\"}],\"name\":\"batchCancelFlipOrders\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint40[]\",\"name\":\"_orderIds\",\"type\":\"uint40[]\"}],\"name\":\"batchCancelOrders\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint40[]\",\"name\":\"_orderIds\",\"type\":\"uint40[]\"}],\"name\":\"batchCancelOrdersNoRevert\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32[]\",\"name\":\"prices\",\"type\":\"uint32[]\"},{\"internalType\":\"uint32[]\",\"name\":\"flipPrices\",\"type\":\"uint32[]\"},{\"internalType\":\"uint96[]\",\"name\":\"sizes\",\"type\":\"uint96[]\"},{\"internalType\":\"bool[]\",\"name\":\"isBuy\",\"type\":\"bool[]\"},{\"internalType\":\"bool\",\"name\":\"_provisionOrRevert\",\"type\":\"bool\"}],\"name\":\"batchProvisionLiquidity\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32[]\",\"name\":\"buyPrices\",\"type\":\"uint32[]\"},{\"internalType\":\"uint96[]\",\"name\":\"buySizes\",\"type\":\"uint96[]\"},{\"internalType\":\"uint32[]\",\"name\":\"sellPrices\",\"type\":\"uint32[]\"},{\"internalType\":\"uint96[]\",\"name\":\"sellSizes\",\"type\":\"uint96[]\"},{\"internalType\":\"uint40[]\",\"name\":\"orderIdsToCancel\",\"type\":\"uint40[]\"},{\"internalType\":\"bool\",\"name\":\"postOnly\",\"type\":\"bool\"}],\"name\":\"batchUpdate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"bestBidAsk\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"collectFees\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getL2Book\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_bidPricePoints\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_askPricePoints\",\"type\":\"uint32\"}],\"name\":\"getL2Book\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getMarketParams\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getVaultParams\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"},{\"internalType\":\"enum IOrderBook.OrderBookType\",\"name\":\"_type\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"_baseAssetAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_baseAssetDecimals\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_quoteAssetAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_quoteAssetDecimals\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_marginAccountAddress\",\"type\":\"address\"},{\"internalType\":\"uint96\",\"name\":\"_sizePrecision\",\"type\":\"uint96\"},{\"internalType\":\"uint32\",\"name\":\"_pricePrecision\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_tickSize\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"_minSize\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"_maxSize\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"_takerFeeBps\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_makerFeeBps\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_kuruAmmVault\",\"type\":\"address\"},{\"internalType\":\"uint96\",\"name\":\"_kuruAmmSpread\",\"type\":\"uint96\"},{\"internalType\":\"address\",\"name\":\"__trustedForwarder\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"forwarder\",\"type\":\"address\"}],\"name\":\"isTrustedForwarder\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"kuruAmmVault\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"marketState\",\"outputs\":[{\"internalType\":\"enum IOrderBook.MarketState\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint96\",\"name\":\"_quoteSize\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"_minAmountOut\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"_isMargin\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"_isFillOrKill\",\"type\":\"bool\"}],\"name\":\"placeAndExecuteMarketBuy\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint96\",\"name\":\"_size\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"_minAmountOut\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"_isMargin\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"_isFillOrKill\",\"type\":\"bool\"}],\"name\":\"placeAndExecuteMarketSell\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"proxiableUUID\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"s_buyPricePoints\",\"outputs\":[{\"internalType\":\"uint40\",\"name\":\"head\",\"type\":\"uint40\"},{\"internalType\":\"uint40\",\"name\":\"tail\",\"type\":\"uint40\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"s_buyTree\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"level0\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"s_orderIdCounter\",\"outputs\":[{\"internalType\":\"uint40\",\"name\":\"\",\"type\":\"uint40\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint40\",\"name\":\"\",\"type\":\"uint40\"}],\"name\":\"s_orders\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"ownerAddress\",\"type\":\"address\"},{\"internalType\":\"uint96\",\"name\":\"size\",\"type\":\"uint96\"},{\"internalType\":\"uint40\",\"name\":\"prev\",\"type\":\"uint40\"},{\"internalType\":\"uint40\",\"name\":\"next\",\"type\":\"uint40\"},{\"internalType\":\"uint40\",\"name\":\"flippedId\",\"type\":\"uint40\"},{\"internalType\":\"uint32\",\"name\":\"price\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"flippedPrice\",\"type\":\"uint32\"},{\"internalType\":\"bool\",\"name\":\"isBuy\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"s_sellPricePoints\",\"outputs\":[{\"internalType\":\"uint40\",\"name\":\"head\",\"type\":\"uint40\"},{\"internalType\":\"uint40\",\"name\":\"tail\",\"type\":\"uint40\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"s_sellTree\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"level0\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"enum IOrderBook.MarketState\",\"name\":\"_state\",\"type\":\"uint8\"}],\"name\":\"toggleMarket\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint96\",\"name\":\"_vaultAskOrderSize\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"_vaultBidOrderSize\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"_askPrice\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_bidPrice\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"_nullifyPartialFills\",\"type\":\"bool\"}],\"name\":\"updateVaultOrdSz\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"upgradeToAndCall\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"vaultAskOrderSize\",\"outputs\":[{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"vaultBestAsk\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"InsufficientLiquidity()\":[{\"details\":\"Thrown when IOC orders do not get filled by the market\"}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"InvalidSpread()\":[{\"details\":\"Thrown when Kuru AMM Vault spread passed to initializer is too high or too low or is not a multiple of 10\"}],\"LengthMismatch()\":[{\"details\":\"Thrown when length mismatch occurs between inputted arrays\"}],\"MarketFeeError()\":[{\"details\":\"Thrown when maker fee passed to initializer is too high/invalid\"}],\"MarketSizeError()\":[{\"details\":\"Thrown when minSize = 0 or maxSize < minSize\"}],\"MarketStateError()\":[{\"details\":\"Thrown when a market is paused and a user tries to execute an action or if the owner passes an already existing market state for toggling\"}],\"NativeAssetInsufficient()\":[{\"details\":\"Thrown when msg.value is insufficient in market orders\"}],\"NativeAssetNotRequired()\":[{\"details\":\"Thrown when msg.value is greater than 0 when native assets are not required\"}],\"NativeAssetSurplus()\":[{\"details\":\"Thrown when msg.value is surplus in market orders\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"OnlyOwnerAllowedError()\":[{\"details\":\"Thrown when a non-owner tries to execute a privileged function, i.e, if non owner tries to pause/unpause a market or if a user tries to cancel an order that they did not place\"}],\"OnlyVaultAllowed()\":[{\"details\":\"Thrown when the call is not made by the vault\"}],\"OrderAlreadyFilledOrCancelled()\":[{\"details\":\"Thrown when cancelOrder is called on an order which is already filled or cancelled\"}],\"PostOnlyError()\":[{\"details\":\"Thrown when a post only order gets filled\"}],\"PriceError()\":[{\"details\":\"Thrown when the inputted price while adding an order is invalid\"}],\"ProvisionError()\":[{\"details\":\"Thrown when a flip order matches with a price\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}],\"SizeError()\":[{\"details\":\"Thrown when the size inputted is invalid, i.e, < minSize or > maxSize\"}],\"SlippageExceeded()\":[{\"details\":\"Throws when slippage is exceeded in market orders\"}],\"TickSizeError()\":[{\"details\":\"Thrown when price is not divisible by tick\"}],\"Uint32Overflow()\":[{\"details\":\"Thrown when safe cast to uint32 fails\"}],\"Uint96Overflow()\":[{\"details\":\"Thrown when safe cast to uint96 fails\"}],\"Unauthorized()\":[{\"details\":\"Thrown when a user is not the owner and tries to execute a privileged function\"}],\"UnauthorizedCallContext()\":[{\"details\":\"The call is from an unauthorized call context.\"}],\"UpgradeFailed()\":[{\"details\":\"The upgrade failed.\"}],\"WrongOrderTypeCancel()\":[{\"details\":\"Thrown when wrong interface is called for a cancel\"}]},\"events\":{\"FlipOrderCreated(uint40,uint40,address,uint96,uint32,uint32,bool)\":{\"details\":\"Emitted when a flip order is created\",\"params\":{\"flippedId\":\"Unique identifier for the flipped order.\",\"flippedPrice\":\"Price point of the flipped order in the specified precision.\",\"isBuy\":\"Boolean indicating if the order is a buy (true) or sell (false) order.\",\"orderId\":\"Unique identifier for the newly created order.\",\"owner\":\"Address of the user who created the order.\",\"price\":\"Price point of the order in the specified precision.\",\"size\":\"Size of the order in the specified precision.\"}},\"FlipOrderUpdated(uint40,uint96)\":{\"details\":\"Emitted when a flip order is updated\",\"params\":{\"orderId\":\"Unique identifier for the order.\",\"size\":\"Size of the order in the specified precision.\"}},\"FlipOrdersCanceled(uint40[],address)\":{\"details\":\"Emitted when one or more flip orders are canceled\",\"params\":{\"orderIds\":\"Array of order identifiers that were canceled.\",\"owner\":\"Address of the user who canceled the orders.\"}},\"FlippedOrderCreated(uint40,uint40,address,uint96,uint32,uint32,bool)\":{\"details\":\"Emitted when a flip order is partially/completely filled and it results in a new order\",\"params\":{\"flippedId\":\"Unique identifier for the flipped order.\",\"flippedPrice\":\"Price point of the flipped order in the specified precision.\",\"isBuy\":\"Boolean indicating if the order is a buy (true) or sell (false) order.\",\"orderId\":\"Unique identifier for the newly created order.\",\"owner\":\"Address of the user who created the order.\",\"price\":\"Price point of the order in the specified precision.\",\"size\":\"Size of the order in the specified precision.\"}},\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized.\"},\"OrderCanceled(uint40,address,uint32,uint96,bool)\":{\"details\":\"Emitted for each cancel\"},\"OrderCreated(uint40,address,uint96,uint32,bool)\":{\"details\":\"Emitted when a new order is created.\",\"params\":{\"isBuy\":\"Boolean indicating if the order is a buy (true) or sell (false) order.\",\"orderId\":\"Unique identifier for the newly created order.\",\"owner\":\"Address of the user who created the order.\",\"price\":\"Price point of the order in the specified precision.\",\"size\":\"Size of the order in the specified precision.\"}},\"OrdersCanceled(uint40[],address)\":{\"details\":\"Emitted when one or more orders are completed or canceled.\",\"params\":{\"orderId\":\"Array of order identifiers that were completed or canceled.\"}},\"Trade(uint40,address,bool,uint256,uint96,address,address,uint96)\":{\"details\":\"Emitted when a trade goes through.\",\"params\":{\"filledSize\":\"Size taken by the taker.\",\"orderId\":\"Order Id of the order that was filled. PS. All data regarding the original order can be found out from the order ID\",\"takerAddress\":\"Address of the taker.\",\"updatedSize\":\"New size of the order\"}},\"Upgraded(address)\":{\"details\":\"Emitted when the proxy's implementation is upgraded.\"},\"VaultParamsUpdated(uint96,uint96,uint96,uint96,uint256,uint256)\":{\"details\":\"Emitted when the vault params are updated\",\"params\":{\"_askPrice\":\"The vault best ask price\",\"_bidPrice\":\"The vault best bid price\",\"_vaultAskOrderSize\":\"Size of the vault ask order\",\"_vaultAskPartiallyFilledSize\":\"Size of the vault ask partially filled order\",\"_vaultBidOrderSize\":\"Size of the vault bid order\",\"_vaultBidPartiallyFilledSize\":\"Size of the vault bid partially filled order\"}}},\"kind\":\"dev\",\"methods\":{\"addBuyOrder(uint32,uint96,bool)\":{\"details\":\"Adds a buy order to the order book.\",\"params\":{\"_postOnly\":\"Whether the order is post-only. The transaction reverts if the order is matched while placing.\",\"_price\":\"Price of the buy order.\",\"size\":\"Size of the buy order.\"}},\"addFlipBuyOrder(uint32,uint32,uint96,bool)\":{\"details\":\"Adds a flip buy order to the order book. This function will just return if it matches against the book.\",\"params\":{\"_flippedPrice\":\"Price of the sell order to which this order will flip on a fill\",\"_price\":\"Price of the buy order.\",\"_provisionOrRevert\":\"The transaction reverts if this is set to true and the flip order matches on placement.\",\"_size\":\"Size of the buy order.\"}},\"addFlipSellOrder(uint32,uint32,uint96,bool)\":{\"details\":\"Adds a flip sell order to the order book. This function will just return if it matches against the book.\",\"params\":{\"_flippedPrice\":\"Price of the buy order to which this order will flip on a fill\",\"_price\":\"Price of the sell order.\",\"_provisionOrRevert\":\"The transaction reverts if this is set to true and the flip order matches on placement.\",\"_size\":\"Size of the sell order.\"}},\"addPairedLiquidity(uint32,uint32,uint96,uint96)\":{\"details\":\"Adds a paired liquidity order to the order book. This function will revert if the order matches against the book.\",\"params\":{\"_askPrice\":\"Price of the ask order.\",\"_askSize\":\"Size of the ask order.\",\"_bidPrice\":\"Price of the bid order.\",\"_bidSize\":\"Size of the bid order.\"}},\"addSellOrder(uint32,uint96,bool)\":{\"details\":\"Adds a sell order to the order book.\",\"params\":{\"_postOnly\":\"Whether the order is post-only. The transaction reverts if the order is matched while placing.\",\"_price\":\"Price of the sell order.\",\"_size\":\"Size of the sell order.\"}},\"batchAddPairedLiquidity(uint32[],uint32[],uint96[],uint96[])\":{\"details\":\"Batch adds paired liquidity to the order book.\",\"params\":{\"askPrices\":\"Array of prices for the ask orders.\",\"askSizes\":\"Array of sizes for the ask orders.\",\"bidPrices\":\"Array of prices for the bid orders.\",\"bidSizes\":\"Array of sizes for the bid orders.\"}},\"batchCancelFlipOrders(uint40[])\":{\"details\":\"Cancels multiple flip orders in a batch. For a flip order pair, you only need to input ID of one of the orders.\",\"params\":{\"_orderIds\":\"Array of order IDs to cancel.\"}},\"batchCancelOrders(uint40[])\":{\"details\":\"Cancels multiple orders in a batch.Reverts if you pass an order ID which is filled or cancelled already.\",\"params\":{\"_orderIds\":\"Array of order IDs to cancel.\"}},\"batchCancelOrdersNoRevert(uint40[])\":{\"details\":\"Cancels multiple orders in a batch.Does not revert if you pass an order ID which is filled or cancelled already.\",\"params\":{\"_orderIds\":\"Array of order IDs to cancel.\"}},\"batchProvisionLiquidity(uint32[],uint32[],uint96[],bool[],bool)\":{\"details\":\"Batch adds flip orders to the order book.\",\"params\":{\"_provisionOrRevert\":\"If set to true, if a flip order matches against the book, the transaction reverts.\",\"flipPrices\":\"Array of prices for the flip orders.\",\"isBuy\":\"Array of booleans indicating if the i'th order is a buy order\",\"prices\":\"Array of prices for the flip orders.\",\"sizes\":\"Array of sizes for the flip orders.\"}},\"batchUpdate(uint32[],uint96[],uint32[],uint96[],uint40[],bool)\":{\"details\":\"Batch updates orders by placing multiple buy and sell orders and canceling orders.\",\"params\":{\"buyPrices\":\"Array of prices for the buy orders.\",\"buySizes\":\"Array of sizes for the buy orders.\",\"orderIdsToCancel\":\"Array of order IDs to cancel.\",\"postOnly\":\"Boolean indicating if the orders should be post-only.\",\"sellPrices\":\"Array of prices for the sell orders.\",\"sellSizes\":\"Array of sizes for the sell orders.\"}},\"bestBidAsk()\":{\"details\":\"Returns the best bid and the best ask of the market.\"},\"collectFees()\":{\"details\":\"Calls the creditFee function in MarginAccount\"},\"constructor\":{\"details\":\"Constructor.\"},\"getL2Book()\":{\"details\":\"This is useful if you want the complete order book, but may fail if the number of price points is a lot\",\"returns\":{\"_0\":\"data Encoded bytes containing the block number, prices, and sizes of the buy and sell orders.\"}},\"getL2Book(uint32,uint32)\":{\"details\":\"Encodes the block number, buy orders, and sell orders. Pass the number of bid and ask price points you need\",\"returns\":{\"data\":\"Encoded bytes containing the block number, prices, and sizes of the buy and sell orders.\"}},\"getMarketParams()\":{\"details\":\"Getter of market params.\"},\"initialize(address,uint8,address,uint256,address,uint256,address,uint96,uint32,uint32,uint96,uint96,uint256,uint256,address,uint96,address)\":{\"params\":{\"_baseAssetAddress\":\"Address of the first token used for trading.\",\"_baseAssetDecimals\":\"Deciimal pricicsion of the first swap token.\",\"_owner\":\"The owner of the contract.\",\"_quoteAssetAddress\":\"Address of the second token used for trading.\",\"_quoteAssetDecimals\":\"Deciimal pricicsion of the first swap token.\"}},\"placeAndExecuteMarketBuy(uint96,uint256,bool,bool)\":{\"details\":\"Places and executes a market buy order.\",\"params\":{\"_isFillOrKill\":\"bool representing if function should revert if full qty is not received.\",\"_isMargin\":\"bool representing if the market order is to be debited from the margin account of the user.\",\"_minAmountOut\":\"minimum amount of base asset user is willing to receive in base asset decimals.\",\"_quoteSize\":\"amount of quote asset user is ready to pay.\"},\"returns\":{\"_0\":\"_baseTokensCredited amount of base asset user received in base asset decimals.\"}},\"placeAndExecuteMarketSell(uint96,uint256,bool,bool)\":{\"details\":\"Places and executes a market sell order.\",\"params\":{\"_isFillOrKill\":\"bool representing if function should revert if full qty is not received.\",\"_isMargin\":\"bool representing if the market order is to be debited from the margin account of the user.\",\"_minAmountOut\":\"minimum amount of quote asset user is willing to receive in quote asset decimals.\",\"_size\":\"Size of the market sell order.\"},\"returns\":{\"_0\":\"_quoteCredited amount of quote asset user received in quote asset decimals.\"}},\"proxiableUUID()\":{\"details\":\"Returns the storage slot used by the implementation, as specified in [ERC1822](https://eips.ethereum.org/EIPS/eip-1822). Note: The `notDelegated` modifier prevents accidental upgrades to an implementation that is a proxy contract.\"},\"toggleMarket(uint8)\":{\"details\":\"Allows admin to pause the market.\"},\"transferOwnership(address)\":{\"details\":\"Allows admin to transfer ownership of the contract.\",\"params\":{\"_newOwner\":\"The new owner of the contract.\"}},\"updateVaultOrdSz(uint96,uint96,uint256,uint256,bool)\":{\"params\":{\"_askPrice\":\"The new ask price. Note: Only updated on the first deposit\",\"_bidPrice\":\"The new bid price. Note: Only updated on the first deposit\",\"_nullifyPartialFills\":\"Whether to nullify partial fills. Only done during specific withdrawals\",\"_vaultAskOrderSize\":\"The new ask order size\",\"_vaultBidOrderSize\":\"The new bid order size\"}},\"upgradeToAndCall(address,bytes)\":{\"details\":\"Upgrades the proxy's implementation to `newImplementation`. Emits a {Upgraded} event. Note: Passing in empty `data` skips the delegatecall to `newImplementation`.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"addFlipBuyOrder(uint32,uint32,uint96,bool)\":{\"notice\":\"Flip orders are not eligible for maker rebates as takers pay for the flipping.\"},\"addFlipSellOrder(uint32,uint32,uint96,bool)\":{\"notice\":\"Flip orders are not eligible for maker rebates as takers pay for the flipping.\"},\"addPairedLiquidity(uint32,uint32,uint96,uint96)\":{\"notice\":\"Flip orders are not eligible for maker rebates as takers pay for the flipping.\"},\"batchAddPairedLiquidity(uint32[],uint32[],uint96[],uint96[])\":{\"notice\":\"Flip orders are not eligible for maker rebates as takers pay for the flipping.\"},\"batchCancelFlipOrders(uint40[])\":{\"notice\":\"If you cancel a flip order, both sides of the order pair will be cancelled. You cannot cancel one side of the order pair alone.\"},\"batchProvisionLiquidity(uint32[],uint32[],uint96[],bool[],bool)\":{\"notice\":\"Flip orders are not eligible for maker rebates as takers pay for the flipping.\"},\"collectFees()\":{\"notice\":\"Anyone can call this function\"},\"getL2Book()\":{\"notice\":\"Wrapper around getL2Book that returns all bid and ask price points\"},\"getL2Book(uint32,uint32)\":{\"notice\":\"Returns the Level 2 order book data.\"},\"updateVaultOrdSz(uint96,uint96,uint256,uint256,bool)\":{\"notice\":\"Updates the vault order sizes and prices when a user deposits or withdraws\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/OrderBook.sol\":\"OrderBook\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/AbstractAMM.sol\":{\"keccak256\":\"0x174a22f9d3fe82b315d86ca86e1358f57e9b199618439284b0d2386f6a3f536e\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://986eeb1def68278dcbdcef380243c6835219a1bfab7cac312957967ae3baf804\",\"dweb:/ipfs/QmT2WWD1jrDH4nkXKmiXWwvMyJMZuSyS5wCGzn9FZ6u4Wg\"]},\"contracts/OrderBook.sol\":{\"keccak256\":\"0x054667b2015d71a16fe0c57816afa53d8a2c8c4a3fdfc603ad3d4c9a08820b08\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://80bc3ca50aa8a391d0a94d72671c8bd88d6a53852cedff355fe3f2eb4324b24f\",\"dweb:/ipfs/QmbQtFxyEQN2C4yBWzbm6JPmtKoENuM7LB51BVpuhsW1Ao\"]},\"contracts/interfaces/IMarginAccount.sol\":{\"keccak256\":\"0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4\",\"dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo\"]},\"contracts/interfaces/IOrderBook.sol\":{\"keccak256\":\"0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d\",\"dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68\"]},\"contracts/libraries/BitMath.sol\":{\"keccak256\":\"0xc6be48e23a72c9e2ed161a622b4839c68fcc1c3a3ca9c24d0c6e5cac9537541f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b8241e020c9f6f2a21f8c0451f6ecb396055a4bf86db122ae9236c555534130f\",\"dweb:/ipfs/QmQz4ghu9QLk2VixhGDjJCiyhn7a7ZC5UctDk1bQ2WLdXw\"]},\"contracts/libraries/ERC2771Context.sol\":{\"keccak256\":\"0x7458e7a07eb42f479dd6a547733373f21d7a45e0d9a78d545e16db6639e61ef5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8d278e5acd57e1943f2e411bcd6f4c13fb8620ae046cf8c8026fd9a103315497\",\"dweb:/ipfs/QmYWsYRhEV9A7sXgkFX6ES278EMN7b2rJ1JyetQ2XBQrWP\"]},\"contracts/libraries/Errors.sol\":{\"keccak256\":\"0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7\",\"dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu\"]},\"contracts/libraries/FixedPointMathLib.sol\":{\"keccak256\":\"0x90a5a4d76dd7cdbf8ee357bd0cb963ad5fb2f3d58444f213872f0e7e23ffbebd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://99f2d78bf91870c6815b4eec8aef48fec7e94de2dba2255f8fb8fe3cb9280946\",\"dweb:/ipfs/QmRjtG3C4hrXAc9hsyRpvzEZXhX3ivMktKB9hYgTaLAi4N\"]},\"contracts/libraries/OrderLinkedList.sol\":{\"keccak256\":\"0x48132979bf939d9d2b42d112a507300cdf1cd440d06ba98ea86e847e60fd2f32\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://0c20ad217ecf6e9ef7892d6e576f26d1fcd21f1804e4e8c1db66a2b7defdb133\",\"dweb:/ipfs/QmXqJ9XNkKJSs8c1pDVoghDWuacooxSCW7UAC4xL52FTjU\"]},\"contracts/libraries/TreeMath.sol\":{\"keccak256\":\"0x61fb4aea5f902aaee0bc1604e5500acee6d426e03bc638afee1d310a24eaba10\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eaaa42df47bc993cd8bc754198423ff87bcaaa2994bdcf9206bfae67fa81aca0\",\"dweb:/ipfs/Qmaqmu7ShkXGFauY6xfMTpnzMUHXbxuJFrCkPCsdMdT9dS\"]},\"lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol\":{\"keccak256\":\"0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e\",\"dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3\"]},\"lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol\":{\"keccak256\":\"0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de\",\"dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22\"]},\"node_modules/solady/src/utils/Initializable.sol\":{\"keccak256\":\"0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559\",\"dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy\"]},\"node_modules/solady/src/utils/SafeTransferLib.sol\":{\"keccak256\":\"0x583f47701d9b47bb3ef80fcabbbd62fbb58a01733b7a57e19658b4b02468883a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2523bfac005e21ef9963fdb3c08b2c61824e2b5ce2f53d1a1828b01ed995217c\",\"dweb:/ipfs/QmbBjVG9tZyeZSQH4m5GUzNBwo2iuvLoZYbmhT4gxnJc4J\"]},\"node_modules/solady/src/utils/UUPSUpgradeable.sol\":{\"keccak256\":\"0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16\",\"dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "InsufficientLiquidity"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "InvalidSpread"}, {"inputs": [], "type": "error", "name": "LengthMismatch"}, {"inputs": [], "type": "error", "name": "MarketFeeError"}, {"inputs": [], "type": "error", "name": "MarketSizeError"}, {"inputs": [], "type": "error", "name": "MarketStateError"}, {"inputs": [], "type": "error", "name": "NativeAssetInsufficient"}, {"inputs": [], "type": "error", "name": "NativeAssetNotRequired"}, {"inputs": [], "type": "error", "name": "NativeAssetSurplus"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [], "type": "error", "name": "OnlyOwnerAllowedError"}, {"inputs": [], "type": "error", "name": "Only<PERSON>aultAllowed"}, {"inputs": [], "type": "error", "name": "OrderAlreadyFilledOrCancelled"}, {"inputs": [], "type": "error", "name": "Post<PERSON>nly<PERSON><PERSON>r"}, {"inputs": [], "type": "error", "name": "PriceError"}, {"inputs": [], "type": "error", "name": "ProvisionError"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [], "type": "error", "name": "SizeError"}, {"inputs": [], "type": "error", "name": "SlippageExceeded"}, {"inputs": [], "type": "error", "name": "TickSizeError"}, {"inputs": [], "type": "error", "name": "Uint32Overflow"}, {"inputs": [], "type": "error", "name": "Uint96Overflow"}, {"inputs": [], "type": "error", "name": "Unauthorized"}, {"inputs": [], "type": "error", "name": "UnauthorizedCallContext"}, {"inputs": [], "type": "error", "name": "UpgradeFailed"}, {"inputs": [], "type": "error", "name": "WrongOrderTypeCancel"}, {"inputs": [{"internalType": "uint40", "name": "orderId", "type": "uint40", "indexed": false}, {"internalType": "uint40", "name": "flippedId", "type": "uint40", "indexed": false}, {"internalType": "address", "name": "owner", "type": "address", "indexed": false}, {"internalType": "uint96", "name": "size", "type": "uint96", "indexed": false}, {"internalType": "uint32", "name": "price", "type": "uint32", "indexed": false}, {"internalType": "uint32", "name": "flippedPrice", "type": "uint32", "indexed": false}, {"internalType": "bool", "name": "isBuy", "type": "bool", "indexed": false}], "type": "event", "name": "F<PERSON><PERSON><PERSON><PERSON>Created", "anonymous": false}, {"inputs": [{"internalType": "uint40", "name": "orderId", "type": "uint40", "indexed": false}, {"internalType": "uint96", "name": "size", "type": "uint96", "indexed": false}], "type": "event", "name": "FlipOrderUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint40[]", "name": "orderIds", "type": "uint40[]", "indexed": false}, {"internalType": "address", "name": "owner", "type": "address", "indexed": false}], "type": "event", "name": "FlipOrdersCanceled", "anonymous": false}, {"inputs": [{"internalType": "uint40", "name": "orderId", "type": "uint40", "indexed": false}, {"internalType": "uint40", "name": "flippedId", "type": "uint40", "indexed": false}, {"internalType": "address", "name": "owner", "type": "address", "indexed": false}, {"internalType": "uint96", "name": "size", "type": "uint96", "indexed": false}, {"internalType": "uint32", "name": "price", "type": "uint32", "indexed": false}, {"internalType": "uint32", "name": "flippedPrice", "type": "uint32", "indexed": false}, {"internalType": "bool", "name": "isBuy", "type": "bool", "indexed": false}], "type": "event", "name": "Flipped<PERSON><PERSON><PERSON>Created", "anonymous": false}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "enum IOrderBook.MarketState", "name": "previousState", "type": "uint8", "indexed": false}, {"internalType": "enum IOrderBook.MarketState", "name": "newState", "type": "uint8", "indexed": false}], "type": "event", "name": "MarketStateUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint40", "name": "orderId", "type": "uint40", "indexed": false}, {"internalType": "address", "name": "owner", "type": "address", "indexed": false}, {"internalType": "uint32", "name": "price", "type": "uint32", "indexed": false}, {"internalType": "uint96", "name": "size", "type": "uint96", "indexed": false}, {"internalType": "bool", "name": "isBuy", "type": "bool", "indexed": false}], "type": "event", "name": "OrderCanceled", "anonymous": false}, {"inputs": [{"internalType": "uint40", "name": "orderId", "type": "uint40", "indexed": false}, {"internalType": "address", "name": "owner", "type": "address", "indexed": false}, {"internalType": "uint96", "name": "size", "type": "uint96", "indexed": false}, {"internalType": "uint32", "name": "price", "type": "uint32", "indexed": false}, {"internalType": "bool", "name": "isBuy", "type": "bool", "indexed": false}], "type": "event", "name": "OrderCreated", "anonymous": false}, {"inputs": [{"internalType": "uint40[]", "name": "orderId", "type": "uint40[]", "indexed": false}, {"internalType": "address", "name": "owner", "type": "address", "indexed": false}], "type": "event", "name": "OrdersCanceled", "anonymous": false}, {"inputs": [{"internalType": "uint40", "name": "orderId", "type": "uint40", "indexed": false}, {"internalType": "address", "name": "makerAddress", "type": "address", "indexed": false}, {"internalType": "bool", "name": "isBuy", "type": "bool", "indexed": false}, {"internalType": "uint256", "name": "price", "type": "uint256", "indexed": false}, {"internalType": "uint96", "name": "updatedSize", "type": "uint96", "indexed": false}, {"internalType": "address", "name": "taker<PERSON><PERSON><PERSON>", "type": "address", "indexed": false}, {"internalType": "address", "name": "txOrigin", "type": "address", "indexed": false}, {"internalType": "uint96", "name": "filledSize", "type": "uint96", "indexed": false}], "type": "event", "name": "Trade", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address", "indexed": true}], "type": "event", "name": "Upgraded", "anonymous": false}, {"inputs": [{"internalType": "uint96", "name": "_vaultAskOrderSize", "type": "uint96", "indexed": false}, {"internalType": "uint96", "name": "_vaultAskPartiallyFilledSize", "type": "uint96", "indexed": false}, {"internalType": "uint96", "name": "_vaultBidOrderSize", "type": "uint96", "indexed": false}, {"internalType": "uint96", "name": "_vaultBidPartiallyFilledSize", "type": "uint96", "indexed": false}, {"internalType": "uint256", "name": "_askPrice", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "_bidPrice", "type": "uint256", "indexed": false}], "type": "event", "name": "VaultParamsUpdated", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SPREAD_CONSTANT", "outputs": [{"internalType": "uint96", "name": "", "type": "uint96"}]}, {"inputs": [{"internalType": "uint32", "name": "_price", "type": "uint32"}, {"internalType": "uint96", "name": "size", "type": "uint96"}, {"internalType": "bool", "name": "_postOnly", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "addBuyOrder"}, {"inputs": [{"internalType": "uint32", "name": "_price", "type": "uint32"}, {"internalType": "uint32", "name": "_flippedPrice", "type": "uint32"}, {"internalType": "uint96", "name": "_size", "type": "uint96"}, {"internalType": "bool", "name": "_provisionOrRevert", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "addFlipBuyOrder"}, {"inputs": [{"internalType": "uint32", "name": "_price", "type": "uint32"}, {"internalType": "uint32", "name": "_flippedPrice", "type": "uint32"}, {"internalType": "uint96", "name": "_size", "type": "uint96"}, {"internalType": "bool", "name": "_provisionOrRevert", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "addFlipSellOrder"}, {"inputs": [{"internalType": "uint32", "name": "_bidPrice", "type": "uint32"}, {"internalType": "uint32", "name": "_askPrice", "type": "uint32"}, {"internalType": "uint96", "name": "_bidSize", "type": "uint96"}, {"internalType": "uint96", "name": "_askSize", "type": "uint96"}], "stateMutability": "nonpayable", "type": "function", "name": "addPairedLiquidity"}, {"inputs": [{"internalType": "uint32", "name": "_price", "type": "uint32"}, {"internalType": "uint96", "name": "_size", "type": "uint96"}, {"internalType": "bool", "name": "_postOnly", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "addSellOrder"}, {"inputs": [{"internalType": "uint32[]", "name": "bidPrices", "type": "uint32[]"}, {"internalType": "uint32[]", "name": "askPrices", "type": "uint32[]"}, {"internalType": "uint96[]", "name": "bidSizes", "type": "uint96[]"}, {"internalType": "uint96[]", "name": "askSizes", "type": "uint96[]"}], "stateMutability": "nonpayable", "type": "function", "name": "batchAddPairedLiquidity"}, {"inputs": [{"internalType": "uint40[]", "name": "_orderIds", "type": "uint40[]"}], "stateMutability": "nonpayable", "type": "function", "name": "batchCancelFlipOrders"}, {"inputs": [{"internalType": "uint40[]", "name": "_orderIds", "type": "uint40[]"}], "stateMutability": "nonpayable", "type": "function", "name": "batchCancelOrders"}, {"inputs": [{"internalType": "uint40[]", "name": "_orderIds", "type": "uint40[]"}], "stateMutability": "nonpayable", "type": "function", "name": "batchCancelOrdersNoRevert"}, {"inputs": [{"internalType": "uint32[]", "name": "prices", "type": "uint32[]"}, {"internalType": "uint32[]", "name": "flipPrices", "type": "uint32[]"}, {"internalType": "uint96[]", "name": "sizes", "type": "uint96[]"}, {"internalType": "bool[]", "name": "isBuy", "type": "bool[]"}, {"internalType": "bool", "name": "_provisionOrRevert", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "batchProvisionLiquidity"}, {"inputs": [{"internalType": "uint32[]", "name": "buyPrices", "type": "uint32[]"}, {"internalType": "uint96[]", "name": "buySizes", "type": "uint96[]"}, {"internalType": "uint32[]", "name": "sellPrices", "type": "uint32[]"}, {"internalType": "uint96[]", "name": "sellSizes", "type": "uint96[]"}, {"internalType": "uint40[]", "name": "orderIdsToCancel", "type": "uint40[]"}, {"internalType": "bool", "name": "postOnly", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "batchUpdate"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "bestBidAsk", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "collectFees"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getL2Book", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [{"internalType": "uint32", "name": "_bidPricePoints", "type": "uint32"}, {"internalType": "uint32", "name": "_askPricePoints", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getL2Book", "outputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getMarketParams", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}, {"internalType": "uint96", "name": "", "type": "uint96"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint32", "name": "", "type": "uint32"}, {"internalType": "uint96", "name": "", "type": "uint96"}, {"internalType": "uint96", "name": "", "type": "uint96"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getVaultParams", "outputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint96", "name": "", "type": "uint96"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint96", "name": "", "type": "uint96"}, {"internalType": "uint96", "name": "", "type": "uint96"}, {"internalType": "uint96", "name": "", "type": "uint96"}, {"internalType": "uint96", "name": "", "type": "uint96"}]}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "enum IOrderBook.OrderBookType", "name": "_type", "type": "uint8"}, {"internalType": "address", "name": "_baseAssetAddress", "type": "address"}, {"internalType": "uint256", "name": "_baseAssetDecimals", "type": "uint256"}, {"internalType": "address", "name": "_quote<PERSON><PERSON><PERSON>dd<PERSON>", "type": "address"}, {"internalType": "uint256", "name": "_quoteAssetDecimals", "type": "uint256"}, {"internalType": "address", "name": "_marginAccountAddress", "type": "address"}, {"internalType": "uint96", "name": "_sizePrecision", "type": "uint96"}, {"internalType": "uint32", "name": "_pricePrecision", "type": "uint32"}, {"internalType": "uint32", "name": "_tickSize", "type": "uint32"}, {"internalType": "uint96", "name": "_minSize", "type": "uint96"}, {"internalType": "uint96", "name": "_maxSize", "type": "uint96"}, {"internalType": "uint256", "name": "_takerFeeBps", "type": "uint256"}, {"internalType": "uint256", "name": "_makerFeeBps", "type": "uint256"}, {"internalType": "address", "name": "_kuruAmmVault", "type": "address"}, {"internalType": "uint96", "name": "_kuruAmmSpread", "type": "uint96"}, {"internalType": "address", "name": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "forwarder", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isTrusted<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "kuruAmmVault", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "marketState", "outputs": [{"internalType": "enum IOrderBook.MarketState", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "uint96", "name": "_quoteSize", "type": "uint96"}, {"internalType": "uint256", "name": "_minAmountOut", "type": "uint256"}, {"internalType": "bool", "name": "_isMargin", "type": "bool"}, {"internalType": "bool", "name": "_isFillOrKill", "type": "bool"}], "stateMutability": "payable", "type": "function", "name": "placeAndExecuteMarketBuy", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint96", "name": "_size", "type": "uint96"}, {"internalType": "uint256", "name": "_minAmountOut", "type": "uint256"}, {"internalType": "bool", "name": "_isMargin", "type": "bool"}, {"internalType": "bool", "name": "_isFillOrKill", "type": "bool"}], "stateMutability": "payable", "type": "function", "name": "placeAndExecuteMarketSell", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "s_buyPricePoints", "outputs": [{"internalType": "uint40", "name": "head", "type": "uint40"}, {"internalType": "uint40", "name": "tail", "type": "uint40"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "s_buyTree", "outputs": [{"internalType": "bytes32", "name": "level0", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "s_orderIdCounter", "outputs": [{"internalType": "uint40", "name": "", "type": "uint40"}]}, {"inputs": [{"internalType": "uint40", "name": "", "type": "uint40"}], "stateMutability": "view", "type": "function", "name": "s_orders", "outputs": [{"internalType": "address", "name": "owner<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint96", "name": "size", "type": "uint96"}, {"internalType": "uint40", "name": "prev", "type": "uint40"}, {"internalType": "uint40", "name": "next", "type": "uint40"}, {"internalType": "uint40", "name": "flippedId", "type": "uint40"}, {"internalType": "uint32", "name": "price", "type": "uint32"}, {"internalType": "uint32", "name": "flippedPrice", "type": "uint32"}, {"internalType": "bool", "name": "isBuy", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "s_sellPricePoints", "outputs": [{"internalType": "uint40", "name": "head", "type": "uint40"}, {"internalType": "uint40", "name": "tail", "type": "uint40"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "s_sellTree", "outputs": [{"internalType": "bytes32", "name": "level0", "type": "bytes32"}]}, {"inputs": [{"internalType": "enum IOrderBook.MarketState", "name": "_state", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "toggleMarket"}, {"inputs": [{"internalType": "address", "name": "_newOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "uint96", "name": "_vaultAskOrderSize", "type": "uint96"}, {"internalType": "uint96", "name": "_vaultBidOrderSize", "type": "uint96"}, {"internalType": "uint256", "name": "_askPrice", "type": "uint256"}, {"internalType": "uint256", "name": "_bidPrice", "type": "uint256"}, {"internalType": "bool", "name": "_nullifyPartialFills", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "updateVaultOrdSz"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "upgradeToAndCall"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "vaultAskOrderSize", "outputs": [{"internalType": "uint96", "name": "", "type": "uint96"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "vaultBestAsk", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {"addBuyOrder(uint32,uint96,bool)": {"details": "Adds a buy order to the order book.", "params": {"_postOnly": "Whether the order is post-only. The transaction reverts if the order is matched while placing.", "_price": "Price of the buy order.", "size": "Size of the buy order."}}, "addFlipBuyOrder(uint32,uint32,uint96,bool)": {"details": "Adds a flip buy order to the order book. This function will just return if it matches against the book.", "params": {"_flippedPrice": "Price of the sell order to which this order will flip on a fill", "_price": "Price of the buy order.", "_provisionOrRevert": "The transaction reverts if this is set to true and the flip order matches on placement.", "_size": "Size of the buy order."}}, "addFlipSellOrder(uint32,uint32,uint96,bool)": {"details": "Adds a flip sell order to the order book. This function will just return if it matches against the book.", "params": {"_flippedPrice": "Price of the buy order to which this order will flip on a fill", "_price": "Price of the sell order.", "_provisionOrRevert": "The transaction reverts if this is set to true and the flip order matches on placement.", "_size": "Size of the sell order."}}, "addPairedLiquidity(uint32,uint32,uint96,uint96)": {"details": "Adds a paired liquidity order to the order book. This function will revert if the order matches against the book.", "params": {"_askPrice": "Price of the ask order.", "_askSize": "Size of the ask order.", "_bidPrice": "Price of the bid order.", "_bidSize": "Size of the bid order."}}, "addSellOrder(uint32,uint96,bool)": {"details": "Adds a sell order to the order book.", "params": {"_postOnly": "Whether the order is post-only. The transaction reverts if the order is matched while placing.", "_price": "Price of the sell order.", "_size": "Size of the sell order."}}, "batchAddPairedLiquidity(uint32[],uint32[],uint96[],uint96[])": {"details": "<PERSON><PERSON> adds paired liquidity to the order book.", "params": {"askPrices": "Array of prices for the ask orders.", "askSizes": "Array of sizes for the ask orders.", "bidPrices": "Array of prices for the bid orders.", "bidSizes": "Array of sizes for the bid orders."}}, "batchCancelFlipOrders(uint40[])": {"details": "Cancels multiple flip orders in a batch. For a flip order pair, you only need to input ID of one of the orders.", "params": {"_orderIds": "Array of order IDs to cancel."}}, "batchCancelOrders(uint40[])": {"details": "Cancels multiple orders in a batch.Reverts if you pass an order ID which is filled or cancelled already.", "params": {"_orderIds": "Array of order IDs to cancel."}}, "batchCancelOrdersNoRevert(uint40[])": {"details": "Cancels multiple orders in a batch.Does not revert if you pass an order ID which is filled or cancelled already.", "params": {"_orderIds": "Array of order IDs to cancel."}}, "batchProvisionLiquidity(uint32[],uint32[],uint96[],bool[],bool)": {"details": "<PERSON><PERSON> adds flip orders to the order book.", "params": {"_provisionOrRevert": "If set to true, if a flip order matches against the book, the transaction reverts.", "flipPrices": "Array of prices for the flip orders.", "isBuy": "Array of booleans indicating if the i'th order is a buy order", "prices": "Array of prices for the flip orders.", "sizes": "Array of sizes for the flip orders."}}, "batchUpdate(uint32[],uint96[],uint32[],uint96[],uint40[],bool)": {"details": "Batch updates orders by placing multiple buy and sell orders and canceling orders.", "params": {"buyPrices": "Array of prices for the buy orders.", "buySizes": "Array of sizes for the buy orders.", "orderIdsToCancel": "Array of order IDs to cancel.", "postOnly": "Boolean indicating if the orders should be post-only.", "sellPrices": "Array of prices for the sell orders.", "sellSizes": "Array of sizes for the sell orders."}}, "bestBidAsk()": {"details": "Returns the best bid and the best ask of the market."}, "collectFees()": {"details": "Calls the creditFee function in MarginAccount"}, "constructor": {"details": "Constructor."}, "getL2Book()": {"details": "This is useful if you want the complete order book, but may fail if the number of price points is a lot", "returns": {"_0": "data Encoded bytes containing the block number, prices, and sizes of the buy and sell orders."}}, "getL2Book(uint32,uint32)": {"details": "Encodes the block number, buy orders, and sell orders. Pass the number of bid and ask price points you need", "returns": {"data": "Encoded bytes containing the block number, prices, and sizes of the buy and sell orders."}}, "getMarketParams()": {"details": "Getter of market params."}, "initialize(address,uint8,address,uint256,address,uint256,address,uint96,uint32,uint32,uint96,uint96,uint256,uint256,address,uint96,address)": {"params": {"_baseAssetAddress": "Address of the first token used for trading.", "_baseAssetDecimals": "Deciimal pricicsion of the first swap token.", "_owner": "The owner of the contract.", "_quoteAssetAddress": "Address of the second token used for trading.", "_quoteAssetDecimals": "Deciimal pricicsion of the first swap token."}}, "placeAndExecuteMarketBuy(uint96,uint256,bool,bool)": {"details": "Places and executes a market buy order.", "params": {"_isFillOrKill": "bool representing if function should revert if full qty is not received.", "_isMargin": "bool representing if the market order is to be debited from the margin account of the user.", "_minAmountOut": "minimum amount of base asset user is willing to receive in base asset decimals.", "_quoteSize": "amount of quote asset user is ready to pay."}, "returns": {"_0": "_baseTokensCredited amount of base asset user received in base asset decimals."}}, "placeAndExecuteMarketSell(uint96,uint256,bool,bool)": {"details": "Places and executes a market sell order.", "params": {"_isFillOrKill": "bool representing if function should revert if full qty is not received.", "_isMargin": "bool representing if the market order is to be debited from the margin account of the user.", "_minAmountOut": "minimum amount of quote asset user is willing to receive in quote asset decimals.", "_size": "Size of the market sell order."}, "returns": {"_0": "_quoteCredited amount of quote asset user received in quote asset decimals."}}, "proxiableUUID()": {"details": "Returns the storage slot used by the implementation, as specified in [ERC1822](https://eips.ethereum.org/EIPS/eip-1822). Note: The `notDelegated` modifier prevents accidental upgrades to an implementation that is a proxy contract."}, "toggleMarket(uint8)": {"details": "Allows admin to pause the market."}, "transferOwnership(address)": {"details": "Allows admin to transfer ownership of the contract.", "params": {"_newOwner": "The new owner of the contract."}}, "updateVaultOrdSz(uint96,uint96,uint256,uint256,bool)": {"params": {"_askPrice": "The new ask price. Note: Only updated on the first deposit", "_bidPrice": "The new bid price. Note: Only updated on the first deposit", "_nullifyPartialFills": "Whether to nullify partial fills. Only done during specific withdrawals", "_vaultAskOrderSize": "The new ask order size", "_vaultBidOrderSize": "The new bid order size"}}, "upgradeToAndCall(address,bytes)": {"details": "Upgrades the proxy's implementation to `newImplementation`. Emits a {Upgraded} event. Note: Passing in empty `data` skips the delegatecall to `newImplementation`."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"addFlipBuyOrder(uint32,uint32,uint96,bool)": {"notice": "Flip orders are not eligible for maker rebates as takers pay for the flipping."}, "addFlipSellOrder(uint32,uint32,uint96,bool)": {"notice": "Flip orders are not eligible for maker rebates as takers pay for the flipping."}, "addPairedLiquidity(uint32,uint32,uint96,uint96)": {"notice": "Flip orders are not eligible for maker rebates as takers pay for the flipping."}, "batchAddPairedLiquidity(uint32[],uint32[],uint96[],uint96[])": {"notice": "Flip orders are not eligible for maker rebates as takers pay for the flipping."}, "batchCancelFlipOrders(uint40[])": {"notice": "If you cancel a flip order, both sides of the order pair will be cancelled. You cannot cancel one side of the order pair alone."}, "batchProvisionLiquidity(uint32[],uint32[],uint96[],bool[],bool)": {"notice": "Flip orders are not eligible for maker rebates as takers pay for the flipping."}, "collectFees()": {"notice": "Anyone can call this function"}, "getL2Book()": {"notice": "Wrapper around getL2Book that returns all bid and ask price points"}, "getL2Book(uint32,uint32)": {"notice": "Returns the Level 2 order book data."}, "updateVaultOrdSz(uint96,uint96,uint256,uint256,bool)": {"notice": "Updates the vault order sizes and prices when a user deposits or withdraws"}}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/OrderBook.sol": "OrderBook"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/AbstractAMM.sol": {"keccak256": "0x174a22f9d3fe82b315d86ca86e1358f57e9b199618439284b0d2386f6a3f536e", "urls": ["bzz-raw://986eeb1def68278dcbdcef380243c6835219a1bfab7cac312957967ae3baf804", "dweb:/ipfs/QmT2WWD1jrDH4nkXKmiXWwvMyJMZuSyS5wCGzn9FZ6u4Wg"], "license": "BUSL-1.1"}, "contracts/OrderBook.sol": {"keccak256": "0x054667b2015d71a16fe0c57816afa53d8a2c8c4a3fdfc603ad3d4c9a08820b08", "urls": ["bzz-raw://80bc3ca50aa8a391d0a94d72671c8bd88d6a53852cedff355fe3f2eb4324b24f", "dweb:/ipfs/QmbQtFxyEQN2C4yBWzbm6JPmtKoENuM7LB51BVpuhsW1Ao"], "license": "BUSL-1.1"}, "contracts/interfaces/IMarginAccount.sol": {"keccak256": "0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2", "urls": ["bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4", "dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IOrderBook.sol": {"keccak256": "0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b", "urls": ["bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d", "dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68"], "license": "GPL-2.0-or-later"}, "contracts/libraries/BitMath.sol": {"keccak256": "0xc6be48e23a72c9e2ed161a622b4839c68fcc1c3a3ca9c24d0c6e5cac9537541f", "urls": ["bzz-raw://b8241e020c9f6f2a21f8c0451f6ecb396055a4bf86db122ae9236c555534130f", "dweb:/ipfs/QmQz4ghu9QLk2VixhGDjJCiyhn7a7ZC5UctDk1bQ2WLdXw"], "license": "MIT"}, "contracts/libraries/ERC2771Context.sol": {"keccak256": "0x7458e7a07eb42f479dd6a547733373f21d7a45e0d9a78d545e16db6639e61ef5", "urls": ["bzz-raw://8d278e5acd57e1943f2e411bcd6f4c13fb8620ae046cf8c8026fd9a103315497", "dweb:/ipfs/QmYWsYRhEV9A7sXgkFX6ES278EMN7b2rJ1JyetQ2XBQrWP"], "license": "MIT"}, "contracts/libraries/Errors.sol": {"keccak256": "0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f", "urls": ["bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7", "dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu"], "license": "BUSL-1.1"}, "contracts/libraries/FixedPointMathLib.sol": {"keccak256": "0x90a5a4d76dd7cdbf8ee357bd0cb963ad5fb2f3d58444f213872f0e7e23ffbebd", "urls": ["bzz-raw://99f2d78bf91870c6815b4eec8aef48fec7e94de2dba2255f8fb8fe3cb9280946", "dweb:/ipfs/QmRjtG3C4hrXAc9hsyRpvzEZXhX3ivMktKB9hYgTaLAi4N"], "license": "MIT"}, "contracts/libraries/OrderLinkedList.sol": {"keccak256": "0x48132979bf939d9d2b42d112a507300cdf1cd440d06ba98ea86e847e60fd2f32", "urls": ["bzz-raw://0c20ad217ecf6e9ef7892d6e576f26d1fcd21f1804e4e8c1db66a2b7defdb133", "dweb:/ipfs/QmXqJ9XNkKJSs8c1pDVoghDWuacooxSCW7UAC4xL52FTjU"], "license": "BUSL-1.1"}, "contracts/libraries/TreeMath.sol": {"keccak256": "0x61fb4aea5f902aaee0bc1604e5500acee6d426e03bc638afee1d310a24eaba10", "urls": ["bzz-raw://eaaa42df47bc993cd8bc754198423ff87bcaaa2994bdcf9206bfae67fa81aca0", "dweb:/ipfs/Qmaqmu7ShkXGFauY6xfMTpnzMUHXbxuJFrCkPCsdMdT9dS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol": {"keccak256": "0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3", "urls": ["bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e", "dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol": {"keccak256": "0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946", "urls": ["bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de", "dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22"], "license": "MIT"}, "node_modules/solady/src/utils/Initializable.sol": {"keccak256": "0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec", "urls": ["bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559", "dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy"], "license": "MIT"}, "node_modules/solady/src/utils/SafeTransferLib.sol": {"keccak256": "0x583f47701d9b47bb3ef80fcabbbd62fbb58a01733b7a57e19658b4b02468883a", "urls": ["bzz-raw://2523bfac005e21ef9963fdb3c08b2c61824e2b5ce2f53d1a1828b01ed995217c", "dweb:/ipfs/QmbBjVG9tZyeZSQH4m5GUzNBwo2iuvLoZYbmhT4gxnJc4J"], "license": "MIT"}, "node_modules/solady/src/utils/UUPSUpgradeable.sol": {"keccak256": "0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e", "urls": ["bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16", "dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV"], "license": "MIT"}}, "version": 1}, "id": 4}