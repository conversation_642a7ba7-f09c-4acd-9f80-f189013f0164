{"abi": [{"type": "constructor", "inputs": [{"name": "_router", "type": "address", "internalType": "contract IRouter"}, {"name": "_owner", "type": "address", "internalType": "address"}, {"name": "_marginAccount", "type": "address", "internalType": "address"}, {"name": "_kuruCollective", "type": "address", "internalType": "address"}, {"name": "_kuruAmmSpread", "type": "uint96", "internalType": "uint96"}, {"name": "_kuruCollectiveFee", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "cancelOwnershipHandover", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "completeOwnershipHandover", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "deployTokenAndMarket", "inputs": [{"name": "tokenParams", "type": "tuple", "internalType": "struct MonadDeployer.TokenParams", "components": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "symbol", "type": "string", "internalType": "string"}, {"name": "tokenURI", "type": "string", "internalType": "string"}, {"name": "initialSupply", "type": "uint256", "internalType": "uint256"}, {"name": "dev", "type": "address", "internalType": "address"}, {"name": "supplyToDev", "type": "uint256", "internalType": "uint256"}]}, {"name": "marketParams", "type": "tuple", "internalType": "struct MonadDeployer.MarketParams", "components": [{"name": "nativeTokenAmount", "type": "uint256", "internalType": "uint256"}, {"name": "sizePrecision", "type": "uint96", "internalType": "uint96"}, {"name": "pricePrecision", "type": "uint32", "internalType": "uint32"}, {"name": "tickSize", "type": "uint32", "internalType": "uint32"}, {"name": "minSize", "type": "uint96", "internalType": "uint96"}, {"name": "maxSize", "type": "uint96", "internalType": "uint96"}, {"name": "takerFeeBps", "type": "uint256", "internalType": "uint256"}, {"name": "makerFeeBps", "type": "uint256", "internalType": "uint256"}]}, {"name": "metadata", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "market", "type": "address", "internalType": "address"}], "stateMutability": "payable"}, {"type": "function", "name": "kuruAmmSpread", "inputs": [], "outputs": [{"name": "", "type": "uint96", "internalType": "uint96"}], "stateMutability": "view"}, {"type": "function", "name": "kuruCollective", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "kuruCollectiveFee", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "result", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "ownershipHandoverExpiresAt", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "internalType": "address"}], "outputs": [{"name": "result", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "requestOwnershipHandover", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "setKuruAmmSpread", "inputs": [{"name": "_kuruAmmSpread", "type": "uint96", "internalType": "uint96"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setKuruCollective", "inputs": [{"name": "_kuruCollective", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setKuruCollectiveFee", "inputs": [{"name": "_kuruCollectiveFee", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}, {"type": "event", "name": "OwnershipHandoverCanceled", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipHandoverRequested", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "old<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PumpingTime", "inputs": [{"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenURI", "type": "string", "indexed": false, "internalType": "string"}, {"name": "dev", "type": "address", "indexed": false, "internalType": "address"}, {"name": "supplyToDev", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "market", "type": "address", "indexed": false, "internalType": "address"}, {"name": "metadata", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "error", "name": "AlreadyInitialized", "inputs": []}, {"type": "error", "name": "InsufficientAssets", "inputs": [{"name": "expected", "type": "uint256", "internalType": "uint256"}, {"name": "received", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "NewOwnerIsZeroAddress", "inputs": []}, {"type": "error", "name": "NoHandoverRequest", "inputs": []}, {"type": "error", "name": "Unauthorized", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "393:3576:18:-:0;;;;;;;;;;;;;-1:-1:-1;;393:3576:18;;;;-1:-1:-1;;;;;393:3576:18;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;393:3576:18;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;393:3576:18;;;;;;;;;;;;;;;-1:-1:-1;;;;;5710:347:58;;;-1:-1:-1;;5710:347:58;;;-1:-1:-1;5710:347:58;-1:-1:-1;;5710:347:58;393:3576:18;1613:16;-1:-1:-1;;;;;393:3576:18;;1639:46;393:3576;;-1:-1:-1;;;;;;393:3576:18;;-1:-1:-1;393:3576:18;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;393:3576:18;;;;;;-1:-1:-1;393:3576:18;;;;;-1:-1:-1;393:3576:18;;;;-1:-1:-1;;;;;393:3576:18;;;;;;:::o", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "393:3576:18:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;393:3576:18;;;;;;:::i;:::-;11885:237:58;;;;;393:3576:18;11885:237:58;;;;393:3576:18;;;;;;;;;;-1:-1:-1;393:3576:18;;-1:-1:-1;;393:3576:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;393:3576:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;393:3576:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;393:3576:18;;;;;;;;;;;;;;-1:-1:-1;;;;;393:3576:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2063:16;;2081:18;;393:3576;;;;;;;2049:93;;;;;;;;;;393:3576;2049:93;;;;;393:3576;;2049:93;;;;;;393:3576;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;2136:4;393:3576;;;2049:93;;393:3576;2049:93;;;;;-1:-1:-1;;;;;393:3576:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;393:3576:18;;;;;;;;;;2161:421;;2193:40;393:3576;2161:421;;393:3576;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2161:421;393:3576;;;;;;;2161:6;393:3576;2161:421;;;;;;;393:3576;2161:421;;;393:3576;;;;2610:50;393:3576;;2610:50;;;:::i;:::-;2596:9;:65;2592:184;;-1:-1:-1;393:3576:18;;-1:-1:-1;;;2810:35:18;;-1:-1:-1;;;;;393:3576:18;;;;;;;;;;;2810:35;;;;;;;393:3576;2810:35;;;393:3576;;;;;;;;;;2909:7;393:3576;;2909:7;393:3576;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;2963:36:18;;-1:-1:-1;;;;;393:3576:18;;;2963:36;;393:3576;2909:7;393:3576;;;;;;;;;;;;;;-1:-1:-1;2963:36:18;;;;;;;;393:3576;2963:36;;;393:3576;-1:-1:-1;393:3576:18;;;-1:-1:-1;;;3009:152:18;;393:3576;3009:152;;393:3576;;;;;;;;;2136:4;393:3576;;;;;;;;;;;;-1:-1:-1;;;;;393:3576:18;3009:152;;;;;;;;393:3576;;3171:75;393:3576;;;;;;;;;;;3203:42;393:3576;;;;;3203:42;:::i;:::-;393:3576;;-1:-1:-1;;;3171:75:18;;-1:-1:-1;;;;;393:3576:18;;;;3171:75;;393:3576;;;;;;;;;;;;;;;3171:75;;;393:3576;3171:75;;;;;;;;;393:3576;-1:-1:-1;393:3576:18;;;;;;;;;3256:13;-1:-1:-1;;;;;393:3576:18;3256:94;;;;;393:3576;;;;;;;;;;;;;3256:94;;393:3576;3256:94;;393:3576;;;;;;;;;;;3256:94;;;;;;;393:3576;3256:94;3365:210;3256:94;393:3576;3256:94;;;;;393:3576;3418:20;3481:42;3418:20;;393:3576;;;;;;;;;;;;;;;3481:42;:::i;:::-;393:3576;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;393:3576:18;;3365:210;;;;393:3576;;;;;;3256:94;;;;;393:3576;3256:94;;:::i;:::-;393:3576;;3481:42;3256:94;;;393:3576;;;;;;;;;3256:94;393:3576;;;3171:75;;;393:3576;3171:75;393:3576;3171:75;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;3009:152;393:3576;3009:152;;393:3576;3009:152;;;;;;393:3576;3009:152;;;:::i;:::-;;;393:3576;;;;;3009:152;;;;;-1:-1:-1;3009:152:18;;2963:36;;;;;;;;;;;;;:::i;:::-;;;393:3576;;;;;;;;;;;;2810:35;;;393:3576;2810:35;;;;;;;;;393:3576;2810:35;;;:::i;:::-;;;393:3576;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;:::i;:::-;;2810:35;;;;;;-1:-1:-1;2810:35:18;;2592:184;2703:50;393:3576;;;2703:50;:::i;:::-;2684:81;;;393:3576;2684:81;393:3576;;2596:9;393:3576;;;;2684:81;2161:421;;;393:3576;2161:421;;393:3576;2161:421;;;;;;393:3576;2161:421;;;:::i;:::-;;;393:3576;;;;;;;:::i;:::-;2161:421;;;;;;-1:-1:-1;2161:421:18;;2049:93;393:3576;;;;;;;;;;;;;;-1:-1:-1;;393:3576:18;;;;;;:::i;:::-;12478:70:58;;:::i;:::-;8479:183;;;;;;8681:8;;;:::i;:::-;393:3576:18;8479:183:58;;393:3576:18;8479:183:58;393:3576:18;8479:183:58;;393:3576:18;;;-1:-1:-1;;393:3576:18;;;;;;:::i;:::-;12478:70:58;;:::i;:::-;10506:526;;;;393:3576:18;10506:526:58;393:3576:18;10506:526:58;;;;;;;;;393:3576:18;11051:12:58;10506:526;;11051:12;:::i;10506:526::-;;393:3576:18;10506:526:58;393:3576:18;10506:526:58;;393:3576:18;;;;;;-1:-1:-1;;393:3576:18;;;;-1:-1:-1;;11523:61:58;393:3576:18;;-1:-1:-1;;;;;393:3576:18;;;;;;;;;;;;;;-1:-1:-1;;393:3576:18;;;;;-1:-1:-1;;;;;393:3576:18;;;;;;;;;;;;;;;-1:-1:-1;;393:3576:18;;;;;;-1:-1:-1;;;;;393:3576:18;;;;;;;12478:70:58;;:::i;:::-;-1:-1:-1;;;;;393:3576:18;;;;;;;;;;;;;;;;-1:-1:-1;;393:3576:18;;;;12478:70:58;;:::i;:::-;393:3576:18;;;;;;;;-1:-1:-1;;393:3576:18;;;;12478:70:58;;:::i;:::-;393:3576:18;6813:405:58;;;;;;;393:3576:18;-1:-1:-1;;6813:405:58;393:3576:18;;;;-1:-1:-1;;393:3576:18;;;;9831:339:58;;;;393:3576:18;9831:339:58;393:3576:18;9831:339:58;;;;;;393:3576:18;9831:339:58;;393:3576:18;;;;;;;-1:-1:-1;;393:3576:18;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;393:3576:18;;;;;1007:32;393:3576;;;;;;;;;;-1:-1:-1;;393:3576:18;;;;9239:383:58;;;;393:3576:18;9239:383:58;7972:9;9132:15;393:3576:18;9239:383:58;;;;;;393:3576:18;9239:383:58;;393:3576:18;;;;;;;-1:-1:-1;;393:3576:18;;;;;;:::i;:::-;12478:70:58;;:::i;:::-;-1:-1:-1;;;;;393:3576:18;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;393:3576:18;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;393:3576:18;;;;;:::i;:::-;;;;;;;;;;;;;-1:-1:-1;393:3576:18;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;-1:-1:-1;393:3576:18;;;;;;;;-1:-1:-1;;393:3576:18;;;;:::o;:::-;;;-1:-1:-1;;;;;393:3576:18;;;;;;:::o;:::-;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;;;393:3576:18;;;;;;:::o;:::-;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;:::o;7292:355:58:-;-1:-1:-1;;7390:251:58;;;;;7292:355::o;7390:251::-;;;;;;;6145:1089;393:3576:18;;;;;6813:405:58;;;;;;-1:-1:-1;6813:405:58;;-1:-1:-1;;6813:405:58;6145:1089::o", "linkReferences": {}, "immutableReferences": {"14829": [{"start": 1010, "length": 32}], "14838": [{"start": 1416, "length": 32}]}}, "methodIdentifiers": {"cancelOwnershipHandover()": "54d1f13d", "completeOwnershipHandover(address)": "f04e283e", "deployTokenAndMarket((string,string,string,uint256,address,uint256),(uint256,uint96,uint32,uint32,uint96,uint96,uint256,uint256),bytes)": "f7f48c2c", "kuruAmmSpread()": "8789af75", "kuruCollective()": "2eda666a", "kuruCollectiveFee()": "2df15cb2", "owner()": "8da5cb5b", "ownershipHandoverExpiresAt(address)": "fee81cf4", "renounceOwnership()": "715018a6", "requestOwnershipHandover()": "25692962", "setKuruAmmSpread(uint96)": "86d0fb95", "setKuruCollective(address)": "230d07f1", "setKuruCollectiveFee(uint256)": "814f1eb2", "transferOwnership(address)": "f2fde38b"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"contract IRouter\",\"name\":\"_router\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_marginAccount\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_kuruCollective\",\"type\":\"address\"},{\"internalType\":\"uint96\",\"name\":\"_kuruAmmSpread\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"_kuruCollectiveFee\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AlreadyInitialized\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"expected\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"received\",\"type\":\"uint256\"}],\"name\":\"InsufficientAssets\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NewOwnerIsZeroAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NoHandoverRequest\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"OwnershipHandoverCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"OwnershipHandoverRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"tokenURI\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"dev\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"supplyToDev\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"metadata\",\"type\":\"bytes\"}],\"name\":\"PumpingTime\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"cancelOwnershipHandover\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"completeOwnershipHandover\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"tokenURI\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"initialSupply\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"dev\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"supplyToDev\",\"type\":\"uint256\"}],\"internalType\":\"struct MonadDeployer.TokenParams\",\"name\":\"tokenParams\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint256\",\"name\":\"nativeTokenAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint96\",\"name\":\"sizePrecision\",\"type\":\"uint96\"},{\"internalType\":\"uint32\",\"name\":\"pricePrecision\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"tickSize\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"minSize\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"maxSize\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"takerFeeBps\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"makerFeeBps\",\"type\":\"uint256\"}],\"internalType\":\"struct MonadDeployer.MarketParams\",\"name\":\"marketParams\",\"type\":\"tuple\"},{\"internalType\":\"bytes\",\"name\":\"metadata\",\"type\":\"bytes\"}],\"name\":\"deployTokenAndMarket\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"kuruAmmSpread\",\"outputs\":[{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"kuruCollective\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"kuruCollectiveFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"result\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"ownershipHandoverExpiresAt\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"result\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"requestOwnershipHandover\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint96\",\"name\":\"_kuruAmmSpread\",\"type\":\"uint96\"}],\"name\":\"setKuruAmmSpread\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_kuruCollective\",\"type\":\"address\"}],\"name\":\"setKuruCollective\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_kuruCollectiveFee\",\"type\":\"uint256\"}],\"name\":\"setKuruCollectiveFee\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"AlreadyInitialized()\":[{\"details\":\"Cannot double-initialize.\"}],\"NewOwnerIsZeroAddress()\":[{\"details\":\"The `newOwner` cannot be the zero address.\"}],\"NoHandoverRequest()\":[{\"details\":\"The `pendingOwner` does not have a valid handover request.\"}],\"Unauthorized()\":[{\"details\":\"The caller is not authorized to call the function.\"}]},\"events\":{\"OwnershipHandoverCanceled(address)\":{\"details\":\"The ownership handover to `pendingOwner` has been canceled.\"},\"OwnershipHandoverRequested(address)\":{\"details\":\"An ownership handover to `pendingOwner` has been requested.\"},\"OwnershipTransferred(address,address)\":{\"details\":\"The ownership is transferred from `oldOwner` to `newOwner`. This event is intentionally kept the same as OpenZeppelin's Ownable to be compatible with indexers and [EIP-173](https://eips.ethereum.org/EIPS/eip-173), despite it not being as lightweight as a single argument event.\"}},\"kind\":\"dev\",\"methods\":{\"cancelOwnershipHandover()\":{\"details\":\"Cancels the two-step ownership handover to the caller, if any.\"},\"completeOwnershipHandover(address)\":{\"details\":\"Allows the owner to complete the two-step ownership handover to `pendingOwner`. Reverts if there is no existing ownership handover requested by `pendingOwner`.\"},\"owner()\":{\"details\":\"Returns the owner of the contract.\"},\"ownershipHandoverExpiresAt(address)\":{\"details\":\"Returns the expiry timestamp for the two-step ownership handover to `pendingOwner`.\"},\"renounceOwnership()\":{\"details\":\"Allows the owner to renounce their ownership.\"},\"requestOwnershipHandover()\":{\"details\":\"Request a two-step ownership handover to the caller. The request will automatically expire in 48 hours (172800 seconds) by default.\"},\"transferOwnership(address)\":{\"details\":\"Allows the owner to transfer the ownership to `newOwner`.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/periphery/MonadDeployer.sol\":\"MonadDeployer\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/interfaces/IKuruAMMVault.sol\":{\"keccak256\":\"0xf0460e901fd738f2046c075784aa3045a58342c2f8ed276df83cf5386be969c7\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://54c3a5647cce41595226f4bbdc403bf6ef91177f3caa57de908e9ecaa2ac6460\",\"dweb:/ipfs/QmcWxssB8rFcX1QCZBqHzmmE3Dq2AHUFPfJvogzegQnMoS\"]},\"contracts/interfaces/IMarginAccount.sol\":{\"keccak256\":\"0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4\",\"dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo\"]},\"contracts/interfaces/IOrderBook.sol\":{\"keccak256\":\"0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d\",\"dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68\"]},\"contracts/interfaces/IRouter.sol\":{\"keccak256\":\"0x8b5aed176358b66cd5e9a7286eb927b0aa60cc895b83f1ec3d90bea9788d8702\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://c91e0f439908e9e7ad7c5741cab792806721613fa5c6ac74d0b307bb67489340\",\"dweb:/ipfs/QmYdpg9TuTAAAtT3KzUU6yWNnRXCpGxSs51MsBWjtJnXLu\"]},\"contracts/periphery/ERC20.sol\":{\"keccak256\":\"0x8aa728c92ff36c2a5b281d212fff85ee7d1ced42c6fbdcebcfc1f4dae55abbbe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://65555a3627600408de33493cf5acb82b0621f81a0b70d730a46391e96c5ca965\",\"dweb:/ipfs/QmUUu9vjRRVx6htK4PyaPw1gXPrPccCctEu3rg9vpXcuB5\"]},\"contracts/periphery/MonadDeployer.sol\":{\"keccak256\":\"0xf695a5c8c1d64a6bf98e961a7350d892829d65c26a990e49593803f3ee9b64b4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://472639918b26228406e04bdfba38c1ec3e3cd64e88148d4ae7d174a935bc87cf\",\"dweb:/ipfs/Qma5Y8iqty1FALcoL11EKX1kPve8C188yTrLfWtfAaHuYj\"]},\"node_modules/solady/src/auth/Ownable.sol\":{\"keccak256\":\"0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3\",\"dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs\"]},\"node_modules/solady/src/tokens/ERC20.sol\":{\"keccak256\":\"0xb4a3f9ba8a05107f7370de42cff57f3ad26dafd438712c11531a5892de2f59e0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f0a9ca06e3cf6dea1f9a4c5599581573b7d81cd64dc3afb582f325ccf5fdd6dc\",\"dweb:/ipfs/Qmb9r5dDceNF4W8S5u6i85RsNTgE5XG9HbTXkyS25ad3C6\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "contract IRouter", "name": "_router", "type": "address"}, {"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "address", "name": "_marginAccount", "type": "address"}, {"internalType": "address", "name": "_kuruCollective", "type": "address"}, {"internalType": "uint96", "name": "_kuruAmmSpread", "type": "uint96"}, {"internalType": "uint256", "name": "_kuruCollectiveFee", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "AlreadyInitialized"}, {"inputs": [{"internalType": "uint256", "name": "expected", "type": "uint256"}, {"internalType": "uint256", "name": "received", "type": "uint256"}], "type": "error", "name": "InsufficientAssets"}, {"inputs": [], "type": "error", "name": "NewOwnerIsZeroAddress"}, {"inputs": [], "type": "error", "name": "NoHandoverRequest"}, {"inputs": [], "type": "error", "name": "Unauthorized"}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipHandoverCanceled", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipHandoverRequested", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "old<PERSON>wner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "token", "type": "address", "indexed": true}, {"internalType": "string", "name": "tokenURI", "type": "string", "indexed": false}, {"internalType": "address", "name": "dev", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "supplyToDev", "type": "uint256", "indexed": false}, {"internalType": "address", "name": "market", "type": "address", "indexed": false}, {"internalType": "bytes", "name": "metadata", "type": "bytes", "indexed": false}], "type": "event", "name": "PumpingTime", "anonymous": false}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "cancelOwnershipHandover"}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "stateMutability": "payable", "type": "function", "name": "completeOwnershipHandover"}, {"inputs": [{"internalType": "struct MonadDeployer.TokenParams", "name": "tokenParams", "type": "tuple", "components": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "string", "name": "tokenURI", "type": "string"}, {"internalType": "uint256", "name": "initialSupply", "type": "uint256"}, {"internalType": "address", "name": "dev", "type": "address"}, {"internalType": "uint256", "name": "supplyToDev", "type": "uint256"}]}, {"internalType": "struct MonadDeployer.MarketParams", "name": "marketParams", "type": "tuple", "components": [{"internalType": "uint256", "name": "nativeTokenAmount", "type": "uint256"}, {"internalType": "uint96", "name": "sizePrecision", "type": "uint96"}, {"internalType": "uint32", "name": "pricePrecision", "type": "uint32"}, {"internalType": "uint32", "name": "tickSize", "type": "uint32"}, {"internalType": "uint96", "name": "minSize", "type": "uint96"}, {"internalType": "uint96", "name": "maxSize", "type": "uint96"}, {"internalType": "uint256", "name": "takerFeeBps", "type": "uint256"}, {"internalType": "uint256", "name": "makerFeeBps", "type": "uint256"}]}, {"internalType": "bytes", "name": "metadata", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "deployTokenAndMarket", "outputs": [{"internalType": "address", "name": "market", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "kuruAmmSpread", "outputs": [{"internalType": "uint96", "name": "", "type": "uint96"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "kuruCollective", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "kuruCollectiveFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "result", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "stateMutability": "view", "type": "function", "name": "ownershipHandoverExpiresAt", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}]}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "renounceOwnership"}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "requestOwnershipHandover"}, {"inputs": [{"internalType": "uint96", "name": "_kuruAmmSpread", "type": "uint96"}], "stateMutability": "nonpayable", "type": "function", "name": "setKuruAmmSpread"}, {"inputs": [{"internalType": "address", "name": "_kuruCollective", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setKuruCollective"}, {"inputs": [{"internalType": "uint256", "name": "_kuruCollectiveFee", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setKuruCollectiveFee"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "payable", "type": "function", "name": "transferOwnership"}], "devdoc": {"kind": "dev", "methods": {"cancelOwnershipHandover()": {"details": "Cancels the two-step ownership handover to the caller, if any."}, "completeOwnershipHandover(address)": {"details": "Allows the owner to complete the two-step ownership handover to `pendingOwner`. Reverts if there is no existing ownership handover requested by `pendingOwner`."}, "owner()": {"details": "Returns the owner of the contract."}, "ownershipHandoverExpiresAt(address)": {"details": "Returns the expiry timestamp for the two-step ownership handover to `pendingOwner`."}, "renounceOwnership()": {"details": "Allows the owner to renounce their ownership."}, "requestOwnershipHandover()": {"details": "Request a two-step ownership handover to the caller. The request will automatically expire in 48 hours (172800 seconds) by default."}, "transferOwnership(address)": {"details": "Allows the owner to transfer the ownership to `newOwner`."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/periphery/MonadDeployer.sol": "MonadDeployer"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/interfaces/IKuruAMMVault.sol": {"keccak256": "0xf0460e901fd738f2046c075784aa3045a58342c2f8ed276df83cf5386be969c7", "urls": ["bzz-raw://54c3a5647cce41595226f4bbdc403bf6ef91177f3caa57de908e9ecaa2ac6460", "dweb:/ipfs/QmcWxssB8rFcX1QCZBqHzmmE3Dq2AHUFPfJvogzegQnMoS"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IMarginAccount.sol": {"keccak256": "0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2", "urls": ["bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4", "dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IOrderBook.sol": {"keccak256": "0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b", "urls": ["bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d", "dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IRouter.sol": {"keccak256": "0x8b5aed176358b66cd5e9a7286eb927b0aa60cc895b83f1ec3d90bea9788d8702", "urls": ["bzz-raw://c91e0f439908e9e7ad7c5741cab792806721613fa5c6ac74d0b307bb67489340", "dweb:/ipfs/QmYdpg9TuTAAAtT3KzUU6yWNnRXCpGxSs51MsBWjtJnXLu"], "license": "GPL-2.0-or-later"}, "contracts/periphery/ERC20.sol": {"keccak256": "0x8aa728c92ff36c2a5b281d212fff85ee7d1ced42c6fbdcebcfc1f4dae55abbbe", "urls": ["bzz-raw://65555a3627600408de33493cf5acb82b0621f81a0b70d730a46391e96c5ca965", "dweb:/ipfs/QmUUu9vjRRVx6htK4PyaPw1gXPrPccCctEu3rg9vpXcuB5"], "license": "MIT"}, "contracts/periphery/MonadDeployer.sol": {"keccak256": "0xf695a5c8c1d64a6bf98e961a7350d892829d65c26a990e49593803f3ee9b64b4", "urls": ["bzz-raw://472639918b26228406e04bdfba38c1ec3e3cd64e88148d4ae7d174a935bc87cf", "dweb:/ipfs/Qma5Y8iqty1FALcoL11EKX1kPve8C188yTrLfWtfAaHuYj"], "license": "BUSL-1.1"}, "node_modules/solady/src/auth/Ownable.sol": {"keccak256": "0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3", "urls": ["bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3", "dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs"], "license": "MIT"}, "node_modules/solady/src/tokens/ERC20.sol": {"keccak256": "0xb4a3f9ba8a05107f7370de42cff57f3ad26dafd438712c11531a5892de2f59e0", "urls": ["bzz-raw://f0a9ca06e3cf6dea1f9a4c5599581573b7d81cd64dc3afb582f325ccf5fdd6dc", "dweb:/ipfs/Qmb9r5dDceNF4W8S5u6i85RsNTgE5XG9HbTXkyS25ad3C6"], "license": "MIT"}}, "version": 1}, "id": 18}