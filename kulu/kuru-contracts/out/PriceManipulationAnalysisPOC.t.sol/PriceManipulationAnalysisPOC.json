{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "setUp", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "test_AtomicManipulationAttack", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_EconomicImpactOfManipulation", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_NoProtectionMechanisms", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_PriceManipulationVulnerability", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "1059:9962:65:-:0;;;;;;;3166:4:21;1059:9962:65;;3166:4:21;1059:9962:65;;;3166:4:21;1059:9962:65;3166:4:21;1059:9962:65;;1087:4:32;1059:9962:65;;;1087:4:32;1059:9962:65;3166:4:21;1059:9962:65;;;;;;1263:12;1059:9962;;;1263:12;1059:9962;1306:3;1059:9962;;;;;;1298:12;1059:9962;;;1298:12;1059:9962;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x6080806040526004361015610012575f80fd5b5f905f3560e01c908163024512be14611697575080630a9254e4146115c05780631ed7831c146115425780632ade38801461138b5780633e5e3c231461130d5780633f7286f41461128f5780635c74443b14610cdf57806366d9a9a014610bbe57806385226c8114610b34578063916a17c614610a8c578063b0464fdc146109e4578063b5508aa91461095a578063b8bf5343146105ad578063ba414fa614610588578063d3b5950c1461018c578063e20c9f71146100fe5763fa7626d4146100d9575f80fd5b346100fb57806003193601126100fb57602060ff601f54166040519015158152f35b80fd5b50346100fb57806003193601126100fb5760405180916020601554928381520191601582527f55f448fdea98c4d29eb340757ef0a66cd03dbb9538908a6a81d96026b71ec475915b81811061016d576101698561015d81870382611bf5565b60405191829182611a4c565b0390f35b82546001600160a01b0316845260209093019260019283019201610146565b50346100fb57806003193601126100fb57601f54819060081c6001600160a01b0316803b15610510578180916024604051809481936314c5d75960e01b835261032060048401525af180156104f057610573575b50601f5460408051630b4de8b760e41b815260089290921c6001600160a01b0316919081600481855afa80156105335761025e918491610553575b50610384106040519061022f604083611bf5565b602082527f53686f756c64206e6f7420657865637574652061742066616972207072696365602083015261200f565b803b15610510578180916024604051809481936314c5d75960e01b83526104b060048401525af180156104f05761053e575b50601f5460408051630b4de8b760e41b815260089290921c6001600160a01b0316919081600481855afa80156105335761031a918491610513575b5061038410604051906102df606083611bf5565b602382527f53686f756c642065786563757465206174206d616e6970756c6174656420707260208301526269636560e81b6040830152612077565b7f56696374696d20737566666572732066696e616e6369616c206c6f7373000000602060405161034b604082611bf5565b601d81520152693734b83ab630ba34b7b760b11b6040805161036e606082611bf5565b602a81527f56696374696d206c6f7365732034303020756e6974732064756520746f206d6160208201520152803b15610510578180916024604051809481936314c5d75960e01b835261032060048401525af180156104f0576104fb575b5050601f5460408051630b4de8b760e41b8152918290600490829060081c6001600160a01b03165afa80156104f0576104bd9183916104c0575b50610448604051610418604082611bf5565b601d81527f41747461636b65722072657665727473206d616e6970756c6174696f6e0000006020820152826120b7565b6103206040519161045a608084611bf5565b604483527f56554c4e45524142494c4954593a205072696365206d616e6970756c6174696f60208401527f6e20636175736573206d656173757261626c652066696e616e6369616c206c6f6040840152637373657360e01b606084015214612077565b80f35b6104e2915060403d6040116104e9575b6104da8183611bf5565b810190611c17565b505f610406565b503d6104d0565b6040513d84823e3d90fd5b8161050591611bf5565b6100fb57805f6103cc565b50fd5b61052c915060403d6040116104e9576104da8183611bf5565b505f6102cb565b6040513d85823e3d90fd5b8161054891611bf5565b6100fb57805f610290565b61056c915060403d6040116104e9576104da8183611bf5565b505f61021b565b8161057d91611bf5565b6100fb57805f6101e0565b50346100fb57806003193601126100fb5760206105a3611f58565b6040519015158152f35b50346100fb57806003193601126100fb57601f54819060081c6001600160a01b0316803b15610510578180916024604051809481936314c5d75960e01b835261032060048401525af180156104f057610945575b5050601f5460408051630b4de8b760e41b815260089290921c6001600160a01b031691839181600481865afa9081156104f0578291610925575b5061067d60405161064d604082611bf5565b601e81527f496e697469616c2070726963652073686f756c64206265206e6f726d616c00006020820152826120b7565b61038410916106ce604051610693606082611bf5565b602281527f53686f756c64204e4f542074726967676572206174206e6f726d616c20707269602082015261636560f01b60408201528461200f565b803b156108ea578180916024604051809481936314c5d75960e01b83526103e860048401525af180156104f057610910575b5050601f5460408051630b4de8b760e41b815292918390600490829060081c6001600160a01b03165afa9182156105335783926108ee575b5082604051610748604082611bf5565b601b81527f50726963652073686f756c64206265206d616e6970756c61746564000000000060208201526103e8840361086b575b50506104bd9161038410906107e1604051610798606082611bf5565b603081527f56554c4e45524142494c4954593a2053686f756c64207472696767657220616660208201526f3a32b91036b0b734b83ab630ba34b7b760811b604082015283612077565b159081610863575b50604051906107f9608083611bf5565b604c82527f56554c4e45524142494c49545920434f4e4649524d45443a2041747461636b6560208301527f722063616e20666f72636520657865637574696f6e207669612070726963652060408301526b36b0b734b83ab630ba34b7b760a11b6060830152612077565b90505f6107e9565b5f5160206137d55f395f51905f523b156108ea57816108b491604051809381926388b44c8560e01b83528860048401526103e86024840152606060448401526064830190611a8e565b03815f5160206137d55f395f51905f525afa80156104f0571561077c57816108db91611bf5565b6108e657825f61077c565b8280fd5b5080fd5b61090891925060403d6040116104e9576104da8183611bf5565b50905f610738565b8161091a91611bf5565b6108ea57815f610700565b61093e915060403d6040116104e9576104da8183611bf5565b505f61063b565b8161094f91611bf5565b6100fb57805f610601565b50346100fb57806003193601126100fb5760195461097781611c72565b916109856040519384611bf5565b818352601981527f944998273e477b495144fb8794c914197f3ccb46be2900f4698fd0ef743c9695602084015b8383106109c757604051806101698782611aef565b6001602081926109d685611c8a565b8152019201920191906109b2565b50346100fb57806003193601126100fb57601c54610a0181611c72565b91610a0f6040519384611bf5565b818352601c81527f0e4562a10381dec21b205ed72637e6b1b523bdd0e4d4d50af5cd23dd4500a211602084015b838310610a5157604051806101698782611b4e565b60026020600192604051610a6481611bc5565b848060a01b038654168152610a7a858701611d56565b83820152815201920192019190610a3c565b50346100fb57806003193601126100fb57601d54610aa981611c72565b91610ab76040519384611bf5565b818352601d81527f6d4407e7be21f808e6509aa9fa9143369579dd7d760fe20a2c09680fc146134f602084015b838310610af957604051806101698782611b4e565b60026020600192604051610b0c81611bc5565b848060a01b038654168152610b22858701611d56565b83820152815201920192019190610ae4565b50346100fb57806003193601126100fb57601a54610b5181611c72565b91610b5f6040519384611bf5565b818352601a81527f057c384a7d1c54f3a1b2e5e67b2617b8224fdfd1ea7234eea573a6ff665ff63e602084015b838310610ba157604051806101698782611aef565b600160208192610bb085611c8a565b815201920192019190610b8c565b50346100fb57806003193601126100fb57601b54610bdb81611c72565b610be86040519182611bf5565b818152601b83526020810191837f3ad8aa4f87544323a9d1e5dd902f40c356527a7955687113db5f9a85ad579dc1845b838310610ca457868587604051928392602084019060208552518091526040840160408260051b8601019392905b828210610c5557505050500390f35b91936001919395506020610c948192603f198a820301865288519083610c848351604084526040840190611a8e565b9201519084818403910152611ab2565b9601920192018594939192610c46565b60026020600192604051610cb781611bc5565b610cc086611c8a565b8152610ccd858701611d56565b83820152815201920192019190610c18565b50346100fb57806003193601126100fb57601f54819060081c6001600160a01b0316803b15610510578180916024604051809481936314c5d75960e01b83526103e860048401525af180156104f05761127a575b50601f5460408051630b4de8b760e41b81529160081c6001600160a01b03169082600481845afa918215610533578392611258575b50803b15611254578280916024604051809481936314c5d75960e01b83526101f460048401525af190811561053357839161123f575b5050601f5460408051630b4de8b760e41b815260089290921c6001600160a01b031692919081600481865afa908115611234578491611214575b50604051610de7606082611bf5565b602281527f4e6f20545741503a205072696365206368616e67657320696d6d6564696174656020820152616c7960f01b60408201526101f48203611181575b50610e7e9160405191610e3a606084611bf5565b602a83527f4e6f2070726f74656374696f6e20616761696e73742073756464656e207072696020840152696365206368616e67657360b01b60408401521415612077565b803b15610510578180916024604051809481936314c5d75960e01b8352600160048401525af180156104f05761116c575b50601f5460408051630b4de8b760e41b815260089290921c6001600160a01b0316919081600481855afa90811561053357839161114c575b5060405190610ef7606083611bf5565b602d82527f4e6f206d696e696d756d2073697a653a2045787472656d65206d616e6970756c60208301526c185d1a5bdb88185b1b1bddd959609a1b6040830152600181036110c9575b5050803b15610510578180916024604051809481936314c5d75960e01b835261032060048401525af180156104f0576110b4575b5050601f54819060081c6001600160a01b0316803b15610510578180916024604051809481936314c5d75960e01b83526103e860048401525af180156104f05761109f575b505061100b604051610fcc606082611bf5565b602681527f53686f756c64206e6f742074726967676572206265666f7265206d616e6970756020820152653630ba34b7b760d11b60408201525f61200f565b61101d611016611c2d565b6001612077565b6001611097565b6040516104bd91611036608083611bf5565b604382527f56554c4e45524142494c4954593a204e6f206c6174656e63792070726f74656360208301527f74696f6e20616c6c6f77732073616d652d626c6f636b206d616e6970756c617460408301526234b7b760e91b6060830152612077565b506001611024565b816110a991611bf5565b6100fb57805f610fb9565b816110be91611bf5565b6100fb57805f610f74565b5f5160206137d55f395f51905f523b156111475761111291849160405193849283926388b44c8560e01b8452600484015260016024840152606060448401526064830190611a8e565b03815f5160206137d55f395f51905f525afa90811561053357839115610f40578161113c91611bf5565b61051057815f610f40565b505050fd5b611165915060403d6040116104e9576104da8183611bf5565b505f610ee7565b8161117691611bf5565b6100fb57805f610eaf565b5f5160206137d55f395f51905f523b1561121057846111ca91604051809381926388b44c8560e01b83528660048401526101f46024840152606060448401526064830190611a8e565b03815f5160206137d55f395f51905f525afa9081156112055785916111f0575b50610e26565b816111fa91611bf5565b61114757835f6111ea565b6040513d87823e3d90fd5b8480fd5b61122d915060403d6040116104e9576104da8183611bf5565b505f610dd8565b6040513d86823e3d90fd5b8161124991611bf5565b61051057815f610d9e565b5050fd5b61127291925060403d6040116104e9576104da8183611bf5565b50905f610d68565b8161128491611bf5565b6100fb57805f610d33565b50346100fb57806003193601126100fb5760405180916020601754928381520191601782527fc624b66cc0138b8fabc209247f72d758e1cf3343756d543badbf24212bed8c15915b8181106112ee576101698561015d81870382611bf5565b82546001600160a01b03168452602090930192600192830192016112d7565b50346100fb57806003193601126100fb5760405180916020601854928381520191601882527fb13d2d76d1f4b7be834882e410b3e3a8afaf69f83600ae24db354391d2378d2e915b81811061136c576101698561015d81870382611bf5565b82546001600160a01b0316845260209093019260019283019201611355565b50346100fb57806003193601126100fb57601e546113a881611c72565b6113b56040519182611bf5565b818152601e83526020810191837f50bb669a95c7b50b7e8a6f09454034b2b14cf2b85c730dca9a539ca82cb6e350845b8383106114b95786858760405192839260208401906020855251809152604084019160408260051b8601019392815b8383106114215786860387f35b919395509193603f198782030183528551906020604082019260018060a01b0381511683520151916040602083015282518091526060820190602060608260051b850101940192855b82811061148e57505050505060208060019297019301930190928695949293611414565b90919293946020806114ac600193605f198782030189528951611a8e565b970195019392910161146a565b6040516114c581611bc5565b82546001600160a01b031681526001830180546114e181611c72565b916114ef6040519384611bf5565b8183528a526020808b20908b9084015b8382106115255750505050600192826020928360029501528152019201920191906113e5565b60016020819261153486611c8a565b8152019301910190916114ff565b50346100fb57806003193601126100fb5760405180916020601654928381520191601682527fd833147d7dc355ba459fc788f669e58cfaf9dc25ddcd0702e87d69c7b5124289915b8181106115a1576101698561015d81870382611bf5565b82546001600160a01b031684526020909301926001928301920161158a565b50346100fb57806003193601126100fb5760405160e18082019082821067ffffffffffffffff8311176116835790829161210f8339039082f0801561167657601f8054610100600160a81b03191660089290921b610100600160a81b03169190911790556040516115e58082019067ffffffffffffffff821183831017611683579082916121f08339039082f080156116765760018060a01b03166bffffffffffffffffffffffff60a01b602054161760205580f35b50604051903d90823e3d90fd5b634e487b7160e01b84526041600452602484fd5b905034611a48575f366003190112611a4857601f5460081c6001600160a01b0316803b15611a4857816024815f80946314c5d75960e01b835261038460048401525af18015611a3d57611a2a575b50601f5460408051630b4de8b760e41b815260089290921c6001600160a01b031691839181600481865afa9081156104f0578291611a0a575b506103b61091611765604051611735604082611bf5565b601c81527f53686f756c64206e6f74207472696767657220696e697469616c6c790000000060208201528461200f565b803b156108ea578180916024604051809481936314c5d75960e01b83526103e860048401525af180156104f0576119f5575b5050601f5460408051630b4de8b760e41b815260089290921c6001600160a01b031691849181600481865afa9081156104f05782916119d5575b506103b610916117e86117e2611c2d565b84612077565b803b156108ea578180916024604051809481936314c5d75960e01b835261038460048401525af180156104f0576119c0575b5050601f5460408051630b4de8b760e41b8152918290600490829060081c6001600160a01b03165afa9081156112345784916119a0575b5083604051611861604082611bf5565b601e81527f50726963652073686f756c642072657475726e20746f20696e697469616c00006020820152610384831492831561191a575b5050506104bd92159182611912575b508161190a575b50604051906118be606083611bf5565b603482527f56554c4e45524142494c4954593a2041746f6d6963206d616e6970756c6174696020830152731bdb88185d1d1858dac81cdd58d8d95cdcd99d5b60621b6040830152612077565b90505f6118ae565b91505f6118a7565b5f5160206137d55f395f51905f523b156108e65761196491839160405193849283926388b44c8560e01b845260048401526103846024840152606060448401526064830190611a8e565b03815f5160206137d55f395f51905f525afa80156104f057611987575b80611898565b8161199191611bf5565b61199c57835f611981565b8380fd5b6119b9915060403d6040116104e9576104da8183611bf5565b505f611851565b816119ca91611bf5565b6108e657825f61181a565b6119ee915060403d6040116104e9576104da8183611bf5565b505f6117d1565b816119ff91611bf5565b6108ea57815f611797565b611a23915060403d6040116104e9576104da8183611bf5565b505f61171e565b611a3691505f90611bf5565b5f5f6116e5565b6040513d5f823e3d90fd5b5f80fd5b60206040818301928281528451809452019201905f5b818110611a6f5750505090565b82516001600160a01b0316845260209384019390920191600101611a62565b805180835260209291819084018484015e5f828201840152601f01601f1916010190565b90602080835192838152019201905f5b818110611acf5750505090565b82516001600160e01b031916845260209384019390920191600101611ac2565b602081016020825282518091526040820191602060408360051b8301019401925f915b838310611b2157505050505090565b9091929394602080611b3f600193603f198682030187528951611a8e565b97019301930191939290611b12565b602081016020825282518091526040820191602060408360051b8301019401925f915b838310611b8057505050505090565b9091929394602080611bb6600193603f198682030187526040838b51878060a01b03815116845201519181858201520190611ab2565b97019301930191939290611b71565b6040810190811067ffffffffffffffff821117611be157604052565b634e487b7160e01b5f52604160045260245ffd5b90601f8019910116810190811067ffffffffffffffff821117611be157604052565b9190826040910312611a48576020825192015190565b60405190611c3c606083611bf5565b60218252603760f91b6040837f53686f756c642074726967676572206166746572206d616e6970756c6174696f60208201520152565b67ffffffffffffffff8111611be15760051b60200190565b90604051915f8154908160011c9260018316928315611d4c575b602085108414611d38578487528693908115611d165750600114611cd2575b50611cd092500383611bf5565b565b90505f9291925260205f20905f915b818310611cfa575050906020611cd0928201015f611cc3565b6020919350806001915483858901015201910190918492611ce1565b905060209250611cd094915060ff191682840152151560051b8201015f611cc3565b634e487b7160e01b5f52602260045260245ffd5b93607f1693611ca4565b90604051918281549182825260208201905f5260205f20925f905b806007830110611eb357611cd0945491818110611e94575b818110611e75575b818110611e56575b818110611e37575b818110611e18575b818110611df9575b818110611ddc575b10611dc7575b500383611bf5565b6001600160e01b03191681526020015f611dbf565b602083811b6001600160e01b031916855290930192600101611db9565b604083901b6001600160e01b0319168452602090930192600101611db1565b606083901b6001600160e01b0319168452602090930192600101611da9565b608083901b6001600160e01b0319168452602090930192600101611da1565b60a083901b6001600160e01b0319168452602090930192600101611d99565b60c083901b6001600160e01b0319168452602090930192600101611d91565b60e083901b6001600160e01b0319168452602090930192600101611d89565b916008919350610100600191865463ffffffff60e01b8160e01b16825263ffffffff60e01b8160c01b16602083015263ffffffff60e01b8160a01b16604083015263ffffffff60e01b8160801b16606083015263ffffffff60e01b8160601b16608083015263ffffffff60e01b8160401b1660a083015263ffffffff60e01b8160201b1660c083015263ffffffff60e01b1660e0820152019401920185929391611d71565b60085460ff1615611f6857600190565b604051630667f9d760e41b81525f5160206137d55f395f51905f5260048201526519985a5b195960d21b60248201526020816044815f5160206137d55f395f51905f525afa908115611a3d575f91611fc1575b50151590565b90506020813d602011611feb575b81611fdc60209383611bf5565b81010312611a4857515f611fbb565b3d9150611fcf565b60409061200c9392151581528160208201520190611a8e565b90565b80612018575050565b5f5160206137d55f395f51905f523b15611a4857604051637ba0480960e01b8152915f918391829161204e919060048401611ff3565b03815f5160206137d55f395f51905f525afa8015611a3d5761206d5750565b5f611cd091611bf5565b8015612081575050565b5f5160206137d55f395f51905f523b15611a485760405163a34edc0360e01b8152915f918391829161204e919060048401611ff3565b61032081036120c4575050565b5f5160206137d55f395f51905f523b15611a485761204e915f9160405193849283926388b44c8560e01b845260048401526103206024840152606060448401526064830190611a8e56fe60808060405234601e576103e85f556103f260015560be908160238239f35b5f80fdfe60808060405260043610156011575f80fd5b5f3560e01c806314c5d75914607257806390a5a70e14605b5763b4de8b70146037575f80fd5b346057575f3660031901126057576040905f546001549082526020820152f35b5f80fd5b346057576020366003190112605757600435600155005b3460575760203660031901126057576004355f5500fea26469706673582212203b24693c55faa5db112286940fd07492a25d29243c76716f8211008fb7b0852964736f6c634300081c0033610140604052346100d657306080524660a0525f60c0525f60e0525f61010052306101205263409feecd1954600181166100c95760011c6002600160401b031901610084575b60405161150a90816100db823960805181505060a05181505060c05181505060e051815050610100518150506101205181818161087d01526109100152f35b6002600160411b0363409feecd19556001600160401b0360209081527fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d29080a1610045565b63f92ee8a95f526004601cfd5b5f80fdfe60806040526004361015610011575f80fd5b5f3560e01c806310012a1514610b8e5780631691267114610b5f5780631a4e971f14610ae65780631fbe2e4f14610aa75780632569296214610a5e5780632d0335ab14610a265780633bcb61ef14610a0d578063487ed96e146109f85780634f1ef286146108d857806352d1902d1461086a57806354d1f13d146108265780635b31c2e6146107b15780636b446f49146107985780636cce95f0146106595780636e0849051461059b578063715018a61461054e57806384b0196e146104b05780638da5cb5b14610484578063950b2e511461046b578063df477d5014610204578063e6bc6b56146101e1578063f04e283e14610194578063f2fde38b146101575763fee81cf414610121575f80fd5b346101535760203660031901126101535761013a610d14565b63389a75e1600c525f52602080600c2054604051908152f35b5f80fd5b60203660031901126101535761016b610d14565b61017361132a565b8060601b156101875761018590611446565b005b637448fbae5f526004601cfd5b6020366003190112610153576101a8610d14565b6101b061132a565b63389a75e1600c52805f526020600c2090815442116101d4575f6101859255611446565b636f5e88185f526004601cfd5b346101535760206101fa6101f436610d2a565b91611255565b6040519015158152f35b61021961021036610d9c565b90829392611043565b1561045c5760608101359081341061044d5760e08101916001600160e01b031961024284610f3c565b165f525f60205260ff60405f2054161561043e5761025f82610e13565b60405160609190911b6bffffffffffffffffffffffff19166020820190815260808401356034830152906102a081605481015b03601f198101835282610c01565b5190205f52600160205260405f20600160ff19825416179055602082016004604060018060a01b036102d184610e13565b16815192838092630b4de8b760e41b82525afa908115610433575f91610401575b5060c0840161030081611000565b90816103f2575b81156103ce575b50156103b3575f8086868661034b61035c61033161032b8a610e13565b95610f3c565b93610292610343610100830183610f51565b949092610e13565b604051948593602085019889610f83565b51925af1610368610fd1565b901561038a5761038690604051918291602083526020830190610d78565b0390f35b6040516315fcd67560e01b8152602060048201529081906103af906024830190610d78565b0390fd5b8360409163eaac67f160e01b5f52600452013560245260445ffd5b6103d89150611000565b15806103e5575b8661030e565b50806040850135116103df565b90508160408601351090610307565b90506040813d60401161042b575b8161041c60409383610c01565b810103126101535751856102f2565b3d915061040f565b6040513d5f823e3d90fd5b63d02b530d60e01b5f5260045ffd5b63044044a560e21b5f5260045ffd5b630e751dc360e31b5f5260045ffd5b346101535760206101fa61047e36610d2a565b9161113f565b34610153575f36600319011261015357638b78c6d819546040516001600160a01b039091168152602090f35b34610153575f366003190112610153576104ef6104fd6104ce611489565b604092919251938493600f60f81b855260e0602086015260e0850190610d78565b908382036040850152610d78565b4660608301523060808301525f60a083015281810360c083015260206060519182815201906080905f5b818110610535575050500390f35b8251845285945060209384019390920191600101610527565b5f3660031901126101535761056161132a565b5f638b78c6d819547f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08280a3600160ff1b638b78c6d81955005b6105b06105a736610d2a565b90829392611255565b1561045c5760408101359081341061044d5760a081016001600160e01b03196105d882610f3c565b165f525f60205260ff60405f2054161561043e5760608201359160018301809311610645575f9384936001600160a01b0361061284610e13565b1685526002602052604085205561034b61035c61063461032b60208601610e13565b9361029261034360c0830183610f51565b634e487b7160e01b5f52601160045260245ffd5b3461015357604036600319011261015357610672610d14565b6024356001600160401b03811161015357610691903690600401610c36565b63409feecd19918254806003855561076f575b50638b78c6d81954610762576001600160a01b0316801560ff1b8117638b78c6d819555f7f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08180a35f5b82825182101561072b57506001906001600160e01b031961070f8285610deb565b51165f525f60205260405f208260ff19825416179055016106ee565b8061073257005b6002905560016020527fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d2602080a1005b630dc149f05f526004601cfd5b600181819593951c14303b101561078b5760ff1b1b91836106a4565b63f92ee8a95f526004601cfd5b346101535760206101fa6107ab36610d9c565b91611043565b34610153576020366003190112610153576004356001600160401b038111610153576107e1903690600401610c36565b6107e961132a565b5f5b8151811015610185576001906001600160e01b031961080a8285610deb565b51165f525f60205260405f208260ff19825416179055016107eb565b5f3660031901126101535763389a75e1600c52335f525f6020600c2055337ffa7b8eab7da67f412cc9575ed43464468f9bfbae89d1675917346ca6d8fe3c925f80a2005b34610153575f36600319011261015357307f0000000000000000000000000000000000000000000000000000000000000000036108cb5760206040517f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc8152f35b639f03a0265f526004601cfd5b6040366003190112610153576108ec610d14565b6024356001600160401b0381116101535761090b903690600401610cad565b9091307f0000000000000000000000000000000000000000000000000000000000000000146108cb5761093c61132a565b60018060a01b03166352d1902d6001527f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc602060016004601d855afa51036109ea57807fbc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3b5f80a281817f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc556109cd57005b815f926040519485378338925af4156109e257005b3d5f823e3d90fd5b6355299b496001526004601dfd5b6105b0610a0436610d2a565b9082939261113f565b346101535760206101fa610a2036610cda565b91610e27565b34610153576020366003190112610153576001600160a01b03610a47610d14565b165f526002602052602060405f2054604051908152f35b5f3660031901126101535763389a75e1600c52335f526202a30042016020600c2055337fdbf36a107da19e49527a7176a1babf963b4b0ff8cde35ee35d6cd8f1f9ac7e1d5f80a2005b346101535760203660031901126101535760043563ffffffff60e01b8116809103610153575f525f602052602060ff60405f2054166040519015158152f35b3461015357610b00610af736610cda565b90829392610e27565b1561045c57610292610b42610b1483610e13565b6040519283916020808401960135908690916034926001600160601b03199060601b16825260148201520190565b5190205f52600160205260405f20600160ff198254161790555f80f35b34610153576020366003190112610153576004355f526001602052602060ff60405f2054166040519015158152f35b34610153576020366003190112610153576004356001600160401b03811161015357610bbe903690600401610c36565b610bc661132a565b5f5b8151811015610185576001906001600160e01b0319610be78285610deb565b51165f525f60205260405f2060ff19815416905501610bc8565b90601f801991011681019081106001600160401b03821117610c2257604052565b634e487b7160e01b5f52604160045260245ffd5b81601f82011215610153578035916001600160401b038311610c22578260051b9160405193610c686020850186610c01565b845260208085019382010191821161015357602001915b818310610c8c5750505090565b82356001600160e01b03198116810361015357815260209283019201610c7f565b9181601f84011215610153578235916001600160401b038311610153576020838186019501011161015357565b906003198201608081126101535760601361015357600491606435906001600160401b03821161015357610d1091600401610cad565b9091565b600435906001600160a01b038216820361015357565b906040600319830112610153576004356001600160401b0381116101535760e081840360031901126101535760040191602435906001600160401b03821161015357610d1091600401610cad565b805180835260209291819084018484015e5f828201840152601f01601f1916010190565b906040600319830112610153576004356001600160401b0381116101535761012081840360031901126101535760040191602435906001600160401b03821161015357610d1091600401610cad565b8051821015610dff5760209160051b010190565b634e487b7160e01b5f52603260045260245ffd5b356001600160a01b03811681036101535790565b90604082013592834211610f2d57610eab91610ea6610e4585610e13565b6020860135966040519060208201927f3ec94f0b764242014d547a29ab20da6056912fc1b658b4ad364cd66f407a8005845260018060a01b03166040830152886060830152608082015260808152610e9e60a082610c01565b519020611346565b6113bb565b91610292610ee6610ebb84610e13565b9260405192839160208301958690916034926001600160601b03199060601b16825260148201520190565b5190205f52600160205260ff60405f205416610f1f576001600160a01b0390610f0e90610e13565b6001600160a01b0390921691161490565b623f613760e71b5f5260045ffd5b631ab7da6b60e01b5f5260045ffd5b356001600160e01b0319811681036101535790565b903590601e198136030182121561015357018035906001600160401b0382116101535760200191813603831361015357565b6001600160e01b0319909116815260189392918290600483013701906001600160601b03199060601b1660048201520190565b6001600160401b038111610c2257601f01601f191660200190565b3d15610ffb573d90610fe282610fb6565b91610ff06040519384610c01565b82523d5f602084013e565b606090565b3580151581036101535790565b92919261101982610fb6565b916110276040519384610c01565b829481845281830111610153578281602093845f960137010152565b9060a082013592834211610f2d57610eab91610ea661106185610e13565b61106d60208701610e13565b60808701359761107f60c08901611000565b61108b60e08a01610f3c565b906110a461109d6101008c018c610f51565b369161100d565b60208151910120926040519460208601967f1122e3187221cfd9d40ac63fa267c39b342d8710972523d6cbf343f43c0322f7885260018060a01b0316604087015260018060a01b0316606086015260408b0135608086015260608b013560a08601528b60c086015260e0850152151561010084015263ffffffff60e01b166101208301526101408201526101408152610e9e61016082610c01565b9190608083013591824211610f2d5761121491610ea661115e86610e13565b61116a60208801610e13565b60608801359661117c60a08a01610f3c565b61118c61109d60c08c018c610f51565b60208151910120916040519360208501957f199c879f0d8420ea1ff045984f2ac5541f91073952f48791833c79f1bea4534e875260018060a01b0316604086015260018060a01b0316606085015260408b013560808501528960a085015260c084015263ffffffff60e01b1660e08301526101008201526101008152610e9e61012082610c01565b906001600160a01b0361122684610e13565b165f52600260205260405f20541115918261124057505090565b9091506001600160a01b0390610f0e90610e13565b9190608083013591824211610f2d5761121491610ea661127486610e13565b61128060208801610e13565b60608801359661129260a08a01610f3c565b6112a261109d60c08c018c610f51565b60208151910120916040519360208501957f4a5f28af10183842f321d76044fae00f1697f0bc9a8eae077fe67857cc5d6404875260018060a01b0316604086015260018060a01b0316606085015260408b013560808501528960a085015260c084015263ffffffff60e01b1660e08301526101008201526101008152610e9e61012082610c01565b638b78c6d81954330361133957565b6382b429005f526004601cfd5b60a0611350611489565b90602081519101209060208151910120604051917f8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f835260208301526040820152466060820152306080820152206719010000000000005f52601a52603a526042601820905f603a52565b929190600191604051945f526040811461141b576041146114015750506020600160805f805b5afa51913d156113f4575f606052604052565b638baa579f5f526004601cfd5b60805f602093604084816001960135841a875281376113e1565b5060805f6020938385600195013590601b8260ff1c01875235604052848060ff1b03166060526113e1565b60018060a01b031680638b78c6d819547f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e05f80a3801560ff1b17638b78c6d81955565b60409081516114988382610c01565b600d81526c25bab93aa337b93bb0b93232b960991b6020820152916114bf81519182610c01565b60058152640312e302e360dc1b60208201529056fea264697066735822122025a7e3a0dac83d99c2251871374b6d597021638e3d56a231206e91c42f04d7a564736f6c634300081c00330000000000000000000000007109709ecfa91a80626ff3989d68f67f5b1dd12da26469706673582212206e41719e39ff05b305d5ad62e91851d8e46dd5761161f43130d9f0a5662a60a564736f6c634300081c0033", "sourceMap": "1059:9962:65:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1065:26:32;1059:9962:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2723:18:25;1059:9962:65;;;;;;;2723:18:25;1059:9962:65;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;1059:9962:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8543:13;1059:9962;;;;;-1:-1:-1;;;;;1059:9962:65;8543:36;;;;;1059:9962;;;8543:36;1059:9962;;;;;;;;;8543:36;;8271:3;1059:9962;8543:36;;1059:9962;8543:36;;;;;;;;1059:9962;-1:-1:-1;8543:13:65;1059:9962;;;;-1:-1:-1;;;8615:26:65;;1059:9962;;;;;-1:-1:-1;;;;;1059:9962:65;;;;;;;8615:26;;;;;;8812:73;8615:26;;;;;1059:9962;8749:53;2467:3;10842:27;1059:9962;;;;;;;:::i;:::-;;;;;;;;;8812:73;:::i;:::-;8966:43;;;;;1059:9962;;;8543:36;1059:9962;;;;;;;;;8966:43;;8441:4;1059:9962;8966:43;;1059:9962;8966:43;;;;;;;;1059:9962;-1:-1:-1;8543:13:65;1059:9962;;;;-1:-1:-1;;;9041:26:65;;1059:9962;;;;;-1:-1:-1;;;;;1059:9962:65;;;;;;;9041:26;;;;;;9242:82;9041:26;;;;;1059:9962;9183:49;2467:3;10842:27;1059:9962;;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;1059:9962:65;;;;9242:82;:::i;:::-;1059:9962;;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;;1059:9962:65;;;;;;;:::i;:::-;;;;;;;;;;;9810:36;;;;;1059:9962;;;8543:36;1059:9962;;;;;;;;;9810:36;;8271:3;1059:9962;9810:36;;1059:9962;9810:36;;;;;;;;1059:9962;-1:-1:-1;;8543:13:65;1059:9962;;;;-1:-1:-1;;;9883:26:65;;1059:9962;;;;;;;;;-1:-1:-1;;;;;1059:9962:65;9883:26;;;;;;10073:162;9883:26;;;;;1059:9962;;9919:67;1059:9962;;;;;;:::i;:::-;;;;;;;;;9919:67;;:::i;:::-;8271:3;1059:9962;;;;;;;:::i;:::-;;;;;;;;;;;;;;-1:-1:-1;;;1059:9962:65;;;;10115:26;10073:162;:::i;:::-;1059:9962;;9883:26;;;;1059:9962;9883:26;1059:9962;9883:26;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;;;;1059:9962;;;;;;;;;9810:36;;;;;:::i;:::-;1059:9962;;9810:36;;;;;1059:9962;;9041:26;;;;1059:9962;9041:26;1059:9962;9041:26;;;;;;;:::i;:::-;;;;;;1059:9962;;;;;;;;;8966:43;;;;;:::i;:::-;1059:9962;;8966:43;;;;8615:26;;;;1059:9962;8615:26;1059:9962;8615:26;;;;;;;:::i;:::-;;;;;8543:36;;;;;:::i;:::-;1059:9962;;8543:36;;;;1059:9962;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;2173:13;1059:9962;;;;;-1:-1:-1;;;;;1059:9962:65;2173:38;;;;;1059:9962;;;2173:38;1059:9962;;;;;;;;;2173:38;;2123:3;1059:9962;2173:38;;1059:9962;2173:38;;;;;;;;1059:9962;-1:-1:-1;;2173:13:65;1059:9962;;;;-1:-1:-1;;;2277:26:65;;1059:9962;;;;;-1:-1:-1;;;;;1059:9962:65;;;;;;;;2277:26;;;;;;;;;;;1059:9962;;2313:67;1059:9962;;;;;;:::i;:::-;;;;;;;;;2313:67;;:::i;:::-;2467:3;10842:27;1059:9962;2798:72;1059:9962;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;1059:9962:65;;;;2798:72;;:::i;:::-;3039:43;;;;;1059:9962;;;2173:38;1059:9962;;;;;;;;;3039:43;;2982:4;1059:9962;3039:43;;1059:9962;3039:43;;;;;;;;1059:9962;-1:-1:-1;;2173:13:65;1059:9962;;;;-1:-1:-1;;;3151:26:65;;1059:9962;;;;;;;;;;-1:-1:-1;;;;;1059:9962:65;3151:26;;;;;;;;;;;1059:9962;;;;;;;;;:::i;:::-;;;;;;;;;2982:4;2925:13:20;;2921:73;;1059:9962:65;3388:49;;3629:182;3388:49;2467:3;10842:27;1059:9962;3447:94;1059:9962;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;1059:9962:65;;;;3447:94;;:::i;:::-;3653:22;:56;;;;1059:9962;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;-1:-1:-1;;;1059:9962:65;;;;3629:182;:::i;3653:56::-;;;;;;2921:73:20;-1:-1:-1;;;;;;;;;;;2954:29:20;;;;1059:9962:65;;;;;;;;;;;;2954:29:20;;;1059:9962:65;2954:29:20;;1059:9962:65;2982:4;2173:38;1059:9962;;;;;;;;;;;;;:::i;:::-;2954:29:20;;-1:-1:-1;;;;;;;;;;;2954:29:20;;;;;;;2921:73;2954:29;;;;;:::i;:::-;1059:9962:65;;2954:29:20;;2921:73;;1059:9962:65;;;;2954:29:20;1059:9962:65;;;3151:26;;;;;1059:9962;3151:26;1059:9962;3151:26;;;;;;;:::i;:::-;;;;;;3039:43;;;;;:::i;:::-;1059:9962;;3039:43;;;;2277:26;;;;1059:9962;2277:26;1059:9962;2277:26;;;;;;;:::i;:::-;;;;;2173:38;;;;;:::i;:::-;1059:9962;;2173:38;;;;1059:9962;;;;;;;;;;;;;2575:18:25;1059:9962:65;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;2575:18:25;1059:9962:65;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;2876:18:25;1059:9962:65;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;2876:18:25;1059:9962:65;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3653:18:25;1059:9962:65;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;3653:18:25;1059:9962:65;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3162:18:25;1059:9962:65;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;3162:18:25;1059:9962:65;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;3346:26:25;1059:9962:65;;;;:::i;:::-;;;;;;;:::i;:::-;;;;3346:26:25;1059:9962:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6150:13;1059:9962;;;;;-1:-1:-1;;;;;1059:9962:65;6150:31;;;;;1059:9962;;;6150:31;1059:9962;;;;;;;;;6150:31;;6176:4;1059:9962;6150:31;;1059:9962;6150:31;;;;;;;;1059:9962;-1:-1:-1;6150:13:65;1059:9962;;;;-1:-1:-1;;;6211:26:65;;1059:9962;;;-1:-1:-1;;;;;1059:9962:65;;;;;;6211:26;;;;;;;;;;;1059:9962;6248:30;;;;;;1059:9962;;;6150:31;1059:9962;;;;;;;;;6248:30;;6274:3;1059:9962;6248:30;;1059:9962;6248:30;;;;;;;;;;;1059:9962;-1:-1:-1;;6150:13:65;1059:9962;;;;-1:-1:-1;;;6326:26:65;;1059:9962;;;;;-1:-1:-1;;;;;1059:9962:65;;;;;;;;6326:26;;;;;;;;;;;1059:9962;;;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;1059:9962:65;;;;6274:3;2925:13:20;;2921:73;;1059:9962:65;;6498:74;1059:9962;;;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;1059:9962:65;;;;6509:16;;6498:74;:::i;:::-;6716:28;;;;;1059:9962;;;6150:31;1059:9962;;;;;;;;;6716:28;;6742:1;1059:9962;6716:28;;1059:9962;6716:28;;;;;;;;1059:9962;-1:-1:-1;6150:13:65;1059:9962;;;;-1:-1:-1;;;6804:26:65;;1059:9962;;;;;-1:-1:-1;;;;;1059:9962:65;;;;;;;6804:26;;;;;;;;;;;1059:9962;;;;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;1059:9962:65;;;;6742:1;2925:13:20;;2921:73;;1059:9962:65;7249:40;;;;;;;1059:9962;;;6150:31;1059:9962;;;;;;;;;7249:40;;7065:3;1059:9962;7249:40;;1059:9962;7249:40;;;;;;;;1059:9962;-1:-1:-1;;6150:13:65;1059:9962;;;;;-1:-1:-1;;;;;1059:9962:65;7384:43;;;;;1059:9962;;;6150:31;1059:9962;;;;;;;;;7384:43;;6176:4;1059:9962;7384:43;;1059:9962;7384:43;;;;;;;;1059:9962;7462:51;;7597:73;1059:9962;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;1059:9962:65;;;;;7597:73;:::i;:::-;7680:66;1059:9962;;:::i;:::-;6742:1;7680:66;:::i;:::-;7780:19;:40;;1059:9962;;;7756:157;;1059:9962;;;;:::i;:::-;;;;;;;;;;;;;;-1:-1:-1;;;1059:9962:65;;;;7756:157;:::i;7780:40::-;-1:-1:-1;6742:1:65;7780:40;;7384:43;;;;;:::i;:::-;1059:9962;;7384:43;;;;7249:40;;;;;:::i;:::-;1059:9962;;7249:40;;;;2921:73:20;-1:-1:-1;;;;;;;;;;;2954:29:20;;;;1059:9962:65;;;;;;;;;;;;;;2954:29:20;;1059:9962:65;2954:29:20;;1059:9962:65;6742:1;6150:31;1059:9962;;;;;;;;;;;;;:::i;:::-;2954:29:20;;-1:-1:-1;;;;;;;;;;;2954:29:20;;;;;;;;;;2921:73;2954:29;;;;;:::i;:::-;1059:9962:65;;2954:29:20;;2921:73;;2954:29;1059:9962:65;;;;6804:26;;;;1059:9962;6804:26;1059:9962;6804:26;;;;;;;:::i;:::-;;;;;6716:28;;;;;:::i;:::-;1059:9962;;6716:28;;;;2921:73:20;-1:-1:-1;;;;;;;;;;;2954:29:20;;;;1059:9962:65;;;;;;;;;;;;2954:29:20;;;1059:9962:65;2954:29:20;;1059:9962:65;6274:3;6150:31;1059:9962;;;;;;;;;;;;;:::i;:::-;2954:29:20;;-1:-1:-1;;;;;;;;;;;2954:29:20;;;;;;;;;;;2921:73;;;;2954:29;;;;;:::i;:::-;1059:9962:65;;2954:29:20;;;;;1059:9962:65;;;;;;;;;2954:29:20;1059:9962:65;;;6326:26;;;;1059:9962;6326:26;1059:9962;6326:26;;;;;;;:::i;:::-;;;;;;1059:9962;;;;;;;;;6248:30;;;;;:::i;:::-;1059:9962;;6248:30;;;;;1059:9962;;;6211:26;;;;;1059:9962;6211:26;1059:9962;6211:26;;;;;;;:::i;:::-;;;;;;6150:31;;;;;:::i;:::-;1059:9962;;6150:31;;;;1059:9962;;;;;;;;;;;;;;;;;;3501:18:25;1059:9962:65;;;;;;;3501:18:25;1059:9962:65;;;;;;;;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;1059:9962:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3794:16:25;1059:9962:65;;;;;;;3794:16:25;1059:9962:65;;;;;;;;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;1059:9962:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3948:19:25;1059:9962:65;;;;:::i;:::-;;;;;;;:::i;:::-;;;;3948:19:25;1059:9962:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;1059:9962:65;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3018:16:25;1059:9962:65;;;;;;;3018:16:25;1059:9962:65;;;;;;;;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;1059:9962:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1411:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;1395:45;1059:9962;;-1:-1:-1;;;;;;1059:9962:65;;;;;;-1:-1:-1;;;;;1059:9962:65;;;;;;;;;1499:19;;;;;1411:29;1499:19;;;;;;;;;;;;;;;;;;;;;;1059:9962;;;;;;;;;1483:35;1059:9962;;;1483:35;1059:9962;;;1499:19;1059:9962;;;;;;;;;;;1499:19;-1:-1:-1;;;1059:9962:65;;;;;;;;;;;;;;;;-1:-1:-1;;1059:9962:65;;;;4462:13;1059:9962;;;-1:-1:-1;;;;;1059:9962:65;4462:39;;;;;1059:9962;4462:39;1059:9962;;;;;;;4462:39;;4449:3;1059:9962;4462:39;;1059:9962;4462:39;;;;;;;;1059:9962;-1:-1:-1;4462:13:65;1059:9962;;;;-1:-1:-1;;;4536:26:65;;1059:9962;;;;;-1:-1:-1;;;;;1059:9962:65;;;;;;;;4536:26;;;;;;;;;;;1059:9962;4599:53;4192:3;10842:27;1059:9962;4662:64;1059:9962;;;;;;:::i;:::-;;;;;;;;;4662:64;;:::i;:::-;4958:43;;;;;1059:9962;;;4462:39;1059:9962;;;;;;;;;4958:43;;4907:4;1059:9962;4958:43;;1059:9962;4958:43;;;;;;;;1059:9962;-1:-1:-1;;4462:13:65;1059:9962;;;;-1:-1:-1;;;5103:26:65;;1059:9962;;;;;-1:-1:-1;;;;;1059:9962:65;;;;;;;;5103:26;;;;;;;;;;;1059:9962;5170:57;4192:3;10842:27;1059:9962;5237:72;1059:9962;;:::i;:::-;5237:72;;:::i;:::-;5369:39;;;;;1059:9962;;;4462:39;1059:9962;;;;;;;;;5369:39;;4449:3;1059:9962;5369:39;;1059:9962;5369:39;;;;;;;;1059:9962;-1:-1:-1;;4462:13:65;1059:9962;;;;-1:-1:-1;;;5492:26:65;;1059:9962;;;;;;;;;-1:-1:-1;;;;;1059:9962:65;5492:26;;;;;;;;;;;1059:9962;;;;;;;;;:::i;:::-;;;;;;;;;4449:3;2925:13:20;;;;;2921:73;;1059:9962:65;5693:20;;;5669:177;5693:20;;:47;;;;1059:9962;5693:75;;;;1059:9962;;;;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;1059:9962:65;;;;5669:177;:::i;5693:75::-;;;;;;:47;;-1:-1:-1;5693:47:65;;;2921:73:20;-1:-1:-1;;;;;;;;;;;2954:29:20;;;;1059:9962:65;;;;;;;;;;;;;;2954:29:20;;1059:9962:65;2954:29:20;;1059:9962:65;4449:3;4462:39;1059:9962;;;;;;;;;;;;;:::i;:::-;2954:29:20;;-1:-1:-1;;;;;;;;;;;2954:29:20;;;;;;;;2921:73;;;;2954:29;;;;;:::i;:::-;1059:9962:65;;2954:29:20;;;;1059:9962:65;;;;5492:26;;;;1059:9962;5492:26;1059:9962;5492:26;;;;;;;:::i;:::-;;;;;5369:39;;;;;:::i;:::-;1059:9962;;5369:39;;;;5103:26;;;;1059:9962;5103:26;1059:9962;5103:26;;;;;;;:::i;:::-;;;;;4958:43;;;;;:::i;:::-;1059:9962;;4958:43;;;;4536:26;;;;1059:9962;4536:26;1059:9962;4536:26;;;;;;;:::i;:::-;;;;;4462:39;;;;1059:9962;4462:39;;:::i;:::-;1059:9962;4462:39;;;;1059:9962;;;;;;;;;4462:39;1059:9962;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;;;1059:9962:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1059:9962:65;;;;;;;;-1:-1:-1;;1059:9962:65;;;;:::o;:::-;;;;;;;;;;;;;;-1:-1:-1;1059:9962:65;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;;;;1059:9962:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;-1:-1:-1;1059:9962:65;;;;;-1:-1:-1;1059:9962:65;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;1059:9962:65;;;;;;;;;:::o;:::-;;;;;;;;;;;:::o;:::-;;;;;-1:-1:-1;1059:9962:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:::o;:::-;;;-1:-1:-1;1059:9962:65;;;;;-1:-1:-1;1059:9962:65;;-1:-1:-1;1059:9962:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1059:9962:65;;;;;-1:-1:-1;1059:9962:65;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1059:9962:65;;-1:-1:-1;1059:9962:65;;-1:-1:-1;1059:9962:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;;1059:9962:65;;;;;;;;;;;;;-1:-1:-1;;;;;;1059:9962:65;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;1059:9962:65;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;1059:9962:65;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;1059:9962:65;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;1059:9962:65;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;1059:9962:65;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;1059:9962:65;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1306:195:20;1365:7;1059:9962:65;;;;;;1395:4:20;1388:11;:::o;1361:134::-;1059:9962:65;;;;;1437:33:20;;-1:-1:-1;;;;;;;;;;;1437:33:20;;;1059:9962:65;192:59:20;;;1255:17;;;1059:9962:65;1255:17:20;1437:33;;;-1:-1:-1;;;;;;;;;;;1437:33:20;;;;;;;1059:9962:65;1437:33:20;;;1361:134;1437:47;;;1430:54;:::o;1437:33::-;;;1255:17;1437:33;;1255:17;1437:33;;;;;;1255:17;1437:33;;;:::i;:::-;;;1255:17;;;;;1437:33;;;;;;-1:-1:-1;1437:33:20;;1059:9962:65;;;;;;;;;;;;;;;;;;:::i;:::-;;:::o;2179:149:20:-;;2262:60;;2179:149;;:::o;2262:60::-;-1:-1:-1;;;;;;;;;;;2286:25:20;;;;1059:9962:65;;-1:-1:-1;;;2286:25:20;;1059:9962:65;2286:25:20;;1059:9962:65;;;;2286:25:20;;1059:9962:65;2286:25:20;;;;:::i;:::-;;;-1:-1:-1;;;;;;;;;;;2286:25:20;;;;;;;;2179:149;:::o;2286:25::-;;;;;:::i;1894:148::-;1980:5;;1976:60;;1894:148;;:::o;1976:60::-;-1:-1:-1;;;;;;;;;;;2001:24:20;;;;1059:9962:65;;-1:-1:-1;;;2001:24:20;;1059:9962:65;2001:24:20;;1059:9962:65;;;;2001:24:20;;1059:9962:65;2001:24:20;;;;:::i;2823:177::-;2123:3:65;2925:13:20;;2921:73;;2823:177;;:::o;2921:73::-;-1:-1:-1;;;;;;;;;;;2954:29:20;;;;1059:9962:65;;-1:-1:-1;1059:9962:65;;;;;;;;;;;2954:29:20;;;;;1059:9962:65;2123:3;1059:9962;;;;;;;;;;;;;;:::i", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "setUp()": "0a9254e4", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "test_AtomicManipulationAttack()": "024512be", "test_EconomicImpactOfManipulation()": "d3b5950c", "test_NoProtectionMechanisms()": "5c74443b", "test_PriceManipulationVulnerability()": "b8bf5343"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"setUp\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_AtomicManipulationAttack\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_EconomicImpactOfManipulation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_NoProtectionMechanisms\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_PriceManipulationVulnerability\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"POC demonstrating critical price manipulation vulnerability FINDING: KuruForwarder.executePriceDependent() is vulnerable to price oracle manipulation attacks due to direct reliance on spot prices without protection mechanisms.\",\"kind\":\"dev\",\"methods\":{\"test_AtomicManipulationAttack()\":{\"details\":\"Test Case 2: Demonstrate atomic attack (same-block manipulation) This test proves that manipulation and execution can happen atomically, making the attack nearly undetectable.\"},\"test_EconomicImpactOfManipulation()\":{\"details\":\"Test Case 4: Demonstrate economic impact of manipulation This test shows how price manipulation leads to financial losses.\"},\"test_NoProtectionMechanisms()\":{\"details\":\"Test Case 3: Demonstrate lack of protection mechanisms This test proves that the system has no protections against price manipulation.\"},\"test_PriceManipulationVulnerability()\":{\"details\":\"Test Case 1: Demonstrate price manipulation vulnerability This test proves that an attacker can manipulate the price returned by bestBidAsk() and that this manipulation would affect price-dependent execution logic. SCENARIO: Victim wants to execute when trigger price (900) < current price This means they want to execute when the market price is HIGH (above 900)\"}},\"title\":\"Price Oracle Manipulation in KuruForwarder.executePriceDependent()\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/PriceManipulationAnalysisPOC.t.sol\":\"PriceManipulationAnalysisPOC\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/AbstractAMM.sol\":{\"keccak256\":\"0x174a22f9d3fe82b315d86ca86e1358f57e9b199618439284b0d2386f6a3f536e\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://986eeb1def68278dcbdcef380243c6835219a1bfab7cac312957967ae3baf804\",\"dweb:/ipfs/QmT2WWD1jrDH4nkXKmiXWwvMyJMZuSyS5wCGzn9FZ6u4Wg\"]},\"contracts/KuruAMMVault.sol\":{\"keccak256\":\"0x110d6ea2c23c31eafdcbc244727243902a6bd406bf4a509851614842a5157eb4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae1964d8b700b192b2483b9b841340f145fc84b3a13fb3001198c23f00e390e4\",\"dweb:/ipfs/QmZBNTQmH4ucyQZSSfyUJ7eMNFYiHuaNGUAzkGaQdxraF4\"]},\"contracts/KuruForwarder.sol\":{\"keccak256\":\"0xacc170f5ce66221ae208235ae21d4014b537002f3085c729fd67793a97ce8730\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ce914cf74f9d9d3fd73b8392318063d4b57c90e7b615990030d1afd1f2402368\",\"dweb:/ipfs/QmZvTGqYLwiBSQKDTKVEVAQN3mEBMuLefuUoAWrW6uPAP4\"]},\"contracts/MarginAccount.sol\":{\"keccak256\":\"0xb84f7d6424bcaa18060b459e607e79042dd445ab2dcbd7d9627ca5ca67ce5ace\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://063e892e7ef061958cd2e2a91be36288e63a2c9f75c36cb3759d7cf7dfb979c9\",\"dweb:/ipfs/QmPFvYPyTuq4hz6R51Uf5VJtcQJzHGCamaGjybJRpfnpec\"]},\"contracts/OrderBook.sol\":{\"keccak256\":\"0x054667b2015d71a16fe0c57816afa53d8a2c8c4a3fdfc603ad3d4c9a08820b08\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://80bc3ca50aa8a391d0a94d72671c8bd88d6a53852cedff355fe3f2eb4324b24f\",\"dweb:/ipfs/QmbQtFxyEQN2C4yBWzbm6JPmtKoENuM7LB51BVpuhsW1Ao\"]},\"contracts/Router.sol\":{\"keccak256\":\"0xc1aef5024f67486952008a5bc7617c652b15e2f0f8a43371b94a83529e603402\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://3bdba47306acc4d8ae80f1bec973760e7949a707931126815bddb604d4ed8878\",\"dweb:/ipfs/QmU1Eybo2JteXwvjmKfcgyQYa5waFfTd2jUpsdVArsk1Kk\"]},\"contracts/interfaces/IKuruAMMVault.sol\":{\"keccak256\":\"0xf0460e901fd738f2046c075784aa3045a58342c2f8ed276df83cf5386be969c7\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://54c3a5647cce41595226f4bbdc403bf6ef91177f3caa57de908e9ecaa2ac6460\",\"dweb:/ipfs/QmcWxssB8rFcX1QCZBqHzmmE3Dq2AHUFPfJvogzegQnMoS\"]},\"contracts/interfaces/IMarginAccount.sol\":{\"keccak256\":\"0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4\",\"dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo\"]},\"contracts/interfaces/IOrderBook.sol\":{\"keccak256\":\"0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d\",\"dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68\"]},\"contracts/interfaces/IRouter.sol\":{\"keccak256\":\"0x8b5aed176358b66cd5e9a7286eb927b0aa60cc895b83f1ec3d90bea9788d8702\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://c91e0f439908e9e7ad7c5741cab792806721613fa5c6ac74d0b307bb67489340\",\"dweb:/ipfs/QmYdpg9TuTAAAtT3KzUU6yWNnRXCpGxSs51MsBWjtJnXLu\"]},\"contracts/libraries/BitMath.sol\":{\"keccak256\":\"0xc6be48e23a72c9e2ed161a622b4839c68fcc1c3a3ca9c24d0c6e5cac9537541f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b8241e020c9f6f2a21f8c0451f6ecb396055a4bf86db122ae9236c555534130f\",\"dweb:/ipfs/QmQz4ghu9QLk2VixhGDjJCiyhn7a7ZC5UctDk1bQ2WLdXw\"]},\"contracts/libraries/ERC2771Context.sol\":{\"keccak256\":\"0x7458e7a07eb42f479dd6a547733373f21d7a45e0d9a78d545e16db6639e61ef5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8d278e5acd57e1943f2e411bcd6f4c13fb8620ae046cf8c8026fd9a103315497\",\"dweb:/ipfs/QmYWsYRhEV9A7sXgkFX6ES278EMN7b2rJ1JyetQ2XBQrWP\"]},\"contracts/libraries/Errors.sol\":{\"keccak256\":\"0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7\",\"dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu\"]},\"contracts/libraries/FixedPointMathLib.sol\":{\"keccak256\":\"0x90a5a4d76dd7cdbf8ee357bd0cb963ad5fb2f3d58444f213872f0e7e23ffbebd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://99f2d78bf91870c6815b4eec8aef48fec7e94de2dba2255f8fb8fe3cb9280946\",\"dweb:/ipfs/QmRjtG3C4hrXAc9hsyRpvzEZXhX3ivMktKB9hYgTaLAi4N\"]},\"contracts/libraries/OrderLinkedList.sol\":{\"keccak256\":\"0x48132979bf939d9d2b42d112a507300cdf1cd440d06ba98ea86e847e60fd2f32\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://0c20ad217ecf6e9ef7892d6e576f26d1fcd21f1804e4e8c1db66a2b7defdb133\",\"dweb:/ipfs/QmXqJ9XNkKJSs8c1pDVoghDWuacooxSCW7UAC4xL52FTjU\"]},\"contracts/libraries/TreeMath.sol\":{\"keccak256\":\"0x61fb4aea5f902aaee0bc1604e5500acee6d426e03bc638afee1d310a24eaba10\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eaaa42df47bc993cd8bc754198423ff87bcaaa2994bdcf9206bfae67fa81aca0\",\"dweb:/ipfs/Qmaqmu7ShkXGFauY6xfMTpnzMUHXbxuJFrCkPCsdMdT9dS\"]},\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3\",\"dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc\",\"dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a\",\"dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT\"]},\"lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4\",\"dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xe2b159412b06b44a7f90972104300c587f308506d37d2143dd7e689e2eac6f01\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://a96e13ac6fec3ffe61a55d6005ef0ef96f47adf3f4f3531e0418f0bf02d3f93c\",\"dweb:/ipfs/QmeFci69gm7a6c6pEqSNoe3HfXPVPiagUc51Pyo5PoS8Rn\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol\":{\"keccak256\":\"0xbf2aefe54b76d7f7bcd4f6da1080b7b1662611937d870b880db584d09cea56b5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5e7e2f12e0feec75296e57f51f82fdaa8bd1551f4b8cc6560442c0bf60f818c\",\"dweb:/ipfs/QmcW9wDMaQ8RbQibMarfp17a3bABzY5KraWe2YDwuUrUoz\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049\",\"dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0xa1ad192cd45317c788618bef5cb1fb3ca4ce8b230f6433ac68cc1d850fb81618\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b43447bb85a53679d269a403c693b9d88d6c74177dfb35eddca63abaf7cf110a\",\"dweb:/ipfs/QmXSDmpd4bNZj1PDgegr6C4w1jDaWHXCconC3rYiw9TSkQ\"]},\"lib/openzeppelin-contracts/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0x20462ddb2665e9521372c76b001d0ce196e59dbbd989de9af5576cad0bd5628b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f417fd12aeec8fbfaceaa30e3a08a0724c0bc39de363e2acf6773c897abbaf6d\",\"dweb:/ipfs/QmU4Hko6sApdweVM92CsiuLKkCk8HfyBeutF89PCTz5Tye\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0x6d0ae6e206645341fd122d278c2cb643dea260c190531f2f3f6a0426e77b00c0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://032d1201d839435be2c85b72e33206b3ea980c569d6ebf7fa57d811ab580a82f\",\"dweb:/ipfs/QmeqQjAtMvdZT2tG7zm39itcRJkuwu8AEReK6WRnLJ18DD\"]},\"lib/openzeppelin-contracts/contracts/utils/Create2.sol\":{\"keccak256\":\"0xbb7e8401583d26268ea9103013bcdcd90866a7718bd91105ebd21c9bf11f4f06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://866a11ad89c93ee918078f7a46ae31e17d89216ce64603f0d34be7ed0a5c520e\",\"dweb:/ipfs/QmW3ckLEJg2v2NzuVLNJFmRuerGSipw6Dzg6ntbmqbAGoC\"]},\"lib/openzeppelin-contracts/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol\":{\"keccak256\":\"0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e\",\"dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol\":{\"keccak256\":\"0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de\",\"dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0xa56ca923f70c1748830700250b19c61b70db9a683516dc5e216694a50445d99c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cac938788bc4be12101e59d45588b4e059579f4e61062e1cda8d6b06c0191b15\",\"dweb:/ipfs/QmV2JKCyjTVH3rkWNrfdJRhAT7tZ3usAN2XcnD4h53Mvih\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x287b55befed2961a7eabd7d7b1b2839cbca8a5b80ef8dcbb25ed3d4c2002c305\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bd39944e8fc06be6dbe2dd1d8449b5336e23c6a7ba3e8e9ae5ae0f37f35283f5\",\"dweb:/ipfs/QmPV3FGYjVwvKSgAXKUN3r9T9GwniZz83CxBpM7vyj2G53\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0x8de418a5503946cabe331f35fe242d3201a73f67f77aaeb7110acb1f30423aca\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a376d3dda2cb70536c0a45c208b29b34ac560c4cb4f513a42079f96ba47d2dd\",\"dweb:/ipfs/QmZQg6gn1sUpM8wHzwNvSnihumUCAhxD119MpXeKp8B9s8\"]},\"node_modules/@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0xa92e4fa126feb6907daa0513ddd816b2eb91f30a808de54f63c17d0e162c3439\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a367861093b74443b137564d3f3c472f70bcf114739e62059c939f25e315706c\",\"dweb:/ipfs/Qmd7JMpcxD9RuQjK3uM3EzJUgSqdN8vzp8eytEiuwxQJ6h\"]},\"node_modules/solady/src/auth/Ownable.sol\":{\"keccak256\":\"0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3\",\"dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs\"]},\"node_modules/solady/src/tokens/ERC20.sol\":{\"keccak256\":\"0xb4a3f9ba8a05107f7370de42cff57f3ad26dafd438712c11531a5892de2f59e0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f0a9ca06e3cf6dea1f9a4c5599581573b7d81cd64dc3afb582f325ccf5fdd6dc\",\"dweb:/ipfs/Qmb9r5dDceNF4W8S5u6i85RsNTgE5XG9HbTXkyS25ad3C6\"]},\"node_modules/solady/src/utils/ECDSA.sol\":{\"keccak256\":\"0x077d168511141c83fd93914f4609c5363341da03a3971cbc0d3f6a75e9893de0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c218864adb4e97d81908318d9bd0ed75f40ec996c826141fab2646135820295b\",\"dweb:/ipfs/QmVQyWG6Ff4LAUnzj6JMEHHYfNBANVYLaMkun6TYaWShms\"]},\"node_modules/solady/src/utils/EIP712.sol\":{\"keccak256\":\"0xb5c4c8ac5368c9785b4e30314f4ad6f3ae13bdc21679007735681d13da797bec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c4456a4eaa8748f802fd1188db6405d18c452eb7c0dde84a49b49a7f94b5970d\",\"dweb:/ipfs/QmZzsFn4VwvBFy2MJVJXvntCQsDRCXbRrSKKfXxXv9jYGM\"]},\"node_modules/solady/src/utils/Initializable.sol\":{\"keccak256\":\"0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559\",\"dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy\"]},\"node_modules/solady/src/utils/SafeTransferLib.sol\":{\"keccak256\":\"0x583f47701d9b47bb3ef80fcabbbd62fbb58a01733b7a57e19658b4b02468883a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2523bfac005e21ef9963fdb3c08b2c61824e2b5ce2f53d1a1828b01ed995217c\",\"dweb:/ipfs/QmbBjVG9tZyeZSQH4m5GUzNBwo2iuvLoZYbmhT4gxnJc4J\"]},\"node_modules/solady/src/utils/UUPSUpgradeable.sol\":{\"keccak256\":\"0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16\",\"dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV\"]},\"test/PriceManipulationAnalysisPOC.t.sol\":{\"keccak256\":\"0xa0af688027decd5059fbb7c9985f9773db980558086546d038d61fda2f3a531c\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://166c364f6a2cfbd9d7df6f389957fa5709f573405384cd66cda37c3f057cad79\",\"dweb:/ipfs/QmdywnK6RsHHgJsNYuu3veoGC5nrsB2T9S2iPUXy1mdiQZ\"]},\"test/lib/MintableERC20.sol\":{\"keccak256\":\"0x5c9d1c2c5f29fe7a13784f5467a6cbe0029bf1661fd5a5031ac9b531dbf4515a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b7627d0a11419a0db10ade04def9e12499f2bf692b611c14caecc5ac24070ab5\",\"dweb:/ipfs/QmQZ969Gypd8e9xiqUMLGdMYPWQopSjmmPTDSTMoAT1rdV\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "setUp"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_AtomicManipulationAttack"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_EconomicImpactOfManipulation"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_NoProtectionMechanisms"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_PriceManipulationVulnerability"}], "devdoc": {"kind": "dev", "methods": {"test_AtomicManipulationAttack()": {"details": "Test Case 2: Demonstrate atomic attack (same-block manipulation) This test proves that manipulation and execution can happen atomically, making the attack nearly undetectable."}, "test_EconomicImpactOfManipulation()": {"details": "Test Case 4: Demonstrate economic impact of manipulation This test shows how price manipulation leads to financial losses."}, "test_NoProtectionMechanisms()": {"details": "Test Case 3: Demonstrate lack of protection mechanisms This test proves that the system has no protections against price manipulation."}, "test_PriceManipulationVulnerability()": {"details": "Test Case 1: Demonstrate price manipulation vulnerability This test proves that an attacker can manipulate the price returned by bestBidAsk() and that this manipulation would affect price-dependent execution logic. SCENARIO: Victim wants to execute when trigger price (900) < current price This means they want to execute when the market price is HIGH (above 900)"}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/PriceManipulationAnalysisPOC.t.sol": "PriceManipulationAnalysisPOC"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/AbstractAMM.sol": {"keccak256": "0x174a22f9d3fe82b315d86ca86e1358f57e9b199618439284b0d2386f6a3f536e", "urls": ["bzz-raw://986eeb1def68278dcbdcef380243c6835219a1bfab7cac312957967ae3baf804", "dweb:/ipfs/QmT2WWD1jrDH4nkXKmiXWwvMyJMZuSyS5wCGzn9FZ6u4Wg"], "license": "BUSL-1.1"}, "contracts/KuruAMMVault.sol": {"keccak256": "0x110d6ea2c23c31eafdcbc244727243902a6bd406bf4a509851614842a5157eb4", "urls": ["bzz-raw://ae1964d8b700b192b2483b9b841340f145fc84b3a13fb3001198c23f00e390e4", "dweb:/ipfs/QmZBNTQmH4ucyQZSSfyUJ7eMNFYiHuaNGUAzkGaQdxraF4"], "license": "BUSL-1.1"}, "contracts/KuruForwarder.sol": {"keccak256": "0xacc170f5ce66221ae208235ae21d4014b537002f3085c729fd67793a97ce8730", "urls": ["bzz-raw://ce914cf74f9d9d3fd73b8392318063d4b57c90e7b615990030d1afd1f2402368", "dweb:/ipfs/QmZvTGqYLwiBSQKDTKVEVAQN3mEBMuLefuUoAWrW6uPAP4"], "license": "BUSL-1.1"}, "contracts/MarginAccount.sol": {"keccak256": "0xb84f7d6424bcaa18060b459e607e79042dd445ab2dcbd7d9627ca5ca67ce5ace", "urls": ["bzz-raw://063e892e7ef061958cd2e2a91be36288e63a2c9f75c36cb3759d7cf7dfb979c9", "dweb:/ipfs/QmPFvYPyTuq4hz6R51Uf5VJtcQJzHGCamaGjybJRpfnpec"], "license": "BUSL-1.1"}, "contracts/OrderBook.sol": {"keccak256": "0x054667b2015d71a16fe0c57816afa53d8a2c8c4a3fdfc603ad3d4c9a08820b08", "urls": ["bzz-raw://80bc3ca50aa8a391d0a94d72671c8bd88d6a53852cedff355fe3f2eb4324b24f", "dweb:/ipfs/QmbQtFxyEQN2C4yBWzbm6JPmtKoENuM7LB51BVpuhsW1Ao"], "license": "BUSL-1.1"}, "contracts/Router.sol": {"keccak256": "0xc1aef5024f67486952008a5bc7617c652b15e2f0f8a43371b94a83529e603402", "urls": ["bzz-raw://3bdba47306acc4d8ae80f1bec973760e7949a707931126815bddb604d4ed8878", "dweb:/ipfs/QmU1Eybo2JteXwvjmKfcgyQYa5waFfTd2jUpsdVArsk1Kk"], "license": "BUSL-1.1"}, "contracts/interfaces/IKuruAMMVault.sol": {"keccak256": "0xf0460e901fd738f2046c075784aa3045a58342c2f8ed276df83cf5386be969c7", "urls": ["bzz-raw://54c3a5647cce41595226f4bbdc403bf6ef91177f3caa57de908e9ecaa2ac6460", "dweb:/ipfs/QmcWxssB8rFcX1QCZBqHzmmE3Dq2AHUFPfJvogzegQnMoS"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IMarginAccount.sol": {"keccak256": "0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2", "urls": ["bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4", "dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IOrderBook.sol": {"keccak256": "0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b", "urls": ["bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d", "dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IRouter.sol": {"keccak256": "0x8b5aed176358b66cd5e9a7286eb927b0aa60cc895b83f1ec3d90bea9788d8702", "urls": ["bzz-raw://c91e0f439908e9e7ad7c5741cab792806721613fa5c6ac74d0b307bb67489340", "dweb:/ipfs/QmYdpg9TuTAAAtT3KzUU6yWNnRXCpGxSs51MsBWjtJnXLu"], "license": "GPL-2.0-or-later"}, "contracts/libraries/BitMath.sol": {"keccak256": "0xc6be48e23a72c9e2ed161a622b4839c68fcc1c3a3ca9c24d0c6e5cac9537541f", "urls": ["bzz-raw://b8241e020c9f6f2a21f8c0451f6ecb396055a4bf86db122ae9236c555534130f", "dweb:/ipfs/QmQz4ghu9QLk2VixhGDjJCiyhn7a7ZC5UctDk1bQ2WLdXw"], "license": "MIT"}, "contracts/libraries/ERC2771Context.sol": {"keccak256": "0x7458e7a07eb42f479dd6a547733373f21d7a45e0d9a78d545e16db6639e61ef5", "urls": ["bzz-raw://8d278e5acd57e1943f2e411bcd6f4c13fb8620ae046cf8c8026fd9a103315497", "dweb:/ipfs/QmYWsYRhEV9A7sXgkFX6ES278EMN7b2rJ1JyetQ2XBQrWP"], "license": "MIT"}, "contracts/libraries/Errors.sol": {"keccak256": "0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f", "urls": ["bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7", "dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu"], "license": "BUSL-1.1"}, "contracts/libraries/FixedPointMathLib.sol": {"keccak256": "0x90a5a4d76dd7cdbf8ee357bd0cb963ad5fb2f3d58444f213872f0e7e23ffbebd", "urls": ["bzz-raw://99f2d78bf91870c6815b4eec8aef48fec7e94de2dba2255f8fb8fe3cb9280946", "dweb:/ipfs/QmRjtG3C4hrXAc9hsyRpvzEZXhX3ivMktKB9hYgTaLAi4N"], "license": "MIT"}, "contracts/libraries/OrderLinkedList.sol": {"keccak256": "0x48132979bf939d9d2b42d112a507300cdf1cd440d06ba98ea86e847e60fd2f32", "urls": ["bzz-raw://0c20ad217ecf6e9ef7892d6e576f26d1fcd21f1804e4e8c1db66a2b7defdb133", "dweb:/ipfs/QmXqJ9XNkKJSs8c1pDVoghDWuacooxSCW7UAC4xL52FTjU"], "license": "BUSL-1.1"}, "contracts/libraries/TreeMath.sol": {"keccak256": "0x61fb4aea5f902aaee0bc1604e5500acee6d426e03bc638afee1d310a24eaba10", "urls": ["bzz-raw://eaaa42df47bc993cd8bc754198423ff87bcaaa2994bdcf9206bfae67fa81aca0", "dweb:/ipfs/Qmaqmu7ShkXGFauY6xfMTpnzMUHXbxuJFrCkPCsdMdT9dS"], "license": "MIT"}, "lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/StdAssertions.sol": {"keccak256": "0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43", "urls": ["bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3", "dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd", "urls": ["bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc", "dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8", "urls": ["bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a", "dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT"], "license": "MIT"}, "lib/forge-std/src/Test.sol": {"keccak256": "0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c", "urls": ["bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4", "dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xe2b159412b06b44a7f90972104300c587f308506d37d2143dd7e689e2eac6f01", "urls": ["bzz-raw://a96e13ac6fec3ffe61a55d6005ef0ef96f47adf3f4f3531e0418f0bf02d3f93c", "dweb:/ipfs/QmeFci69gm7a6c6pEqSNoe3HfXPVPiagUc51Pyo5PoS8Rn"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol": {"keccak256": "0xbf2aefe54b76d7f7bcd4f6da1080b7b1662611937d870b880db584d09cea56b5", "urls": ["bzz-raw://f5e7e2f12e0feec75296e57f51f82fdaa8bd1551f4b8cc6560442c0bf60f818c", "dweb:/ipfs/QmcW9wDMaQ8RbQibMarfp17a3bABzY5KraWe2YDwuUrUoz"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e", "urls": ["bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049", "dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0xa1ad192cd45317c788618bef5cb1fb3ca4ce8b230f6433ac68cc1d850fb81618", "urls": ["bzz-raw://b43447bb85a53679d269a403c693b9d88d6c74177dfb35eddca63abaf7cf110a", "dweb:/ipfs/QmXSDmpd4bNZj1PDgegr6C4w1jDaWHXCconC3rYiw9TSkQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0x20462ddb2665e9521372c76b001d0ce196e59dbbd989de9af5576cad0bd5628b", "urls": ["bzz-raw://f417fd12aeec8fbfaceaa30e3a08a0724c0bc39de363e2acf6773c897abbaf6d", "dweb:/ipfs/QmU4Hko6sApdweVM92CsiuLKkCk8HfyBeutF89PCTz5Tye"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0x6d0ae6e206645341fd122d278c2cb643dea260c190531f2f3f6a0426e77b00c0", "urls": ["bzz-raw://032d1201d839435be2c85b72e33206b3ea980c569d6ebf7fa57d811ab580a82f", "dweb:/ipfs/QmeqQjAtMvdZT2tG7zm39itcRJkuwu8AEReK6WRnLJ18DD"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Create2.sol": {"keccak256": "0xbb7e8401583d26268ea9103013bcdcd90866a7718bd91105ebd21c9bf11f4f06", "urls": ["bzz-raw://866a11ad89c93ee918078f7a46ae31e17d89216ce64603f0d34be7ed0a5c520e", "dweb:/ipfs/QmW3ckLEJg2v2NzuVLNJFmRuerGSipw6Dzg6ntbmqbAGoC"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuardTransient.sol": {"keccak256": "0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3", "urls": ["bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e", "dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/TransientSlot.sol": {"keccak256": "0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946", "urls": ["bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de", "dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"keccak256": "0xa56ca923f70c1748830700250b19c61b70db9a683516dc5e216694a50445d99c", "urls": ["bzz-raw://cac938788bc4be12101e59d45588b4e059579f4e61062e1cda8d6b06c0191b15", "dweb:/ipfs/QmV2JKCyjTVH3rkWNrfdJRhAT7tZ3usAN2XcnD4h53Mvih"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x287b55befed2961a7eabd7d7b1b2839cbca8a5b80ef8dcbb25ed3d4c2002c305", "urls": ["bzz-raw://bd39944e8fc06be6dbe2dd1d8449b5336e23c6a7ba3e8e9ae5ae0f37f35283f5", "dweb:/ipfs/QmPV3FGYjVwvKSgAXKUN3r9T9GwniZz83CxBpM7vyj2G53"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0x8de418a5503946cabe331f35fe242d3201a73f67f77aaeb7110acb1f30423aca", "urls": ["bzz-raw://5a376d3dda2cb70536c0a45c208b29b34ac560c4cb4f513a42079f96ba47d2dd", "dweb:/ipfs/QmZQg6gn1sUpM8wHzwNvSnihumUCAhxD119MpXeKp8B9s8"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Context.sol": {"keccak256": "0xa92e4fa126feb6907daa0513ddd816b2eb91f30a808de54f63c17d0e162c3439", "urls": ["bzz-raw://a367861093b74443b137564d3f3c472f70bcf114739e62059c939f25e315706c", "dweb:/ipfs/Qmd7JMpcxD9RuQjK3uM3EzJUgSqdN8vzp8eytEiuwxQJ6h"], "license": "MIT"}, "node_modules/solady/src/auth/Ownable.sol": {"keccak256": "0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3", "urls": ["bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3", "dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs"], "license": "MIT"}, "node_modules/solady/src/tokens/ERC20.sol": {"keccak256": "0xb4a3f9ba8a05107f7370de42cff57f3ad26dafd438712c11531a5892de2f59e0", "urls": ["bzz-raw://f0a9ca06e3cf6dea1f9a4c5599581573b7d81cd64dc3afb582f325ccf5fdd6dc", "dweb:/ipfs/Qmb9r5dDceNF4W8S5u6i85RsNTgE5XG9HbTXkyS25ad3C6"], "license": "MIT"}, "node_modules/solady/src/utils/ECDSA.sol": {"keccak256": "0x077d168511141c83fd93914f4609c5363341da03a3971cbc0d3f6a75e9893de0", "urls": ["bzz-raw://c218864adb4e97d81908318d9bd0ed75f40ec996c826141fab2646135820295b", "dweb:/ipfs/QmVQyWG6Ff4LAUnzj6JMEHHYfNBANVYLaMkun6TYaWShms"], "license": "MIT"}, "node_modules/solady/src/utils/EIP712.sol": {"keccak256": "0xb5c4c8ac5368c9785b4e30314f4ad6f3ae13bdc21679007735681d13da797bec", "urls": ["bzz-raw://c4456a4eaa8748f802fd1188db6405d18c452eb7c0dde84a49b49a7f94b5970d", "dweb:/ipfs/QmZzsFn4VwvBFy2MJVJXvntCQsDRCXbRrSKKfXxXv9jYGM"], "license": "MIT"}, "node_modules/solady/src/utils/Initializable.sol": {"keccak256": "0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec", "urls": ["bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559", "dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy"], "license": "MIT"}, "node_modules/solady/src/utils/SafeTransferLib.sol": {"keccak256": "0x583f47701d9b47bb3ef80fcabbbd62fbb58a01733b7a57e19658b4b02468883a", "urls": ["bzz-raw://2523bfac005e21ef9963fdb3c08b2c61824e2b5ce2f53d1a1828b01ed995217c", "dweb:/ipfs/QmbBjVG9tZyeZSQH4m5GUzNBwo2iuvLoZYbmhT4gxnJc4J"], "license": "MIT"}, "node_modules/solady/src/utils/UUPSUpgradeable.sol": {"keccak256": "0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e", "urls": ["bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16", "dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV"], "license": "MIT"}, "test/PriceManipulationAnalysisPOC.t.sol": {"keccak256": "0xa0af688027decd5059fbb7c9985f9773db980558086546d038d61fda2f3a531c", "urls": ["bzz-raw://166c364f6a2cfbd9d7df6f389957fa5709f573405384cd66cda37c3f057cad79", "dweb:/ipfs/QmdywnK6RsHHgJsNYuu3veoGC5nrsB2T9S2iPUXy1mdiQZ"], "license": "BUSL-1.1"}, "test/lib/MintableERC20.sol": {"keccak256": "0x5c9d1c2c5f29fe7a13784f5467a6cbe0029bf1661fd5a5031ac9b531dbf4515a", "urls": ["bzz-raw://b7627d0a11419a0db10ade04def9e12499f2bf692b611c14caecc5ac24070ab5", "dweb:/ipfs/QmQZ969Gypd8e9xiqUMLGdMYPWQopSjmmPTDSTMoAT1rdV"], "license": "MIT"}}, "version": 1}, "id": 65}