{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowedInterface", "inputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "cancelOwnershipHandover", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "cancelPriceDependent", "inputs": [{"name": "req", "type": "tuple", "internalType": "struct KuruFor<PERSON>er.CancelPriceDependentRequest", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}]}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "completeOwnershipHandover", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "eip712Domain", "inputs": [], "outputs": [{"name": "fields", "type": "bytes1", "internalType": "bytes1"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "version", "type": "string", "internalType": "string"}, {"name": "chainId", "type": "uint256", "internalType": "uint256"}, {"name": "verifyingContract", "type": "address", "internalType": "address"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "extensions", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "execute", "inputs": [{"name": "req", "type": "tuple", "internalType": "struct KuruForwarder.ForwardRequest", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "market", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}, {"name": "data", "type": "bytes", "internalType": "bytes"}]}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "payable"}, {"type": "function", "name": "executeMarginAccountRequest", "inputs": [{"name": "req", "type": "tuple", "internalType": "struct KuruFor<PERSON><PERSON>.MarginAccountRequest", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "marginAccount", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}, {"name": "data", "type": "bytes", "internalType": "bytes"}]}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "payable"}, {"type": "function", "name": "executePriceDependent", "inputs": [{"name": "req", "type": "tuple", "internalType": "struct Kuru<PERSON>or<PERSON><PERSON>.PriceDependentRequest", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "market", "type": "address", "internalType": "address"}, {"name": "price", "type": "uint256", "internalType": "uint256"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "isBelowPrice", "type": "bool", "internalType": "bool"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}, {"name": "data", "type": "bytes", "internalType": "bytes"}]}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "payable"}, {"type": "function", "name": "executedPriceDependentRequest", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "getNonce", "inputs": [{"name": "from", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_owner", "type": "address", "internalType": "address"}, {"name": "_allowedInterfaces", "type": "bytes4[]", "internalType": "bytes4[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "result", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "ownershipHandoverExpiresAt", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "internalType": "address"}], "outputs": [{"name": "result", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "removeAllowedInterfaces", "inputs": [{"name": "_allowedInterfaces", "type": "bytes4[]", "internalType": "bytes4[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "requestOwnershipHandover", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "setAllowedInterfaces", "inputs": [{"name": "_allowedInterfaces", "type": "bytes4[]", "internalType": "bytes4[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "verify", "inputs": [{"name": "req", "type": "tuple", "internalType": "struct KuruForwarder.ForwardRequest", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "market", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}, {"name": "data", "type": "bytes", "internalType": "bytes"}]}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "verifyCancelPriceDependent", "inputs": [{"name": "req", "type": "tuple", "internalType": "struct KuruFor<PERSON>er.CancelPriceDependentRequest", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}]}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "verifyMarginAccountRequest", "inputs": [{"name": "req", "type": "tuple", "internalType": "struct KuruFor<PERSON><PERSON>.MarginAccountRequest", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "marginAccount", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}, {"name": "data", "type": "bytes", "internalType": "bytes"}]}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "verifyPriceDependent", "inputs": [{"name": "req", "type": "tuple", "internalType": "struct Kuru<PERSON>or<PERSON><PERSON>.PriceDependentRequest", "components": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "market", "type": "address", "internalType": "address"}, {"name": "price", "type": "uint256", "internalType": "uint256"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "isBelowPrice", "type": "bool", "internalType": "bool"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}, {"name": "data", "type": "bytes", "internalType": "bytes"}]}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "OwnershipHandoverCanceled", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipHandoverRequested", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "old<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AlreadyInitialized", "inputs": []}, {"type": "error", "name": "DeadlineExpired", "inputs": []}, {"type": "error", "name": "ExecutionFailed", "inputs": [{"name": "", "type": "bytes", "internalType": "bytes"}]}, {"type": "error", "name": "InsufficientValue", "inputs": []}, {"type": "error", "name": "InterfaceNotAllowed", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NewOwnerIsZeroAddress", "inputs": []}, {"type": "error", "name": "NoHandoverRequest", "inputs": []}, {"type": "error", "name": "NonceAlreadyUsed", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "PriceDependentRequestFailed", "inputs": [{"name": "_currentPrice", "type": "uint256", "internalType": "uint256"}, {"name": "_breakpointPrice", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "SignatureMismatch", "inputs": []}, {"type": "error", "name": "Unauthorized", "inputs": []}, {"type": "error", "name": "UnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UpgradeFailed", "inputs": []}], "bytecode": {"object": "0x610140604052346100d657306080524660a0525f60c0525f60e0525f61010052306101205263409feecd1954600181166100c95760011c6002600160401b031901610084575b60405161150a90816100db823960805181505060a05181505060c05181505060e051815050610100518150506101205181818161087d01526109100152f35b6002600160411b0363409feecd19556001600160401b0360209081527fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d29080a1610045565b63f92ee8a95f526004601cfd5b5f80fdfe60806040526004361015610011575f80fd5b5f3560e01c806310012a1514610b8e578063********14610b5f5780631a4e971f14610ae65780631fbe2e4f14610aa7578063********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", "sourceMap": "574:9614:2:-:0;;;;;;2157:4:61;2119:45;;2191:13;2174:30;;-1:-1:-1;2580:26:61;;-1:-1:-1;2616:32:61;;-1:-1:-1;3184:34:61;;2157:4;1501:31:64;;2001:66:62;;6669:609;3868:4:2;6669:609:62;;;;2001:66;6669:609;-1:-1:-1;;;;;;6669:609:62;;;-1:-1:-1;574:9614:2;;;;;;;;2119:45:61;574:9614:2;;;;2174:30:61;574:9614:2;;;;2580:26:61;574:9614:2;;;;2616:32:61;574:9614:2;;;;3184:34:61;574:9614:2;;;;1501:31:64;574:9614:2;;;;;;;;;;;6669:609:62;-1:-1:-1;;;;;;;6669:609:62;-1:-1:-1;;;;;6669:609:62;;;;;;;;;;;;-1:-1:-1;6669:609:62;;;;574:9614:2;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x60806040526004361015610011575f80fd5b5f3560e01c806310012a1514610b8e578063********14610b5f5780631a4e971f14610ae65780631fbe2e4f14610aa7578063********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", "sourceMap": "574:9614:2:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;574:9614:2;;;;;;:::i;:::-;11885:237:58;;;574:9614:2;11885:237:58;574:9614:2;11885:237:58;;;;574:9614:2;;;;;;;;;;;;;-1:-1:-1;;574:9614:2;;;;;;:::i;:::-;12478:70:58;;:::i;:::-;8479:183;;;;;;8681:8;;;:::i;:::-;574:9614:2;8479:183:58;;574:9614:2;8479:183:58;574:9614:2;8479:183:58;;574:9614:2;;;-1:-1:-1;;574:9614:2;;;;;;:::i;:::-;12478:70:58;;:::i;:::-;10506:526;;;;574:9614:2;10506:526:58;574:9614:2;10506:526:58;;;;;;;;;574:9614:2;11051:12:58;10506:526;;11051:12;:::i;10506:526::-;;574:9614:2;10506:526:58;574:9614:2;10506:526:58;;574:9614:2;;;;;;;;;:::i;:::-;;;:::i;:::-;;;;;;;;;;8962:36;574:9614;;;:::i;:::-;8962:36;;;;;:::i;:::-;574:9614;;;9071:9;;;574:9614;9058:9;;;:22;574:9614;;;9157:12;;;-1:-1:-1;;;;;;9157:12:2;;;:::i;:::-;574:9614;;;;;;;;;;2243:85;574:9614;;;;9281:8;;;:::i;:::-;574:9614;;;;;;;-1:-1:-1;;574:9614:2;;9264:37;;574:9614;;;9291:9;;;574:9614;;;;;9264:37;;574:9614;;;;9264:37;;10133;;9264;;;;;;:::i;:::-;574:9614;9254:48;;574:9614;;;;;;;;;;;;;;;;;;9362:10;;574:9614;;;;;;;9362:10;;;:::i;:::-;574:9614;;;;;;;;;;9351:35;;;;;;;;;574:9614;9351:35;;;574:9614;9418:16;;;;;;;:::i;:::-;:48;;;;574:9614;9417:105;;;;574:9614;;;;;;9666:10;;;;9741:8;9700:50;9717:12;9666:10;;;:::i;:::-;9717:12;;:::i;:::-;9731:8;9700:50;9731:8;;;;;;:::i;:::-;9741;;;;:::i;:::-;574:9614;;9700:50;;;574:9614;9700:50;;;;;:::i;:::-;9666:85;;;;;;:::i;:::-;9766:8;;9762:73;;574:9614;;;;;;;;;;;;;;;:::i;:::-;;;;9762:73;574:9614;;-1:-1:-1;;;9797:27:2;;574:9614;;9797:27;;574:9614;;;;;;;;;;;:::i;:::-;9797:27;;;574:9614;;;;;;;;;;;9582:9;574:9614;;;;;;9417:105;9473:16;;;;:::i;:::-;9472:17;:49;;;9417:105;;;;9472:49;9493:9;;574:9614;9493:9;;574:9614;9493:28;9472:49;;9418:48;9438:9;;;574:9614;9438:9;;574:9614;9438:28;9418:48;;;9351:35;;;574:9614;9351:35;;574:9614;9351:35;;;;;;574:9614;9351:35;;;:::i;:::-;;;574:9614;;;;;9351:35;;;;;;-1:-1:-1;9351:35:2;;;574:9614;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;:::i;:::-;;;;;;-1:-1:-1;;574:9614:2;;;;-1:-1:-1;;11523:61:58;574:9614:2;;-1:-1:-1;;;;;574:9614:2;;;;;;;;;;;;;;-1:-1:-1;;574:9614:2;;;;;;7385:23:61;;:::i;:::-;574:9614:2;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;7428:13:61;574:9614:2;;;;7479:4:61;574:9614:2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;574:9614:2;;;;;;;;;;;;;;;;-1:-1:-1;;574:9614:2;;;;12478:70:58;;:::i;:::-;574:9614:2;5044:589:58;;6299:437;;;;;-1:-1:-1;;;;;6299:437:58;574:9614:2;;7518:42;574:9614;;;:::i;:::-;7518:42;;;;;:::i;:::-;574:9614;;;;7633:9;;574:9614;7620:9;;;:22;574:9614;;7719:12;;;-1:-1:-1;;;;;;7719:12:2;;;:::i;:::-;574:9614;;;;;;;;;;2243:85;574:9614;;;;7807:9;;;574:9614;;;;;;;;;;;;;;-1:-1:-1;;;;;7795:8:2;;;:::i;:::-;574:9614;;;7787:7;574:9614;;;;;;7967:8;7926:50;7943:12;7885:17;574:9614;7885:17;;;:::i;7943:12::-;7957:8;7926:50;7957:8;;;;;;:::i;574:9614::-;;;;;;;;;;;;;;;;;;-1:-1:-1;;574:9614:2;;;;;;:::i;:::-;;;-1:-1:-1;;;;;574:9614:2;;;;;;;;;;;:::i;:::-;2001:66:62;;3207:622;;;;;;;;;574:9614:2;-1:-1:-1;;;5044:589:58;;;-1:-1:-1;;;;;5044:589:58;;;;;;;-1:-1:-1;;5044:589:58;574:9614:2;5044:589:58;574:9614:2;;5044:589:58;574:9614:2;3153:3;574:9614;;;3122:29;;;;;-1:-1:-1;2804:4:2;;-1:-1:-1;;;;;;3189:21:2;574:9614;3189:21;;:::i;:::-;574:9614;;;;;;;;;;;;;;;;;;;;3107:13;;3122:29;;3892:296:62;;574:9614:2;3892:296:62;;;;2804:4:2;574:9614;3892:296:62;;574:9614:2;3892:296:62;;574:9614:2;5044:589:58;;574:9614:2;5044:589:58;574:9614:2;5044:589:58;;3207:622:62;;;;;;;;;;;;;;;;;;;;;;;;574:9614:2;3207:622:62;574:9614:2;3207:622:62;;574:9614:2;;;;;;;;;:::i;:::-;;;:::i;:::-;;;;;;-1:-1:-1;;574:9614:2;;;;;;-1:-1:-1;;;;;574:9614:2;;;;;;;;;;;:::i;:::-;12478:70:58;;:::i;:::-;574:9614:2;3438:3;574:9614;;3407:29;;;;;574:9614;;-1:-1:-1;;;;;;3474:21:2;574:9614;3474:21;;:::i;:::-;574:9614;;;;;;;;;;;;;;;;;;;;3392:13;;574:9614;;;-1:-1:-1;;574:9614:2;;;;9831:339:58;;;;574:9614:2;9831:339:58;574:9614:2;9831:339:58;;;;;;574:9614:2;9831:339:58;;574:9614:2;;;;;;;-1:-1:-1;;574:9614:2;;;;6466:184:64;6407:6;6466:184;;;574:9614:2;;;2619:66:64;574:9614:2;;;6466:184:64;;574:9614:2;6466:184:64;574:9614:2;6466:184:64;;574:9614:2;;;-1:-1:-1;;574:9614:2;;;;;;:::i;:::-;;;-1:-1:-1;;;;;574:9614:2;;;;;;;;;;;:::i;:::-;5771:446:64;;;5712:6;5771:446;;;2821:89:2;;:::i;:::-;574:9614;;;;;4259:1327:64;;;;;574:9614:2;4259:1327:64;574:9614:2;4259:1327:64;;;;;;;;;;574:9614:2;4259:1327:64;;;;;;;;574:9614:2;4259:1327:64;;574:9614:2;4259:1327:64;574:9614:2;4259:1327:64;;;;;;;;;;;;574:9614:2;4259:1327:64;;574:9614:2;4259:1327:64;;;;;;;;;574:9614:2;4259:1327:64;;574:9614:2;8229:22;574:9614;;;:::i;:::-;8229:22;;;;;:::i;574:9614::-;;;;;;;;;:::i;:::-;;;:::i;:::-;;;;;;-1:-1:-1;;574:9614:2;;;;-1:-1:-1;;;;;574:9614:2;;:::i;:::-;;;;4144:7;574:9614;;;;;;;;;;;;;;;;-1:-1:-1;;574:9614:2;;;;9239:383:58;;;;574:9614:2;9239:383:58;7972:9;9132:15;574:9614:2;9239:383:58;;;;;;574:9614:2;9239:383:58;;574:9614:2;;;;;;;-1:-1:-1;;574:9614:2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;9998:42;574:9614;;;:::i;:::-;9998:42;;;;;:::i;:::-;574:9614;;;10133:37;;10150:8;;;:::i;:::-;574:9614;;10133:37;;;10160:9;10133:37;;;10160:9;;574:9614;10133:37;;574:9614;;;;-1:-1:-1;;;;;574:9614:2;;;;;;;;;;;;;;10133:37;574:9614;10123:48;;574:9614;;;10160:9;574:9614;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;574:9614:2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;574:9614:2;;;;;;-1:-1:-1;;;;;574:9614:2;;;;;;;;;;;:::i;:::-;12478:70:58;;:::i;:::-;574:9614:2;3675:3;574:9614;;3644:29;;;;;574:9614;;-1:-1:-1;;;;;;3711:21:2;574:9614;3711:21;;:::i;:::-;574:9614;;;;;;;;;;;;;;;;;;3629:13;;574:9614;;;10133:37;;574:9614;;;;;;;;-1:-1:-1;;;;;574:9614:2;;;;;;;:::o;:::-;;;;-1:-1:-1;574:9614:2;;;;;-1:-1:-1;574:9614:2;;;;;;;;;;;;;-1:-1:-1;;;;;574:9614:2;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;;;;574:9614:2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;574:9614:2;;;;;;;;;;;;;;;:::o;:::-;;-1:-1:-1;;574:9614:2;;;;;;;;-1:-1:-1;574:9614:2;;;;;;;-1:-1:-1;;;;;574:9614:2;;;;;;;;;:::i;:::-;;;:::o;:::-;;;;-1:-1:-1;;;;;574:9614:2;;;;;;:::o;:::-;;;-1:-1:-1;;574:9614:2;;;;;;;-1:-1:-1;;;;;574:9614:2;;;;;;;;-1:-1:-1;;574:9614:2;;;;;;;;;;-1:-1:-1;;;;;574:9614:2;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;-1:-1:-1;574:9614:2;;;;;;;;-1:-1:-1;;574:9614:2;;;;:::o;:::-;;;-1:-1:-1;;574:9614:2;;;;;;;-1:-1:-1;;;;;574:9614:2;;;;;;;;-1:-1:-1;;574:9614:2;;;;;;;;;;-1:-1:-1;;;;;574:9614:2;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;-1:-1:-1;;;;;574:9614:2;;;;;;;:::o;5947:625::-;;6143:12;;;574:9614;6124:15;;;:31;574:9614;;6202:155;6285:8;6202:128;6285:8;;;:::i;:::-;6295:9;;;574:9614;;6143:12;574:9614;6240:79;6295:9;6240:79;;574:9614;2243:85;574:9614;;;;;;;;6143:12;2243:85;;574:9614;2243:85;;;;574:9614;2243:85;;;574:9614;2243:85;6240:79;;;;;;:::i;:::-;574:9614;6230:90;;6202:128;:::i;:::-;:155;:::i;:::-;6446:8;6429:37;;6446:8;;;:::i;:::-;574:9614;6143:12;574:9614;6429:37;;;6295:9;6429:37;;;;574:9614;;;;-1:-1:-1;;;;;574:9614:2;;;;;;;;;;;;;;6429:37;574:9614;6419:48;;-1:-1:-1;574:9614:2;;6295:9;574:9614;;6143:12;-1:-1:-1;574:9614:2;2243:85;574:9614;2243:85;;-1:-1:-1;;;;;574:9614:2;6557:8;;;:::i;:::-;-1:-1:-1;;;;;574:9614:2;;;;;6547:18;;5947:625::o;2243:85::-;;;;-1:-1:-1;2243:85:2;;-1:-1:-1;2243:85:2;574:9614;;;;-1:-1:-1;574:9614:2;;-1:-1:-1;574:9614:2;;;-1:-1:-1;;;;;;574:9614:2;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;574:9614:2;;;;;;;;;;;;;;:::o;:::-;-1:-1:-1;;;;;;574:9614:2;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;574:9614:2;;;;;;;;;;;:::o;:::-;-1:-1:-1;;;;;574:9614:2;;;;;;-1:-1:-1;;574:9614:2;;;;:::o;:::-;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;-1:-1:-1;574:9614:2;;;;:::o;:::-;;;:::o;1987:181::-;;574:9614;;;1987:181;;;;;:::o;:::-;;;;;;;:::i;:::-;574:9614;;;;;;;:::i;:::-;1987:181;;;;;;;;;;;;;;;;-1:-1:-1;1987:181:2;;574:9614;;;;1987:181::o;5000:941::-;;5184:12;;;574:9614;5165:15;;;:31;574:9614;;5243:483;5377:8;5243:456;5377:8;;;:::i;:::-;5407:10;;;;;:::i;:::-;5501:9;;;574:9614;5566:16;;;;;;:::i;:::-;5604:12;;;;;:::i;:::-;5648:8;1987:181;5648:8;;;;;;:::i;:::-;1987:181;;;:::i;:::-;5407:10;574:9614;;;;5638:19;574:9614;5439:9;574:9614;5298:377;5407:10;5298:377;;574:9614;1987:181;574:9614;;;;;;;;5439:9;1987:181;;574:9614;;;;;;;5470:9;1987:181;;574:9614;5439:9;;;574:9614;5501:9;1987:181;;574:9614;5470:9;;;574:9614;5184:12;1987:181;;574:9614;1987:181;5566:16;1987:181;;574:9614;5604:12;1987:181;;574:9614;;;5648:8;1987:181;;574:9614;;;;;1987:181;;;574:9614;1987:181;;;574:9614;1987:181;5298:377;;;;;;:::i;4170:676::-;;;4305:12;;;574:9614;4286:15;;;:31;574:9614;;4364:406;4490:8;4364:379;4490:8;;;:::i;:::-;4520:10;;;;;:::i;:::-;4583:9;;;574:9614;4648:12;;;;;;:::i;:::-;1987:181;4692:8;;;;;;:::i;1987:181::-;4520:10;574:9614;;;;4682:19;574:9614;4552:9;574:9614;4419:300;4520:10;4419:300;;;1785:142;574:9614;;;;;;;;4552:9;1785:142;;574:9614;;;;;;;4583:9;1785:142;;574:9614;4552:9;;;574:9614;4305:12;1785:142;;574:9614;1785:142;4648:12;1785:142;;574:9614;4692:8;1785:142;;574:9614;;;;;1785:142;;;574:9614;1785:142;;;574:9614;1785:142;4419:300;;;;;;:::i;4364:406::-;574:9614;-1:-1:-1;;;;;4808:8:2;;;:::i;:::-;574:9614;-1:-1:-1;574:9614:2;4800:7;4520:10;574:9614;4552:9;-1:-1:-1;574:9614:2;;-1:-1:-1;4787:30:2;:52;;;;4780:59;;4170:676;:::o;4787:52::-;574:9614;;-1:-1:-1;;;;;;574:9614:2;4831:8;;;:::i;6578:752::-;;;6767:12;;;574:9614;6748:15;;;:31;574:9614;;6826:428;6967:8;6826:401;6967:8;;;:::i;:::-;6997:17;;;;;:::i;:::-;7067:9;;;574:9614;7132:12;;;;;;:::i;:::-;1987:181;7176:8;;;;;;:::i;1987:181::-;6997:17;574:9614;;;;7166:19;574:9614;7036:9;574:9614;6881:322;6997:17;6881:322;;;2395:155;574:9614;;;;;;;;7036:9;1785:142;;574:9614;;;;;;;7067:9;1785:142;;574:9614;7036:9;;;574:9614;6767:12;1785:142;;574:9614;1785:142;7132:12;1785:142;;574:9614;7176:8;1785:142;;574:9614;;;;;1785:142;;;574:9614;1785:142;;;574:9614;1785:142;6881:322;;;;;;:::i;7292:355:58:-;-1:-1:-1;;7390:251:58;;;;;7292:355::o;7390:251::-;;;;;;;5757:885:61;8507:362;8216:23;;:::i;:::-;574:9614:2;;;;;;8265:22:61;574:9614:2;;;;;;8315:25:61;8507:362;;;;;;574:9614:2;8507:362:61;;;;;;;;;;;;;;;;;;6238:398;574:9614:2;6238:398:61;;;;;;;;;574:9614:2;6238:398:61;;5757:885::o;4075:1855:60:-;;;;4260:1664;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4075:1855::o;4260:1664::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6145:1089:58;574:9614:2;;;;;6299:437:58;5044:589;;;6299:437;;;;;;;;;;-1:-1:-1;;6299:437:58;6145:1089::o;3885:174:2:-;574:9614;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;574:9614:2;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;574:9614:2;;;;3885:174;:::o", "linkReferences": {}, "immutableReferences": {"61731": [{"start": 2173, "length": 32}, {"start": 2320, "length": 32}]}}, "methodIdentifiers": {"allowedInterface(bytes4)": "1fbe2e4f", "cancelOwnershipHandover()": "54d1f13d", "cancelPriceDependent((address,uint256,uint256),bytes)": "1a4e971f", "completeOwnershipHandover(address)": "f04e283e", "eip712Domain()": "84b0196e", "execute((address,address,uint256,uint256,uint256,bytes4,bytes),bytes)": "487ed96e", "executeMarginAccountRequest((address,address,uint256,uint256,uint256,bytes4,bytes),bytes)": "6e084905", "executePriceDependent((address,address,uint256,uint256,uint256,uint256,bool,bytes4,bytes),bytes)": "df477d50", "executedPriceDependentRequest(bytes32)": "********", "getNonce(address)": "2d0335ab", "initialize(address,bytes4[])": "6cce95f0", "owner()": "8da5cb5b", "ownershipHandoverExpiresAt(address)": "fee81cf4", "proxiableUUID()": "52d1902d", "removeAllowedInterfaces(bytes4[])": "10012a15", "renounceOwnership()": "715018a6", "requestOwnershipHandover()": "********", "setAllowedInterfaces(bytes4[])": "5b31c2e6", "transferOwnership(address)": "f2fde38b", "upgradeToAndCall(address,bytes)": "4f1ef286", "verify((address,address,uint256,uint256,uint256,bytes4,bytes),bytes)": "950b2e51", "verifyCancelPriceDependent((address,uint256,uint256),bytes)": "3bcb61ef", "verifyMarginAccountRequest((address,address,uint256,uint256,uint256,bytes4,bytes),bytes)": "e6bc6b56", "verifyPriceDependent((address,address,uint256,uint256,uint256,uint256,bool,bytes4,bytes),bytes)": "6b446f49"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AlreadyInitialized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"DeadlineExpired\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"ExecutionFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientValue\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InterfaceNotAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NewOwnerIsZeroAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NoHandoverRequest\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NonceAlreadyUsed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_currentPrice\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_breakpointPrice\",\"type\":\"uint256\"}],\"name\":\"PriceDependentRequestFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SignatureMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnauthorizedCallContext\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UpgradeFailed\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"OwnershipHandoverCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"OwnershipHandoverRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"Upgraded\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"\",\"type\":\"bytes4\"}],\"name\":\"allowedInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"cancelOwnershipHandover\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"}],\"internalType\":\"struct KuruForwarder.CancelPriceDependentRequest\",\"name\":\"req\",\"type\":\"tuple\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"name\":\"cancelPriceDependent\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"completeOwnershipHandover\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"eip712Domain\",\"outputs\":[{\"internalType\":\"bytes1\",\"name\":\"fields\",\"type\":\"bytes1\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"version\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"chainId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"verifyingContract\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"salt\",\"type\":\"bytes32\"},{\"internalType\":\"uint256[]\",\"name\":\"extensions\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"internalType\":\"struct KuruForwarder.ForwardRequest\",\"name\":\"req\",\"type\":\"tuple\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"name\":\"execute\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"marginAccount\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"internalType\":\"struct KuruForwarder.MarginAccountRequest\",\"name\":\"req\",\"type\":\"tuple\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"name\":\"executeMarginAccountRequest\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"price\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"isBelowPrice\",\"type\":\"bool\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"internalType\":\"struct KuruForwarder.PriceDependentRequest\",\"name\":\"req\",\"type\":\"tuple\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"name\":\"executePriceDependent\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"executedPriceDependentRequest\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"}],\"name\":\"getNonce\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"_allowedInterfaces\",\"type\":\"bytes4[]\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"result\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"ownershipHandoverExpiresAt\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"result\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"proxiableUUID\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4[]\",\"name\":\"_allowedInterfaces\",\"type\":\"bytes4[]\"}],\"name\":\"removeAllowedInterfaces\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"requestOwnershipHandover\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4[]\",\"name\":\"_allowedInterfaces\",\"type\":\"bytes4[]\"}],\"name\":\"setAllowedInterfaces\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"upgradeToAndCall\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"internalType\":\"struct KuruForwarder.ForwardRequest\",\"name\":\"req\",\"type\":\"tuple\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"name\":\"verify\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"}],\"internalType\":\"struct KuruForwarder.CancelPriceDependentRequest\",\"name\":\"req\",\"type\":\"tuple\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"name\":\"verifyCancelPriceDependent\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"marginAccount\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"internalType\":\"struct KuruForwarder.MarginAccountRequest\",\"name\":\"req\",\"type\":\"tuple\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"name\":\"verifyMarginAccountRequest\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"price\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"isBelowPrice\",\"type\":\"bool\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"internalType\":\"struct KuruForwarder.PriceDependentRequest\",\"name\":\"req\",\"type\":\"tuple\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"name\":\"verifyPriceDependent\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"AlreadyInitialized()\":[{\"details\":\"Cannot double-initialize.\"}],\"InsufficientValue()\":[{\"details\":\"Thrown when the value is insufficient\"}],\"InterfaceNotAllowed()\":[{\"details\":\"Thrown when the interface is not allowed\"}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NewOwnerIsZeroAddress()\":[{\"details\":\"The `newOwner` cannot be the zero address.\"}],\"NoHandoverRequest()\":[{\"details\":\"The `pendingOwner` does not have a valid handover request.\"}],\"NonceAlreadyUsed()\":[{\"details\":\"Thrown when the nonce is already used\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"SignatureMismatch()\":[{\"details\":\"Thrown when the signature does not match the request\"}],\"Unauthorized()\":[{\"details\":\"The caller is not authorized to call the function.\"}],\"UnauthorizedCallContext()\":[{\"details\":\"The call is from an unauthorized call context.\"}],\"UpgradeFailed()\":[{\"details\":\"The upgrade failed.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized.\"},\"OwnershipHandoverCanceled(address)\":{\"details\":\"The ownership handover to `pendingOwner` has been canceled.\"},\"OwnershipHandoverRequested(address)\":{\"details\":\"An ownership handover to `pendingOwner` has been requested.\"},\"OwnershipTransferred(address,address)\":{\"details\":\"The ownership is transferred from `oldOwner` to `newOwner`. This event is intentionally kept the same as OpenZeppelin's Ownable to be compatible with indexers and [EIP-173](https://eips.ethereum.org/EIPS/eip-173), despite it not being as lightweight as a single argument event.\"},\"Upgraded(address)\":{\"details\":\"Emitted when the proxy's implementation is upgraded.\"}},\"kind\":\"dev\",\"methods\":{\"cancelOwnershipHandover()\":{\"details\":\"Cancels the two-step ownership handover to the caller, if any.\"},\"completeOwnershipHandover(address)\":{\"details\":\"Allows the owner to complete the two-step ownership handover to `pendingOwner`. Reverts if there is no existing ownership handover requested by `pendingOwner`.\"},\"eip712Domain()\":{\"details\":\"See: https://eips.ethereum.org/EIPS/eip-5267\"},\"owner()\":{\"details\":\"Returns the owner of the contract.\"},\"ownershipHandoverExpiresAt(address)\":{\"details\":\"Returns the expiry timestamp for the two-step ownership handover to `pendingOwner`.\"},\"proxiableUUID()\":{\"details\":\"Returns the storage slot used by the implementation, as specified in [ERC1822](https://eips.ethereum.org/EIPS/eip-1822). Note: The `notDelegated` modifier prevents accidental upgrades to an implementation that is a proxy contract.\"},\"renounceOwnership()\":{\"details\":\"Allows the owner to renounce their ownership.\"},\"requestOwnershipHandover()\":{\"details\":\"Request a two-step ownership handover to the caller. The request will automatically expire in 48 hours (172800 seconds) by default.\"},\"transferOwnership(address)\":{\"details\":\"Allows the owner to transfer the ownership to `newOwner`.\"},\"upgradeToAndCall(address,bytes)\":{\"details\":\"Upgrades the proxy's implementation to `newImplementation`. Emits a {Upgraded} event. Note: Passing in empty `data` skips the delegatecall to `newImplementation`.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/KuruForwarder.sol\":\"KuruForwarder\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/KuruForwarder.sol\":{\"keccak256\":\"0xacc170f5ce66221ae208235ae21d4014b537002f3085c729fd67793a97ce8730\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ce914cf74f9d9d3fd73b8392318063d4b57c90e7b615990030d1afd1f2402368\",\"dweb:/ipfs/QmZvTGqYLwiBSQKDTKVEVAQN3mEBMuLefuUoAWrW6uPAP4\"]},\"contracts/interfaces/IOrderBook.sol\":{\"keccak256\":\"0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d\",\"dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68\"]},\"contracts/libraries/Errors.sol\":{\"keccak256\":\"0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7\",\"dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu\"]},\"node_modules/solady/src/auth/Ownable.sol\":{\"keccak256\":\"0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3\",\"dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs\"]},\"node_modules/solady/src/utils/ECDSA.sol\":{\"keccak256\":\"0x077d168511141c83fd93914f4609c5363341da03a3971cbc0d3f6a75e9893de0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c218864adb4e97d81908318d9bd0ed75f40ec996c826141fab2646135820295b\",\"dweb:/ipfs/QmVQyWG6Ff4LAUnzj6JMEHHYfNBANVYLaMkun6TYaWShms\"]},\"node_modules/solady/src/utils/EIP712.sol\":{\"keccak256\":\"0xb5c4c8ac5368c9785b4e30314f4ad6f3ae13bdc21679007735681d13da797bec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c4456a4eaa8748f802fd1188db6405d18c452eb7c0dde84a49b49a7f94b5970d\",\"dweb:/ipfs/QmZzsFn4VwvBFy2MJVJXvntCQsDRCXbRrSKKfXxXv9jYGM\"]},\"node_modules/solady/src/utils/Initializable.sol\":{\"keccak256\":\"0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559\",\"dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy\"]},\"node_modules/solady/src/utils/UUPSUpgradeable.sol\":{\"keccak256\":\"0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16\",\"dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "AlreadyInitialized"}, {"inputs": [], "type": "error", "name": "DeadlineExpired"}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "type": "error", "name": "ExecutionFailed"}, {"inputs": [], "type": "error", "name": "InsufficientValue"}, {"inputs": [], "type": "error", "name": "InterfaceNotAllowed"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "NewOwnerIsZeroAddress"}, {"inputs": [], "type": "error", "name": "NoHandoverRequest"}, {"inputs": [], "type": "error", "name": "NonceAlreadyUsed"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [{"internalType": "uint256", "name": "_currentPrice", "type": "uint256"}, {"internalType": "uint256", "name": "_breakpointPrice", "type": "uint256"}], "type": "error", "name": "PriceDependentRequestFailed"}, {"inputs": [], "type": "error", "name": "SignatureMismatch"}, {"inputs": [], "type": "error", "name": "Unauthorized"}, {"inputs": [], "type": "error", "name": "UnauthorizedCallContext"}, {"inputs": [], "type": "error", "name": "UpgradeFailed"}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipHandoverCanceled", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipHandoverRequested", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "old<PERSON>wner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address", "indexed": true}], "type": "event", "name": "Upgraded", "anonymous": false}, {"inputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "allowedInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "cancelOwnershipHandover"}, {"inputs": [{"internalType": "struct KuruFor<PERSON>er.CancelPriceDependentRequest", "name": "req", "type": "tuple", "components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}]}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "cancelPriceDependent"}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "stateMutability": "payable", "type": "function", "name": "completeOwnershipHandover"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "eip712Domain", "outputs": [{"internalType": "bytes1", "name": "fields", "type": "bytes1"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "version", "type": "string"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "address", "name": "verifyingContract", "type": "address"}, {"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "uint256[]", "name": "extensions", "type": "uint256[]"}]}, {"inputs": [{"internalType": "struct KuruForwarder.ForwardRequest", "name": "req", "type": "tuple", "components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "market", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}, {"internalType": "bytes", "name": "data", "type": "bytes"}]}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "execute", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [{"internalType": "struct KuruFor<PERSON><PERSON>.MarginAccountRequest", "name": "req", "type": "tuple", "components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "marginAccount", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}, {"internalType": "bytes", "name": "data", "type": "bytes"}]}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "executeMarginAccountRequest", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [{"internalType": "struct Kuru<PERSON>or<PERSON><PERSON>.PriceDependentRequest", "name": "req", "type": "tuple", "components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "market", "type": "address"}, {"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bool", "name": "isBelowPrice", "type": "bool"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}, {"internalType": "bytes", "name": "data", "type": "bytes"}]}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "executePriceDependent", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "executedPriceDependentRequest", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getNonce", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "bytes4[]", "name": "_allowedInterfaces", "type": "bytes4[]"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "result", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "stateMutability": "view", "type": "function", "name": "ownershipHandoverExpiresAt", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes4[]", "name": "_allowedInterfaces", "type": "bytes4[]"}], "stateMutability": "nonpayable", "type": "function", "name": "removeAllowedInterfaces"}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "renounceOwnership"}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "requestOwnershipHandover"}, {"inputs": [{"internalType": "bytes4[]", "name": "_allowedInterfaces", "type": "bytes4[]"}], "stateMutability": "nonpayable", "type": "function", "name": "setAllowedInterfaces"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "payable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "upgradeToAndCall"}, {"inputs": [{"internalType": "struct KuruForwarder.ForwardRequest", "name": "req", "type": "tuple", "components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "market", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}, {"internalType": "bytes", "name": "data", "type": "bytes"}]}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "verify", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "struct KuruFor<PERSON>er.CancelPriceDependentRequest", "name": "req", "type": "tuple", "components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}]}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "verifyCancelPriceDependent", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "struct KuruFor<PERSON><PERSON>.MarginAccountRequest", "name": "req", "type": "tuple", "components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "marginAccount", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}, {"internalType": "bytes", "name": "data", "type": "bytes"}]}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "verifyMarginAccountRequest", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "struct Kuru<PERSON>or<PERSON><PERSON>.PriceDependentRequest", "name": "req", "type": "tuple", "components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "market", "type": "address"}, {"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bool", "name": "isBelowPrice", "type": "bool"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}, {"internalType": "bytes", "name": "data", "type": "bytes"}]}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "verifyPriceDependent", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"cancelOwnershipHandover()": {"details": "Cancels the two-step ownership handover to the caller, if any."}, "completeOwnershipHandover(address)": {"details": "Allows the owner to complete the two-step ownership handover to `pendingOwner`. Reverts if there is no existing ownership handover requested by `pendingOwner`."}, "eip712Domain()": {"details": "See: https://eips.ethereum.org/EIPS/eip-5267"}, "owner()": {"details": "Returns the owner of the contract."}, "ownershipHandoverExpiresAt(address)": {"details": "Returns the expiry timestamp for the two-step ownership handover to `pendingOwner`."}, "proxiableUUID()": {"details": "Returns the storage slot used by the implementation, as specified in [ERC1822](https://eips.ethereum.org/EIPS/eip-1822). Note: The `notDelegated` modifier prevents accidental upgrades to an implementation that is a proxy contract."}, "renounceOwnership()": {"details": "Allows the owner to renounce their ownership."}, "requestOwnershipHandover()": {"details": "Request a two-step ownership handover to the caller. The request will automatically expire in 48 hours (172800 seconds) by default."}, "transferOwnership(address)": {"details": "Allows the owner to transfer the ownership to `newOwner`."}, "upgradeToAndCall(address,bytes)": {"details": "Upgrades the proxy's implementation to `newImplementation`. Emits a {Upgraded} event. Note: Passing in empty `data` skips the delegatecall to `newImplementation`."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/KuruForwarder.sol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/KuruForwarder.sol": {"keccak256": "0xacc170f5ce66221ae208235ae21d4014b537002f3085c729fd67793a97ce8730", "urls": ["bzz-raw://ce914cf74f9d9d3fd73b8392318063d4b57c90e7b615990030d1afd1f2402368", "dweb:/ipfs/QmZvTGqYLwiBSQKDTKVEVAQN3mEBMuLefuUoAWrW6uPAP4"], "license": "BUSL-1.1"}, "contracts/interfaces/IOrderBook.sol": {"keccak256": "0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b", "urls": ["bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d", "dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68"], "license": "GPL-2.0-or-later"}, "contracts/libraries/Errors.sol": {"keccak256": "0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f", "urls": ["bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7", "dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu"], "license": "BUSL-1.1"}, "node_modules/solady/src/auth/Ownable.sol": {"keccak256": "0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3", "urls": ["bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3", "dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs"], "license": "MIT"}, "node_modules/solady/src/utils/ECDSA.sol": {"keccak256": "0x077d168511141c83fd93914f4609c5363341da03a3971cbc0d3f6a75e9893de0", "urls": ["bzz-raw://c218864adb4e97d81908318d9bd0ed75f40ec996c826141fab2646135820295b", "dweb:/ipfs/QmVQyWG6Ff4LAUnzj6JMEHHYfNBANVYLaMkun6TYaWShms"], "license": "MIT"}, "node_modules/solady/src/utils/EIP712.sol": {"keccak256": "0xb5c4c8ac5368c9785b4e30314f4ad6f3ae13bdc21679007735681d13da797bec", "urls": ["bzz-raw://c4456a4eaa8748f802fd1188db6405d18c452eb7c0dde84a49b49a7f94b5970d", "dweb:/ipfs/QmZzsFn4VwvBFy2MJVJXvntCQsDRCXbRrSKKfXxXv9jYGM"], "license": "MIT"}, "node_modules/solady/src/utils/Initializable.sol": {"keccak256": "0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec", "urls": ["bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559", "dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy"], "license": "MIT"}, "node_modules/solady/src/utils/UUPSUpgradeable.sol": {"keccak256": "0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e", "urls": ["bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16", "dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV"], "license": "MIT"}}, "version": 1}, "id": 2}