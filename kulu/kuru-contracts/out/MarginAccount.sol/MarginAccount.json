{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "balances", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "batchWithdrawMaxTokens", "inputs": [{"name": "_tokens", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "cancelOwnershipHandover", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "completeOwnershipHandover", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "creditFee", "inputs": [{"name": "_assetA", "type": "address", "internalType": "address"}, {"name": "_feeA", "type": "uint256", "internalType": "uint256"}, {"name": "_assetB", "type": "address", "internalType": "address"}, {"name": "_feeB", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "creditUser", "inputs": [{"name": "_user", "type": "address", "internalType": "address"}, {"name": "_token", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}, {"name": "_useMargin", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "creditUsersEncoded", "inputs": [{"name": "_encodedData", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "debitUser", "inputs": [{"name": "_user", "type": "address", "internalType": "address"}, {"name": "_token", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "deposit", "inputs": [{"name": "_user", "type": "address", "internalType": "address"}, {"name": "_token", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "getBalance", "inputs": [{"name": "_user", "type": "address", "internalType": "address"}, {"name": "_token", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_owner", "type": "address", "internalType": "address"}, {"name": "_router", "type": "address", "internalType": "address"}, {"name": "_feeCollector", "type": "address", "internalType": "address"}, {"name": "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isTrusted<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "forwarder", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "result", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "ownershipHandoverExpiresAt", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "internalType": "address"}], "outputs": [{"name": "result", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "requestOwnershipHandover", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "setFeeCollector", "inputs": [{"name": "_feeCollector", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "toggleProtocolState", "inputs": [{"name": "_state", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "updateMarkets", "inputs": [{"name": "_marketAddress", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "verifiedMarket", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "_amount", "type": "uint256", "internalType": "uint256"}, {"name": "_token", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "owner", "type": "address", "indexed": false, "internalType": "address"}, {"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "FeeCollectorUpdated", "inputs": [{"name": "newFeeCollector", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "OwnershipHandoverCanceled", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipHandoverRequested", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "old<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ProtocolStateUpdated", "inputs": [{"name": "newState", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "owner", "type": "address", "indexed": false, "internalType": "address"}, {"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "AlreadyInitialized", "inputs": []}, {"type": "error", "name": "FeeCollectorNotChanged", "inputs": []}, {"type": "error", "name": "InsufficientBalance", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NativeAssetMismatch", "inputs": []}, {"type": "error", "name": "NewOwnerIsZeroAddress", "inputs": []}, {"type": "error", "name": "NoHandoverRequest", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OnlyR<PERSON>erAllowed", "inputs": []}, {"type": "error", "name": "OnlyVerifiedMarketsAllowed", "inputs": []}, {"type": "error", "name": "ProtocolPaused", "inputs": []}, {"type": "error", "name": "ProtocolStateNotChanged", "inputs": []}, {"type": "error", "name": "Unauthorized", "inputs": []}, {"type": "error", "name": "UnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UpgradeFailed", "inputs": []}, {"type": "error", "name": "ZeroAddressNotAllowed", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "668:8704:3:-:0;;;;;;;1525:4:64;1501:31;;-1:-1:-1;;6669:609:62;;;;;;2001:66;6669:609;-1:-1:-1;;;;;;6669:609:62;;;-1:-1:-1;668:8704:3;;;;;;1501:31:64;668:8704:3;;;;;;;;;;;6669:609:62;-1:-1:-1;;;;;;;6669:609:62;-1:-1:-1;;;;;6669:609:62;;;;;;;;;;;;-1:-1:-1;6669:609:62;;;;668:8704:3;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "668:8704:3:-:0;;;;;;;;;-1:-1:-1;668:8704:3;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;668:8704:3;;;;;;:::i;:::-;11885:237:58;;;668:8704:3;11885:237:58;668:8704:3;11885:237:58;;;;668:8704:3;;;;;;;;;;;;;;;;-1:-1:-1;;668:8704:3;;;;;;:::i;:::-;;;:::i;:::-;;;;:::i;:::-;;;-1:-1:-1;;;;;668:8704:3;;;;;;;;;2001:66:62;;3207:622;;;;;;;;;668:8704:3;-1:-1:-1;;;5044:589:58;;;-1:-1:-1;;;;;5044:589:58;;;;;;;;;-1:-1:-1;;5044:589:58;668:8704:3;;-1:-1:-1;668:8704:3;5044:589:58;668:8704:3;;5044:589:58;668:8704:3;;;;;;-1:-1:-1;;;;;668:8704:3;;3207:622:62;668:8704:3;;;3207:622:62;668:8704:3;;;;;;;-1:-1:-1;;;;;668:8704:3;;;;;;;;-1:-1:-1;;;;;668:8704:3;;;;;;;;3892:296:62;;668:8704:3;3892:296:62;;;;1859:4:3;668:8704;3892:296:62;;668:8704:3;3892:296:62;;668:8704:3;5044:589:58;;668:8704:3;5044:589:58;668:8704:3;5044:589:58;;3207:622:62;;;;;;;;;;;;;;;;;;;;;;;;668:8704:3;3207:622:62;668:8704:3;3207:622:62;;668:8704:3;;;-1:-1:-1;;668:8704:3;;;;;;:::i;:::-;12478:70:58;;:::i;:::-;8479:183;;;;;;8681:8;;;:::i;8479:183::-;;668:8704:3;8479:183:58;668:8704:3;8479:183:58;;668:8704:3;;;-1:-1:-1;;668:8704:3;;;;;;:::i;:::-;12478:70:58;;:::i;:::-;10506:526;;;;668:8704:3;10506:526:58;668:8704:3;10506:526:58;;;;;;;;;668:8704:3;11051:12:58;10506:526;;11051:12;:::i;10506:526::-;;668:8704:3;10506:526:58;668:8704:3;10506:526:58;;668:8704:3;;;;;;-1:-1:-1;;668:8704:3;;;;;;;;;;;;;;12478:70:58;;:::i;:::-;668:8704:3;;;;;;;;;;1298:24;668:8704;;-1:-1:-1;;;;668:8704:3;;;;;-1:-1:-1;;;668:8704:3;;;;;;;;;1418:28;;668:8704;;1418:28;668:8704;;;;;;;;;;;;;;;;-1:-1:-1;;668:8704:3;;;;9012:26;668:8704;;:::i;:::-;;;:::i;:::-;9012:26;;:::i;:::-;668:8704;;9003:8;668:8704;;;;;;;;;;;;;;;;;;;-1:-1:-1;;668:8704:3;;;;;;:::i;:::-;12478:70:58;;:::i;:::-;668:8704:3;;-1:-1:-1;;;;;668:8704:3;;;;;;1544:29;;668:8704;;-1:-1:-1;;;;;;668:8704:3;;;;;;;;;;1673:34;;668:8704;;1673:34;668:8704;;;;;;;;;;;;;;;;-1:-1:-1;;668:8704:3;;;;-1:-1:-1;;11523:61:58;668:8704:3;;-1:-1:-1;;;;;668:8704:3;;;;;;;;;;;;;;-1:-1:-1;;668:8704:3;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;;;;;668:8704:3;;7541:19;668:8704;;-1:-1:-1;;;;;668:8704:3;;;;7660:9;;:20;668:8704;;;;9296:31;;;;668:8704;-1:-1:-1;;;;;668:8704:3;;;;;;;;;;;;9296:31;;;668:8704;;;;;;;;;;;;;8044:31;668:8704;8044:31;668:8704;;;;9286:42;;668:8704;;7738:8;9296:31;668:8704;;;;7738:47;668:8704;;;7738:47;:::i;:::-;668:8704;;7616:413;668:8704;;;-1:-1:-1;;;;;668:8704:3;;;;;;;;;1068:42;;;668:8704;1068:42;;;668:8704;;;;;;1068:42;;;;;8044:31;;;;668:8704;;;;;;;;;;;;;;;;;;;;;;7616:413;7824:9;668:8704;;7905:26;;;;:::i;:::-;668:8704;;7896:8;668:8704;;;;;7896:47;668:8704;;;7896:47;:::i;:::-;668:8704;;7981:12;;:::i;:::-;668:8704;9254:988:63;;;;;8003:4:3;668:8704;9254:988:63;;;;;;;;;;668:8704:3;;9254:988:63;;;;;;;;7896:8:3;668:8704;9254:988:63;;;;;;;8044:31:3;9254:988:63;8044:31:3;9254:988:63;668:8704:3;9254:988:63;;668:8704:3;9254:988:63;7616:413:3;;9254:988:63;;668:8704:3;9254:988:63;668:8704:3;9254:988:63;;668:8704:3;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;668:8704:3;;;;;;:::i;:::-;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;4357:10;668:8704;;4342:14;668:8704;;;;;;;;;;;4430:277;;;4469:26;;;;;:::i;:::-;668:8704;;;;;4460:47;668:8704;;;;;;4460:47;:::i;:::-;668:8704;;;4430:277;-1:-1:-1;;;;;668:8704:3;;4542:16;668:8704;;4605:7;;;:::i;4538:159::-;4674:7;;;;:::i;668:8704::-;;;;;;;;;;;;-1:-1:-1;;668:8704:3;;;;12478:70:58;;:::i;:::-;668:8704:3;6299:437:58;;;;;;;-1:-1:-1;;;;;6299:437:58;668:8704:3;;;;;;;-1:-1:-1;;668:8704:3;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;5048:10;668:8704;;5033:14;668:8704;;;;;;;;;;;;5149:576;5156:28;;;;;;668:8704;5149:576;5324:3;668:8704;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;668:8704:3;;;;;;;;5406:309;;;5449:26;;;;;:::i;:::-;-1:-1:-1;668:8704:3;;;;5440:47;668:8704;-1:-1:-1;668:8704:3;;;;5440:47;:::i;:::-;668:8704;;5406:309;5149:576;;5406:309;5530:16;;668:8704;;5597:7;;;:::i;5526:175::-;5674:7;;;;:::i;668:8704::-;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;3573:10;668:8704;;3558:14;668:8704;;;;;;;;;;;3662:26;;;;:::i;:::-;668:8704;;3653:8;668:8704;;;;;;;3653:47;668:8704;;3764:26;;;:::i;:::-;668:8704;;3653:8;668:8704;;3755:47;668:8704;;;;;;3755:47;:::i;668:8704::-;;;;;;;;;;;;;;;-1:-1:-1;;668:8704:3;;;;-1:-1:-1;;;;;668:8704:3;;:::i;:::-;;;;919:46;668:8704;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;668:8704:3;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;668:8704:3;;;;;;;2334:29;668:8704;;;;;;-1:-1:-1;;668:8704:3;;;;9831:339:58;;;;668:8704:3;9831:339:58;668:8704:3;9831:339:58;;;;;;668:8704:3;9831:339:58;;668:8704:3;;;;;;;-1:-1:-1;;668:8704:3;;;;6466:184:64;6407:6;6466:184;;;668:8704:3;;;2619:66:64;668:8704:3;;;6466:184:64;;668:8704:3;6466:184:64;668:8704:3;6466:184:64;;668:8704:3;;;-1:-1:-1;;668:8704:3;;;;;;:::i;:::-;;;;;;;;;;;;;;;:::i;:::-;5771:446:64;;;5712:6;5771:446;;;1876:89:3;;:::i;:::-;668:8704;;;;;4259:1327:64;;;;;668:8704:3;4259:1327:64;668:8704:3;4259:1327:64;;;;;;;;;;668:8704:3;4259:1327:64;;;;;;;;668:8704:3;4259:1327:64;;668:8704:3;4259:1327:64;668:8704:3;4259:1327:64;;;;;;;;;;;;668:8704:3;4259:1327:64;;668:8704:3;4259:1327:64;;;;;;;;;668:8704:3;4259:1327:64;;668:8704:3;;;;;;-1:-1:-1;;668:8704:3;;;;;;:::i;:::-;;;:::i;:::-;;;;;;;;;6175:10;668:8704;;6160:14;668:8704;;;;;;;;;;;;;6320:34;;6257;;-1:-1:-1;;;;;668:8704:3;6257:34;:::i;:::-;668:8704;;;;;;;;6248:53;668:8704;;;;6248:53;:::i;:::-;668:8704;;;;-1:-1:-1;;;;;668:8704:3;6320:34;:::i;:::-;668:8704;;;;;;;;6311:53;668:8704;;;;6311:53;:::i;668:8704::-;;;-1:-1:-1;;668:8704:3;;;;9239:383:58;;;;668:8704:3;9239:383:58;7972:9;9132:15;668:8704:3;9239:383:58;;;;;;668:8704:3;9239:383:58;;668:8704:3;;;;;;;-1:-1:-1;;668:8704:3;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6728:18;;;;;;668:8704;6748:3;668:8704;6799:12;6787:37;6799:12;;:::i;:::-;6813:10;;;;;;:::i;:::-;;:::i;6787:37::-;668:8704;;;;;;;;;6848:37;6860:12;;:::i;:::-;6874:10;;;;;;:::i;6848:37::-;668:8704;;;;;;;;;;6908:12;6904:249;;6748:3;;668:8704;6713:13;;6904:249;668:8704;;;;;6944:10;;;;;;:::i;:::-;668:8704;;;7017:8;6988:12;;;:::i;:::-;7017:8;:::i;:::-;6904:249;;;6940:199;7111:8;7073:10;;;;;;;:::i;:::-;7097:12;;:::i;:::-;7111:8;;:::i;:::-;6940:199;;668:8704;;;;;;-1:-1:-1;;668:8704:3;;;;;;:::i;:::-;2025:21;668:8704;-1:-1:-1;;;;;668:8704:3;2011:10;:35;668:8704;;;;;;;;;;-1:-1:-1;;;;;668:8704:3;;;;;3058:14;668:8704;;;;;;;-1:-1:-1;;668:8704:3;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;668:8704:3;;;;;;;;:::i;:::-;;;;;;;;;8371:33;8383:12;;;:::i;:::-;8371:33;:::i;:::-;668:8704;;;;;;;;;8351:54;;668:8704;;8695:41;8480:12;8468:33;8480:12;;;:::i;8468:33::-;668:8704;;;;;;;;8459:54;668:8704;;;8459:54;:::i;:::-;668:8704;;-1:-1:-1;;;;;668:8704:3;;;;8588:7;8559:12;;;:::i;8588:7::-;8695:41;8706:12;;:::i;:::-;668:8704;;;-1:-1:-1;;;;;668:8704:3;;;;;;;;;1068:42;;;668:8704;1068:42;;668:8704;;;;;;1068:42;;;;;8523:157;8661:7;8647:12;;;:::i;:::-;8661:7;;:::i;:::-;8523:157;;668:8704;;;;-1:-1:-1;;;;;668:8704:3;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;668:8704:3;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;668:8704:3;;;;;;:::o;:::-;;;-1:-1:-1;;;;;668:8704:3;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;-1:-1:-1;;;;;668:8704:3;;;;;;;;;-1:-1:-1;;;;;668:8704:3;;;;;;;;;;:::o;:::-;;;;;;;;;;:::o;:::-;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;-1:-1:-1;;;;;668:8704:3;;;;;;;:::o;:::-;;;;;;;;;;:::o;869:404:11:-;668:8704:3;;-1:-1:-1;;;;;668:8704:3;1068:10:11;2334:29:3;;1049:71:11;;869:404;1045:222;;;668:8704:3;;958:8:11;668:8704:3;958:8:11;668:8704:3;;;;;;;;1136:72:11:o;1045:222::-;1068:10;1239:17;:::o;1049:71::-;958:8;1457:2;958:8;1083:37;;1049:71;;9185:150:3;668:8704;;-1:-1:-1;;668:8704:3;;;;;;9296:31;;;668:8704;;;;;;;;;;;9296:31;;;668:8704;;;;;;;;;;;;;;;9286:42;;9185:150;:::o;13466:939:63:-;13593:806;;;13466:939;13593:806;13466:939;;;13593:806;;;;;;;;;;;;;;;;;;;;;;;;;13466:939::o;13593:806::-;;;;;;;4031:342;4146:221;4031:342;;;4146:221;;;;;;;4031:342::o;4146:221::-;;;;;;;7292:355:58;-1:-1:-1;;7390:251:58;;;;;7292:355::o;7390:251::-;;;;;;;6145:1089;668:8704:3;;;;;6299:437:58;;;;;;;;;;;;;;-1:-1:-1;;6299:437:58;6145:1089::o", "linkReferences": {}, "immutableReferences": {"61731": [{"start": 2545, "length": 32}, {"start": 2693, "length": 32}]}}, "methodIdentifiers": {"balances(bytes32)": "8909aa3f", "batchWithdrawMaxTokens(address[])": "0f163fa8", "cancelOwnershipHandover()": "54d1f13d", "completeOwnershipHandover(address)": "f04e283e", "creditFee(address,uint256,address,uint256)": "49f59b62", "creditUser(address,address,uint256,bool)": "75eab98e", "creditUsersEncoded(bytes)": "6f5b601e", "debitUser(address,address,uint256)": "6c139252", "deposit(address,address,uint256)": "8340f549", "getBalance(address,address)": "d4fac45d", "initialize(address,address,address,address)": "f8c8765e", "isTrustedForwarder(address)": "572b6c05", "owner()": "8da5cb5b", "ownershipHandoverExpiresAt(address)": "fee81cf4", "proxiableUUID()": "52d1902d", "renounceOwnership()": "715018a6", "requestOwnershipHandover()": "25692962", "setFeeCollector(address)": "a42dce80", "toggleProtocolState(bool)": "e004f924", "transferOwnership(address)": "f2fde38b", "updateMarkets(address)": "01aef349", "upgradeToAndCall(address,bytes)": "4f1ef286", "verifiedMarket(address)": "5f71a07c", "withdraw(uint256,address)": "00f714ce"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AlreadyInitialized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FeeCollectorNotChanged\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NativeAssetMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NewOwnerIsZeroAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NoHandoverRequest\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OnlyRouterAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OnlyVerifiedMarketsAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ProtocolPaused\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ProtocolStateNotChanged\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnauthorizedCallContext\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UpgradeFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroAddressNotAllowed\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Deposit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newFeeCollector\",\"type\":\"address\"}],\"name\":\"FeeCollectorUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"OwnershipHandoverCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"OwnershipHandoverRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"newState\",\"type\":\"bool\"}],\"name\":\"ProtocolStateUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"Upgraded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Withdrawal\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"balances\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"_tokens\",\"type\":\"address[]\"}],\"name\":\"batchWithdrawMaxTokens\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"cancelOwnershipHandover\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"completeOwnershipHandover\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_assetA\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_feeA\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_assetB\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_feeB\",\"type\":\"uint256\"}],\"name\":\"creditFee\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_user\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"_useMargin\",\"type\":\"bool\"}],\"name\":\"creditUser\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"_encodedData\",\"type\":\"bytes\"}],\"name\":\"creditUsersEncoded\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_user\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"debitUser\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_user\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"deposit\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_user\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_token\",\"type\":\"address\"}],\"name\":\"getBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_router\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_feeCollector\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_trustedForwarder\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"forwarder\",\"type\":\"address\"}],\"name\":\"isTrustedForwarder\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"result\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"ownershipHandoverExpiresAt\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"result\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"proxiableUUID\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"requestOwnershipHandover\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_feeCollector\",\"type\":\"address\"}],\"name\":\"setFeeCollector\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"_state\",\"type\":\"bool\"}],\"name\":\"toggleProtocolState\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_marketAddress\",\"type\":\"address\"}],\"name\":\"updateMarkets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"upgradeToAndCall\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"verifiedMarket\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_token\",\"type\":\"address\"}],\"name\":\"withdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"errors\":{\"AlreadyInitialized()\":[{\"details\":\"Cannot double-initialize.\"}],\"FeeCollectorNotChanged()\":[{\"details\":\"Thrown when fee collector is not set\"}],\"InsufficientBalance()\":[{\"details\":\"Thrown when a user has insufficient margin account balance\"}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NativeAssetMismatch()\":[{\"details\":\"Thrown when msg.value is not zero when native assets are not required\"}],\"NewOwnerIsZeroAddress()\":[{\"details\":\"The `newOwner` cannot be the zero address.\"}],\"NoHandoverRequest()\":[{\"details\":\"The `pendingOwner` does not have a valid handover request.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"OnlyRouterAllowed()\":[{\"details\":\"Thrown when a non-router tries to update markets\"}],\"OnlyVerifiedMarketsAllowed()\":[{\"details\":\"Thrown when a non-verified market tries to execute a market action\"}],\"ProtocolPaused()\":[{\"details\":\"Thrown when protocol is paused\"}],\"ProtocolStateNotChanged()\":[{\"details\":\"Thrown when protocol state is not changed\"}],\"Unauthorized()\":[{\"details\":\"The caller is not authorized to call the function.\"}],\"UnauthorizedCallContext()\":[{\"details\":\"The call is from an unauthorized call context.\"}],\"UpgradeFailed()\":[{\"details\":\"The upgrade failed.\"}],\"ZeroAddressNotAllowed()\":[{\"details\":\"Thrown when zero address is passed as a parameter\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized.\"},\"OwnershipHandoverCanceled(address)\":{\"details\":\"The ownership handover to `pendingOwner` has been canceled.\"},\"OwnershipHandoverRequested(address)\":{\"details\":\"An ownership handover to `pendingOwner` has been requested.\"},\"OwnershipTransferred(address,address)\":{\"details\":\"The ownership is transferred from `oldOwner` to `newOwner`. This event is intentionally kept the same as OpenZeppelin's Ownable to be compatible with indexers and [EIP-173](https://eips.ethereum.org/EIPS/eip-173), despite it not being as lightweight as a single argument event.\"},\"Upgraded(address)\":{\"details\":\"Emitted when the proxy's implementation is upgraded.\"}},\"kind\":\"dev\",\"methods\":{\"batchWithdrawMaxTokens(address[])\":{\"details\":\"This function allows a user to claim all of their tokens from the margin account.\",\"params\":{\"_tokens\":\"Array of tokens to claim.\"}},\"cancelOwnershipHandover()\":{\"details\":\"Cancels the two-step ownership handover to the caller, if any.\"},\"completeOwnershipHandover(address)\":{\"details\":\"Allows the owner to complete the two-step ownership handover to `pendingOwner`. Reverts if there is no existing ownership handover requested by `pendingOwner`.\"},\"creditFee(address,uint256,address,uint256)\":{\"details\":\"This function allows a verified market to register fee collected\",\"params\":{\"_assetA\":\"Address of first asset\",\"_assetB\":\"Address of second asset\",\"_feeA\":\"Fee amount of _assetA to be credited\",\"_feeB\":\"Fee amount of _assetB to be credited\"}},\"creditUser(address,address,uint256,bool)\":{\"details\":\"This function is only callable by a verified market. Verified markets credit user balances.\",\"params\":{\"_amount\":\"amount to credit.\",\"_token\":\"address of token to be credit.\",\"_useMargin\":\"whether to use margin or not - if not, user will receive token through an erc20 transfer\",\"_user\":\"address of the user whose account should be credited.\"}},\"creditUsersEncoded(bytes)\":{\"details\":\"This function is only callable by a verified market. Verified markets credit a bunch of users the tokens.\",\"params\":{\"_encodedData\":\"Address of the user whose account should be credited.\"}},\"debitUser(address,address,uint256)\":{\"details\":\"This function is only callable by a verified market. Verified markets consume user balances.\",\"params\":{\"_amount\":\"amount to debit.\",\"_token\":\"address of token to be debited.\",\"_user\":\"address of the user whose account should be debited.\"}},\"deposit(address,address,uint256)\":{\"details\":\"Function for EOAs to deposit tokens on behalf of a user.\",\"params\":{\"_amount\":\"amount to credit.\",\"_token\":\"address of token to be credit.\",\"_user\":\"Address of the user whose account should be credited.\"}},\"getBalance(address,address)\":{\"details\":\"Function to check balance of a user for a given token.\",\"params\":{\"_token\":\"tokenA addresss\",\"_user\":\"user addresss\"}},\"owner()\":{\"details\":\"Returns the owner of the contract.\"},\"ownershipHandoverExpiresAt(address)\":{\"details\":\"Returns the expiry timestamp for the two-step ownership handover to `pendingOwner`.\"},\"proxiableUUID()\":{\"details\":\"Returns the storage slot used by the implementation, as specified in [ERC1822](https://eips.ethereum.org/EIPS/eip-1822). Note: The `notDelegated` modifier prevents accidental upgrades to an implementation that is a proxy contract.\"},\"renounceOwnership()\":{\"details\":\"Allows the owner to renounce their ownership.\"},\"requestOwnershipHandover()\":{\"details\":\"Request a two-step ownership handover to the caller. The request will automatically expire in 48 hours (172800 seconds) by default.\"},\"transferOwnership(address)\":{\"details\":\"Allows the owner to transfer the ownership to `newOwner`.\"},\"updateMarkets(address)\":{\"details\":\"This function allows the router to register an official verified market.\",\"params\":{\"_marketAddress\":\"Address of the orderbook of the market.\"}},\"upgradeToAndCall(address,bytes)\":{\"details\":\"Upgrades the proxy's implementation to `newImplementation`. Emits a {Upgraded} event. Note: Passing in empty `data` skips the delegatecall to `newImplementation`.\"},\"withdraw(uint256,address)\":{\"details\":\"Function for users to withdraw their assets.\",\"params\":{\"_amount\":\"amount to withdraw.\",\"_token\":\"address of token to be withdrawn.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/MarginAccount.sol\":\"MarginAccount\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/MarginAccount.sol\":{\"keccak256\":\"0xb84f7d6424bcaa18060b459e607e79042dd445ab2dcbd7d9627ca5ca67ce5ace\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://063e892e7ef061958cd2e2a91be36288e63a2c9f75c36cb3759d7cf7dfb979c9\",\"dweb:/ipfs/QmPFvYPyTuq4hz6R51Uf5VJtcQJzHGCamaGjybJRpfnpec\"]},\"contracts/interfaces/IMarginAccount.sol\":{\"keccak256\":\"0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4\",\"dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo\"]},\"contracts/libraries/ERC2771Context.sol\":{\"keccak256\":\"0x7458e7a07eb42f479dd6a547733373f21d7a45e0d9a78d545e16db6639e61ef5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8d278e5acd57e1943f2e411bcd6f4c13fb8620ae046cf8c8026fd9a103315497\",\"dweb:/ipfs/QmYWsYRhEV9A7sXgkFX6ES278EMN7b2rJ1JyetQ2XBQrWP\"]},\"contracts/libraries/Errors.sol\":{\"keccak256\":\"0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7\",\"dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu\"]},\"node_modules/solady/src/auth/Ownable.sol\":{\"keccak256\":\"0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3\",\"dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs\"]},\"node_modules/solady/src/utils/Initializable.sol\":{\"keccak256\":\"0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559\",\"dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy\"]},\"node_modules/solady/src/utils/SafeTransferLib.sol\":{\"keccak256\":\"0x583f47701d9b47bb3ef80fcabbbd62fbb58a01733b7a57e19658b4b02468883a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2523bfac005e21ef9963fdb3c08b2c61824e2b5ce2f53d1a1828b01ed995217c\",\"dweb:/ipfs/QmbBjVG9tZyeZSQH4m5GUzNBwo2iuvLoZYbmhT4gxnJc4J\"]},\"node_modules/solady/src/utils/UUPSUpgradeable.sol\":{\"keccak256\":\"0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16\",\"dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "AlreadyInitialized"}, {"inputs": [], "type": "error", "name": "FeeCollectorNotChanged"}, {"inputs": [], "type": "error", "name": "InsufficientBalance"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "NativeAssetMismatch"}, {"inputs": [], "type": "error", "name": "NewOwnerIsZeroAddress"}, {"inputs": [], "type": "error", "name": "NoHandoverRequest"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [], "type": "error", "name": "OnlyR<PERSON>erAllowed"}, {"inputs": [], "type": "error", "name": "OnlyVerifiedMarketsAllowed"}, {"inputs": [], "type": "error", "name": "ProtocolPaused"}, {"inputs": [], "type": "error", "name": "ProtocolStateNotChanged"}, {"inputs": [], "type": "error", "name": "Unauthorized"}, {"inputs": [], "type": "error", "name": "UnauthorizedCallContext"}, {"inputs": [], "type": "error", "name": "UpgradeFailed"}, {"inputs": [], "type": "error", "name": "ZeroAddressNotAllowed"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": false}, {"internalType": "address", "name": "token", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "newFeeCollector", "type": "address", "indexed": false}], "type": "event", "name": "FeeCollectorUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipHandoverCanceled", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipHandoverRequested", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "old<PERSON>wner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "bool", "name": "newState", "type": "bool", "indexed": false}], "type": "event", "name": "ProtocolStateUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address", "indexed": true}], "type": "event", "name": "Upgraded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": false}, {"internalType": "address", "name": "token", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "balances", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address[]", "name": "_tokens", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "batchWithdrawMaxTokens"}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "cancelOwnershipHandover"}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "stateMutability": "payable", "type": "function", "name": "completeOwnershipHandover"}, {"inputs": [{"internalType": "address", "name": "_assetA", "type": "address"}, {"internalType": "uint256", "name": "_feeA", "type": "uint256"}, {"internalType": "address", "name": "_assetB", "type": "address"}, {"internalType": "uint256", "name": "_feeB", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "creditFee"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}, {"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "bool", "name": "_useMargin", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "creditUser"}, {"inputs": [{"internalType": "bytes", "name": "_encodedData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "creditUsersEncoded"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}, {"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "debitUser"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}, {"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "deposit"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}, {"internalType": "address", "name": "_token", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "address", "name": "_router", "type": "address"}, {"internalType": "address", "name": "_feeCollector", "type": "address"}, {"internalType": "address", "name": "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "forwarder", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isTrusted<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "result", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "stateMutability": "view", "type": "function", "name": "ownershipHandoverExpiresAt", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "renounceOwnership"}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "requestOwnershipHandover"}, {"inputs": [{"internalType": "address", "name": "_feeCollector", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setFeeCollector"}, {"inputs": [{"internalType": "bool", "name": "_state", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "toggleProtocolState"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "payable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "address", "name": "_marketAddress", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "updateMarkets"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "upgradeToAndCall"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "verifiedMarket", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "address", "name": "_token", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw"}, {"inputs": [], "stateMutability": "payable", "type": "receive"}], "devdoc": {"kind": "dev", "methods": {"batchWithdrawMaxTokens(address[])": {"details": "This function allows a user to claim all of their tokens from the margin account.", "params": {"_tokens": "Array of tokens to claim."}}, "cancelOwnershipHandover()": {"details": "Cancels the two-step ownership handover to the caller, if any."}, "completeOwnershipHandover(address)": {"details": "Allows the owner to complete the two-step ownership handover to `pendingOwner`. Reverts if there is no existing ownership handover requested by `pendingOwner`."}, "creditFee(address,uint256,address,uint256)": {"details": "This function allows a verified market to register fee collected", "params": {"_assetA": "Address of first asset", "_assetB": "Address of second asset", "_feeA": "Fee amount of _assetA to be credited", "_feeB": "Fee amount of _assetB to be credited"}}, "creditUser(address,address,uint256,bool)": {"details": "This function is only callable by a verified market. Verified markets credit user balances.", "params": {"_amount": "amount to credit.", "_token": "address of token to be credit.", "_useMargin": "whether to use margin or not - if not, user will receive token through an erc20 transfer", "_user": "address of the user whose account should be credited."}}, "creditUsersEncoded(bytes)": {"details": "This function is only callable by a verified market. Verified markets credit a bunch of users the tokens.", "params": {"_encodedData": "Address of the user whose account should be credited."}}, "debitUser(address,address,uint256)": {"details": "This function is only callable by a verified market. Verified markets consume user balances.", "params": {"_amount": "amount to debit.", "_token": "address of token to be debited.", "_user": "address of the user whose account should be debited."}}, "deposit(address,address,uint256)": {"details": "Function for EOAs to deposit tokens on behalf of a user.", "params": {"_amount": "amount to credit.", "_token": "address of token to be credit.", "_user": "Address of the user whose account should be credited."}}, "getBalance(address,address)": {"details": "Function to check balance of a user for a given token.", "params": {"_token": "tokenA addresss", "_user": "user addresss"}}, "owner()": {"details": "Returns the owner of the contract."}, "ownershipHandoverExpiresAt(address)": {"details": "Returns the expiry timestamp for the two-step ownership handover to `pendingOwner`."}, "proxiableUUID()": {"details": "Returns the storage slot used by the implementation, as specified in [ERC1822](https://eips.ethereum.org/EIPS/eip-1822). Note: The `notDelegated` modifier prevents accidental upgrades to an implementation that is a proxy contract."}, "renounceOwnership()": {"details": "Allows the owner to renounce their ownership."}, "requestOwnershipHandover()": {"details": "Request a two-step ownership handover to the caller. The request will automatically expire in 48 hours (172800 seconds) by default."}, "transferOwnership(address)": {"details": "Allows the owner to transfer the ownership to `newOwner`."}, "updateMarkets(address)": {"details": "This function allows the router to register an official verified market.", "params": {"_marketAddress": "Address of the orderbook of the market."}}, "upgradeToAndCall(address,bytes)": {"details": "Upgrades the proxy's implementation to `newImplementation`. Emits a {Upgraded} event. Note: Passing in empty `data` skips the delegatecall to `newImplementation`."}, "withdraw(uint256,address)": {"details": "Function for users to withdraw their assets.", "params": {"_amount": "amount to withdraw.", "_token": "address of token to be withdrawn."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/MarginAccount.sol": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/MarginAccount.sol": {"keccak256": "0xb84f7d6424bcaa18060b459e607e79042dd445ab2dcbd7d9627ca5ca67ce5ace", "urls": ["bzz-raw://063e892e7ef061958cd2e2a91be36288e63a2c9f75c36cb3759d7cf7dfb979c9", "dweb:/ipfs/QmPFvYPyTuq4hz6R51Uf5VJtcQJzHGCamaGjybJRpfnpec"], "license": "BUSL-1.1"}, "contracts/interfaces/IMarginAccount.sol": {"keccak256": "0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2", "urls": ["bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4", "dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo"], "license": "GPL-2.0-or-later"}, "contracts/libraries/ERC2771Context.sol": {"keccak256": "0x7458e7a07eb42f479dd6a547733373f21d7a45e0d9a78d545e16db6639e61ef5", "urls": ["bzz-raw://8d278e5acd57e1943f2e411bcd6f4c13fb8620ae046cf8c8026fd9a103315497", "dweb:/ipfs/QmYWsYRhEV9A7sXgkFX6ES278EMN7b2rJ1JyetQ2XBQrWP"], "license": "MIT"}, "contracts/libraries/Errors.sol": {"keccak256": "0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f", "urls": ["bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7", "dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu"], "license": "BUSL-1.1"}, "node_modules/solady/src/auth/Ownable.sol": {"keccak256": "0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3", "urls": ["bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3", "dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs"], "license": "MIT"}, "node_modules/solady/src/utils/Initializable.sol": {"keccak256": "0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec", "urls": ["bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559", "dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy"], "license": "MIT"}, "node_modules/solady/src/utils/SafeTransferLib.sol": {"keccak256": "0x583f47701d9b47bb3ef80fcabbbd62fbb58a01733b7a57e19658b4b02468883a", "urls": ["bzz-raw://2523bfac005e21ef9963fdb3c08b2c61824e2b5ce2f53d1a1828b01ed995217c", "dweb:/ipfs/QmbBjVG9tZyeZSQH4m5GUzNBwo2iuvLoZYbmhT4gxnJc4J"], "license": "MIT"}, "node_modules/solady/src/utils/UUPSUpgradeable.sol": {"keccak256": "0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e", "urls": ["bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16", "dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV"], "license": "MIT"}}, "version": 1}, "id": 3}