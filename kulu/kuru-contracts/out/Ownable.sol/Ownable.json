{"abi": [{"type": "function", "name": "cancelOwnershipHandover", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "completeOwnershipHandover", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "result", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "ownershipHandoverExpiresAt", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "internalType": "address"}], "outputs": [{"name": "result", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "requestOwnershipHandover", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}, {"type": "event", "name": "OwnershipHandoverCanceled", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipHandoverRequested", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "old<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AlreadyInitialized", "inputs": []}, {"type": "error", "name": "NewOwnerIsZeroAddress", "inputs": []}, {"type": "error", "name": "NoHandoverRequest", "inputs": []}, {"type": "error", "name": "Unauthorized", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"cancelOwnershipHandover()": "54d1f13d", "completeOwnershipHandover(address)": "f04e283e", "owner()": "8da5cb5b", "ownershipHandoverExpiresAt(address)": "fee81cf4", "renounceOwnership()": "715018a6", "requestOwnershipHandover()": "25692962", "transferOwnership(address)": "f2fde38b"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"AlreadyInitialized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NewOwnerIsZeroAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NoHandoverRequest\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"OwnershipHandoverCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"OwnershipHandoverRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"cancelOwnershipHandover\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"completeOwnershipHandover\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"result\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"ownershipHandoverExpiresAt\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"result\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"requestOwnershipHandover\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"}],\"devdoc\":{\"author\":\"Solady (https://github.com/vectorized/solady/blob/main/src/auth/Ownable.sol)\",\"details\":\"Note: This implementation does NOT auto-initialize the owner to `msg.sender`. You MUST call the `_initializeOwner` in the constructor / initializer. While the ownable portion follows [EIP-173](https://eips.ethereum.org/EIPS/eip-173) for compatibility, the nomenclature for the 2-step ownership handover may be unique to this codebase.\",\"errors\":{\"AlreadyInitialized()\":[{\"details\":\"Cannot double-initialize.\"}],\"NewOwnerIsZeroAddress()\":[{\"details\":\"The `newOwner` cannot be the zero address.\"}],\"NoHandoverRequest()\":[{\"details\":\"The `pendingOwner` does not have a valid handover request.\"}],\"Unauthorized()\":[{\"details\":\"The caller is not authorized to call the function.\"}]},\"events\":{\"OwnershipHandoverCanceled(address)\":{\"details\":\"The ownership handover to `pendingOwner` has been canceled.\"},\"OwnershipHandoverRequested(address)\":{\"details\":\"An ownership handover to `pendingOwner` has been requested.\"},\"OwnershipTransferred(address,address)\":{\"details\":\"The ownership is transferred from `oldOwner` to `newOwner`. This event is intentionally kept the same as OpenZeppelin's Ownable to be compatible with indexers and [EIP-173](https://eips.ethereum.org/EIPS/eip-173), despite it not being as lightweight as a single argument event.\"}},\"kind\":\"dev\",\"methods\":{\"cancelOwnershipHandover()\":{\"details\":\"Cancels the two-step ownership handover to the caller, if any.\"},\"completeOwnershipHandover(address)\":{\"details\":\"Allows the owner to complete the two-step ownership handover to `pendingOwner`. Reverts if there is no existing ownership handover requested by `pendingOwner`.\"},\"owner()\":{\"details\":\"Returns the owner of the contract.\"},\"ownershipHandoverExpiresAt(address)\":{\"details\":\"Returns the expiry timestamp for the two-step ownership handover to `pendingOwner`.\"},\"renounceOwnership()\":{\"details\":\"Allows the owner to renounce their ownership.\"},\"requestOwnershipHandover()\":{\"details\":\"Request a two-step ownership handover to the caller. The request will automatically expire in 48 hours (172800 seconds) by default.\"},\"transferOwnership(address)\":{\"details\":\"Allows the owner to transfer the ownership to `newOwner`.\"}},\"stateVariables\":{\"_OWNERSHIP_HANDOVER_CANCELED_EVENT_SIGNATURE\":{\"details\":\"`keccak256(bytes(\\\"OwnershipHandoverCanceled(address)\\\"))`.\"},\"_OWNERSHIP_HANDOVER_REQUESTED_EVENT_SIGNATURE\":{\"details\":\"`keccak256(bytes(\\\"OwnershipHandoverRequested(address)\\\"))`.\"},\"_OWNERSHIP_TRANSFERRED_EVENT_SIGNATURE\":{\"details\":\"`keccak256(bytes(\\\"OwnershipTransferred(address,address)\\\"))`.\"},\"_OWNER_SLOT\":{\"details\":\"The owner slot is given by: `bytes32(~uint256(uint32(bytes4(keccak256(\\\"_OWNER_SLOT_NOT\\\")))))`. It is intentionally chosen to be a high value to avoid collision with lower slots. The choice of manual storage layout is to enable compatibility with both regular and upgradeable contracts.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"Simple single owner authorization mixin.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"node_modules/solady/src/auth/Ownable.sol\":\"Ownable\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"node_modules/solady/src/auth/Ownable.sol\":{\"keccak256\":\"0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3\",\"dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "AlreadyInitialized"}, {"inputs": [], "type": "error", "name": "NewOwnerIsZeroAddress"}, {"inputs": [], "type": "error", "name": "NoHandoverRequest"}, {"inputs": [], "type": "error", "name": "Unauthorized"}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipHandoverCanceled", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipHandoverRequested", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "old<PERSON>wner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "cancelOwnershipHandover"}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "stateMutability": "payable", "type": "function", "name": "completeOwnershipHandover"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "result", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "stateMutability": "view", "type": "function", "name": "ownershipHandoverExpiresAt", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}]}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "renounceOwnership"}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "requestOwnershipHandover"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "payable", "type": "function", "name": "transferOwnership"}], "devdoc": {"kind": "dev", "methods": {"cancelOwnershipHandover()": {"details": "Cancels the two-step ownership handover to the caller, if any."}, "completeOwnershipHandover(address)": {"details": "Allows the owner to complete the two-step ownership handover to `pendingOwner`. Reverts if there is no existing ownership handover requested by `pendingOwner`."}, "owner()": {"details": "Returns the owner of the contract."}, "ownershipHandoverExpiresAt(address)": {"details": "Returns the expiry timestamp for the two-step ownership handover to `pendingOwner`."}, "renounceOwnership()": {"details": "Allows the owner to renounce their ownership."}, "requestOwnershipHandover()": {"details": "Request a two-step ownership handover to the caller. The request will automatically expire in 48 hours (172800 seconds) by default."}, "transferOwnership(address)": {"details": "Allows the owner to transfer the ownership to `newOwner`."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"node_modules/solady/src/auth/Ownable.sol": "Ownable"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"node_modules/solady/src/auth/Ownable.sol": {"keccak256": "0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3", "urls": ["bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3", "dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs"], "license": "MIT"}}, "version": 1}, "id": 58}