{"abi": [{"type": "error", "name": "InvalidSignature", "inputs": []}], "bytecode": {"object": "0x6080806040523460175760399081601c823930815050f35b5f80fdfe5f80fdfea2646970667358221220b4fd249ae4fe3869b66ab3ebfd0909c72deb221b0ace1ff8f7f07965280079fd64736f6c634300081c0033", "sourceMap": "1453:16599:60:-:0;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x5f80fdfea2646970667358221220b4fd249ae4fe3869b66ab3ebfd0909c72deb221b0ace1ff8f7f07965280079fd64736f6c634300081c0033", "sourceMap": "1453:16599:60:-:0;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"InvalidSignature\",\"type\":\"error\"}],\"devdoc\":{\"author\":\"Solady (https://github.com/vectorized/solady/blob/main/src/utils/ECDSA.sol)Modified from Solmate (https://github.com/transmissions11/solmate/blob/main/src/utils/ECDSA.sol)Modified from OpenZeppelin (https://github.com/OpenZeppelin/openzeppelin-contracts/blob/master/contracts/utils/cryptography/ECDSA.sol)\",\"details\":\"Note: - The recovery functions use the ecrecover precompile (0x1). - As of Solady version 0.0.68, the `recover` variants will revert upon recovery failure.   This is for more safety by default.   Use the `tryRecover` variants if you need to get the zero address back   upon recovery failure instead. - As of Solady version 0.0.134, all `bytes signature` variants accept both   regular 65-byte `(r, s, v)` and EIP-2098 `(r, vs)` short form signatures.   See: https://eips.ethereum.org/EIPS/eip-2098   This is for calldata efficiency on smart accounts prevalent on L2s. WARNING! Do NOT use signatures as unique identifiers: - Use a nonce in the digest to prevent replay attacks on the same contract. - Use EIP-712 for the digest to prevent replay attacks across different chains and contracts.   EIP-712 also enables readable signing of typed data for better user safety. This implementation does NOT check if a signature is non-malleable.\",\"errors\":{\"InvalidSignature()\":[{\"details\":\"The signature is invalid.\"}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"Gas optimized ECDSA wrapper.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"node_modules/solady/src/utils/ECDSA.sol\":\"ECDSA\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"node_modules/solady/src/utils/ECDSA.sol\":{\"keccak256\":\"0x077d168511141c83fd93914f4609c5363341da03a3971cbc0d3f6a75e9893de0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c218864adb4e97d81908318d9bd0ed75f40ec996c826141fab2646135820295b\",\"dweb:/ipfs/QmVQyWG6Ff4LAUnzj6JMEHHYfNBANVYLaMkun6TYaWShms\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "InvalidSignature"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"node_modules/solady/src/utils/ECDSA.sol": "ECDSA"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"node_modules/solady/src/utils/ECDSA.sol": {"keccak256": "0x077d168511141c83fd93914f4609c5363341da03a3971cbc0d3f6a75e9893de0", "urls": ["bzz-raw://c218864adb4e97d81908318d9bd0ed75f40ec996c826141fab2646135820295b", "dweb:/ipfs/QmVQyWG6Ff4LAUnzj6JMEHHYfNBANVYLaMkun6TYaWShms"], "license": "MIT"}}, "version": 1}, "id": 60}