{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "TRUSTED_FORWARDER", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "anyToAnySwap", "inputs": [{"name": "_marketAddresses", "type": "address[]", "internalType": "address[]"}, {"name": "_isBuy", "type": "bool[]", "internalType": "bool[]"}, {"name": "_nativeSend", "type": "bool[]", "internalType": "bool[]"}, {"name": "_debitToken", "type": "address", "internalType": "address"}, {"name": "_creditToken", "type": "address", "internalType": "address"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}, {"name": "_minAmountOut", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "_amountOut", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "cancelOwnershipHandover", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "completeOwnershipHandover", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "computeAddress", "inputs": [{"name": "_baseAssetAddress", "type": "address", "internalType": "address"}, {"name": "_quote<PERSON><PERSON><PERSON>dd<PERSON>", "type": "address", "internalType": "address"}, {"name": "_sizePrecision", "type": "uint96", "internalType": "uint96"}, {"name": "_pricePrecision", "type": "uint32", "internalType": "uint32"}, {"name": "_tickSize", "type": "uint32", "internalType": "uint32"}, {"name": "_minSize", "type": "uint96", "internalType": "uint96"}, {"name": "_maxSize", "type": "uint96", "internalType": "uint96"}, {"name": "_takerFeeBps", "type": "uint256", "internalType": "uint256"}, {"name": "_makerFeeBps", "type": "uint256", "internalType": "uint256"}, {"name": "_kuruAmmSpread", "type": "uint96", "internalType": "uint96"}, {"name": "oldImplementation", "type": "address", "internalType": "address"}, {"name": "old", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "proxy", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "computeVaultAddress", "inputs": [{"name": "_marketAddress", "type": "address", "internalType": "address"}, {"name": "oldImplementation", "type": "address", "internalType": "address"}, {"name": "old", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "deployProxy", "inputs": [{"name": "_type", "type": "uint8", "internalType": "enum IOrderBook.OrderBookType"}, {"name": "_baseAssetAddress", "type": "address", "internalType": "address"}, {"name": "_quote<PERSON><PERSON><PERSON>dd<PERSON>", "type": "address", "internalType": "address"}, {"name": "_sizePrecision", "type": "uint96", "internalType": "uint96"}, {"name": "_pricePrecision", "type": "uint32", "internalType": "uint32"}, {"name": "_tickSize", "type": "uint32", "internalType": "uint32"}, {"name": "_minSize", "type": "uint96", "internalType": "uint96"}, {"name": "_maxSize", "type": "uint96", "internalType": "uint96"}, {"name": "_takerFeeBps", "type": "uint256", "internalType": "uint256"}, {"name": "_makerFeeBps", "type": "uint256", "internalType": "uint256"}, {"name": "_kuruAmmSpread", "type": "uint96", "internalType": "uint96"}], "outputs": [{"name": "proxy", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_owner", "type": "address", "internalType": "address"}, {"name": "_marginAccount", "type": "address", "internalType": "address"}, {"name": "_orderbookImplementation", "type": "address", "internalType": "address"}, {"name": "_kuruAmmVaultImplementation", "type": "address", "internalType": "address"}, {"name": "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "kuruAmmVaultImplementation", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "marginA<PERSON>unt<PERSON><PERSON><PERSON>", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "orderBookImplementation", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "result", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "ownershipHandoverExpiresAt", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "internalType": "address"}], "outputs": [{"name": "result", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "requestOwnershipHandover", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "toggleMarkets", "inputs": [{"name": "markets", "type": "address[]", "internalType": "address[]"}, {"name": "state", "type": "uint8", "internalType": "enum IOrderBook.MarketState"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "transferOwnershipForContracts", "inputs": [{"name": "contracts", "type": "address[]", "internalType": "address[]"}, {"name": "_newOwner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeMultipleOrderBookProxies", "inputs": [{"name": "proxies", "type": "address[]", "internalType": "address[]"}, {"name": "data", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeMultipleVaultProxies", "inputs": [{"name": "proxies", "type": "address[]", "internalType": "address[]"}, {"name": "data", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeOrderBookImplementation", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "upgradeVaultImplementation", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "verifiedMarket", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "pricePrecision", "type": "uint32", "internalType": "uint32"}, {"name": "sizePrecision", "type": "uint96", "internalType": "uint96"}, {"name": "baseAssetAddress", "type": "address", "internalType": "address"}, {"name": "baseAssetDecimals", "type": "uint256", "internalType": "uint256"}, {"name": "quote<PERSON>set<PERSON>dd<PERSON>", "type": "address", "internalType": "address"}, {"name": "quoteAssetDecimals", "type": "uint256", "internalType": "uint256"}, {"name": "tickSize", "type": "uint32", "internalType": "uint32"}, {"name": "minSize", "type": "uint96", "internalType": "uint96"}, {"name": "maxSize", "type": "uint96", "internalType": "uint96"}, {"name": "takerFeeBps", "type": "uint256", "internalType": "uint256"}, {"name": "makerFeeBps", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "KuruRouterSwap", "inputs": [{"name": "msgSender", "type": "address", "indexed": false, "internalType": "address"}, {"name": "debitToken", "type": "address", "indexed": false, "internalType": "address"}, {"name": "creditToken", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "MarketRegistered", "inputs": [{"name": "baseAsset", "type": "address", "indexed": false, "internalType": "address"}, {"name": "quoteAsset", "type": "address", "indexed": false, "internalType": "address"}, {"name": "market", "type": "address", "indexed": false, "internalType": "address"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": false, "internalType": "address"}, {"name": "pricePrecision", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "sizePrecision", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "tickSize", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "minSize", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "maxSize", "type": "uint96", "indexed": false, "internalType": "uint96"}, {"name": "takerFeeBps", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "makerFeeBps", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "kuruAmmSpread", "type": "uint96", "indexed": false, "internalType": "uint96"}], "anonymous": false}, {"type": "event", "name": "OBImplementationUpdated", "inputs": [{"name": "previousImplementation", "type": "address", "indexed": false, "internalType": "address"}, {"name": "newImplementation", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipHandoverCanceled", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipHandoverRequested", "inputs": [{"name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "old<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "VaultImplementationUpdated", "inputs": [{"name": "previousImplementation", "type": "address", "indexed": false, "internalType": "address"}, {"name": "newImplementation", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AlreadyInitialized", "inputs": []}, {"type": "error", "name": "BaseAndQuoteAssetSame", "inputs": []}, {"type": "error", "name": "Create2EmptyBytecode", "inputs": []}, {"type": "error", "name": "FailedDeployment", "inputs": []}, {"type": "error", "name": "InsufficientBalance", "inputs": [{"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidMarket", "inputs": []}, {"type": "error", "name": "InvalidPricePrecision", "inputs": []}, {"type": "error", "name": "InvalidSizePrecision", "inputs": []}, {"type": "error", "name": "InvalidTickSize", "inputs": []}, {"type": "error", "name": "LengthMismatch", "inputs": []}, {"type": "error", "name": "MarketTypeMismatch", "inputs": []}, {"type": "error", "name": "NewOwnerIsZeroAddress", "inputs": []}, {"type": "error", "name": "NoHandoverRequest", "inputs": []}, {"type": "error", "name": "NoMarketsPassed", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "SlippageExceeded", "inputs": []}, {"type": "error", "name": "Uint96Overflow", "inputs": []}, {"type": "error", "name": "Unauthorized", "inputs": []}, {"type": "error", "name": "UnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UpgradeFailed", "inputs": []}], "bytecode": {"object": "0x60a080604052346099573060805263409feecd195460018116608c5760011c6002600160401b0319016048575b6124bd908161009e823960805181818161165901526117080152f35b6002600160411b0363409feecd19556001600160401b0360209081527fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d29080a1602c565b63f92ee8a95f526004601cfd5b5f80fdfe6080604052600436101561001a575b3615610018575f80fd5b005b5f3560e01c80631444ccb614611a605780631459457a146118ed57806317e4c4d914611864578063********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", "sourceMap": "1082:15797:5:-:0;;;;;;;1525:4:64;1501:31;;-1:-1:-1;;6669:609:62;;;;;;2001:66;6669:609;-1:-1:-1;;;;;;6669:609:62;;;-1:-1:-1;1082:15797:5;;;;;;1501:31:64;1082:15797:5;;;;;;;;;;;6669:609:62;-1:-1:-1;;;;;;;6669:609:62;-1:-1:-1;;;;;6669:609:62;;;;;;;;;;;;-1:-1:-1;6669:609:62;;;;1082:15797:5;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x6080604052600436101561001a575b3615610018575f80fd5b005b5f3560e01c80631444ccb614611a605780631459457a146118ed57806317e4c4d914611864578063********1461181b578063483100bd146117f35780634f1ef286146116b457806352d1902d1461164657806354d1f13d146116025780635f2b7a3c146115495780635f71a07c1461147e578063715018a61461143157806372cfb2ca1461138d5780637b4372fd1461126a5780638da5cb5b1461123e578063a041649914611216578063a574b091146111ee578063af835119146111c6578063c8a296aa14611122578063ca0dcb8b1461106a578063ce186ec314610754578063dd874d0a146106cb578063f04e283e1461067e578063f2fde38b14610643578063fee81cf4146106115763ffa5210a0361000e5760e03660031901126104ac576004356001600160401b0381116104ac5761015c903690600401611df0565b906024356001600160401b0381116104ac5761017c903690600401611df0565b906044356001600160401b0381116104ac5761019c903690600401611df0565b9190936101a7611bbd565b926101b0611bd3565b9560a43595865f9960018110610602578083036105f3578085036105f35784156105df576101dd84611ed9565b15610590575b9594939291905f905b8782106102d4578b8b8b8b60c43584106102c557604080513381526001600160a01b0392831660208201529184169082018190526060820192909252608081018490527fae71e8ae9695e4f3523d27453a24d99edc4738fea8130c1cb33eb9ef95f533549060a090a1156102a2575f6044601082602094336014528660345263a9059cbb60601b82525af13d1560015f5114171615610295576020905f6034525b604051908152f35b6390b8ec185f526004601cfd5b505f38818084335af1156102b85760209061028d565b63b12d13eb5f526004601cfd5b638199f5f360e01b5f5260045ffd5b909192939495969a506102e8828c88611ec9565b356001600160a01b03811691908290036104ac57815f525f60205260405f206040519261031484611c12565b81549263ffffffff8416948581526001600160601b03602082019560201c16855260018060a01b0360018501541660408201526002840154956060820196875260018060a01b036003860154166080830152600760048601549560a084019687526001600160601b03600582015463ffffffff811660c0870152818160201c1660e087015260801c166101008501526006810154610120850152015461014083015215610581578b956020956103d36103ce8a8e8e611ec9565b611ed9565b15610572576103e76103ce8a8c879b611ec9565b156104c357505061045f9361041461040d61041f9463ffffffff61041a95511690611ee6565b9151611e8e565b90611ef9565b612188565b604051637c51d6cf60e01b81526001600160601b0390911660048201525f6024820181905260448201526001606482015293849283919082906084820190565b03925af19081156104b8575f91610483575b50995b9594939291906001018a6101ec565b90506020813d82116104b0575b8161049d60209383611c42565b810103126104ac57515f610471565b5f80fd5b3d9150610490565b6040513d5f823e3d90fd5b610528955061041a925061040d6104e8946001600160601b0361041493511690611ee6565b60405163532c46db60e01b81526001600160601b0390911660048201525f6024820181905260448201526001606482015293849283919082906084820190565b03925af19081156104b8575f91610541575b5099610474565b90506020813d821161056a575b8161055b60209383611c42565b810103126104ac57515f61053a565b3d915061054e565b6103e76103ce8a8c5f9b611ec9565b639db8d5b160e01b5f5260045ffd5b60405182606052306040523360601b602c526323b872dd60601b600c5260205f6064601c828d5af13d1560015f51141716156105d2575f6060526040526101e3565b637939f4245f526004601cfd5b634e487b7160e01b5f52603260045260245ffd5b631fec674760e31b5f5260045ffd5b63684d939f60e11b5f5260045ffd5b346104ac5760203660031901126104ac5761062a611b7b565b63389a75e1600c525f52602080600c2054604051908152f35b60203660031901126104ac57610657611b7b565b61065f611fe4565b8060601b156106715761001890612000565b637448fbae5f526004601cfd5b60203660031901126104ac57610692611b7b565b61069a611fe4565b63389a75e1600c52805f526020600c2090815442116106be575f6100189255612000565b636f5e88185f526004601cfd5b346104ac5760203660031901126104ac576106e4611b7b565b6106ec611fe4565b6002546001600160a01b0382811692908216908382146104ac57604080516001600160a01b0393841681529190921660208201527fbe445c2eda0edb9bcbd5caff10d1795a2bebcf2e3b44015a59df6bc777d010329190a16001600160a01b03191617600255005b346104ac576101603660031901126104ac57600360043510156104ac57610779611b91565b610781611ba7565b90606435906001600160601b03821682036104ac5761079e611bff565b9160a43563ffffffff811681036104ac576107b7611be9565b60e435916001600160601b03831683036104ac5761014435936001600160601b03851685036104ac576107eb600435611e70565b600435600103610ff3576001600160a01b038616610fe4576001600160a01b03881615610fe4575b63ffffffff821615610fd5576001600160601b03811661083a61083582612043565b611e8e565b03610fc65763ffffffff871661085261083582612043565b03610fb757610862600435611e70565b600435600103610f64576012965b61087b600435611e70565b600435600203610f025760125b8787610124358c898989888a61010435956108a299611f17565b6040519a906108b6602061029d018d611c42565b61029d808d526121eb60208e0139600160a01b600190036002541660209c8d6040516108e28282611c42565b5f81526040518094838201926108f89184611e20565b03601f198101855261090a9085611c42565b604051938385945160208192018587015e840190838201905f8252519283915e01015f815203601f19810182526109419082611c42565b61094a9161213e565b604080516001600160a01b038316818f01528d8152919b918d9161096e9082611c42565b805190820120604051610986602061029d0182611c42565b61029d81528281019261029d6121eb8539600454604051906001600160a01b03166109b18383611c42565b5f82526040518091848201936109c79185611e20565b03601f19810182526109d99082611c42565b6040519586945180918587015e840190838201905f8252519283915e01015f815203601f1981018352610a0c9083611c42565b6001600160a01b0391610a1e9161213e565b16928c8c604051610a2e81611c12565b63ffffffff881681526001600160601b038916838201528d600160a01b6001900316604082015260ff85166060820152600160a01b600190038616608082015260ff841660a082015263ffffffff8a1660c08201526001600160601b038b1660e08201526101008101906001600160601b038d168252610120810191610104358352610140820193610124358552600160a01b60019003165f525f855260405f2094825163ffffffff1663ffffffff1663ffffffff198754161786558201516001600160601b0316610b23908690640100000000600160801b0382549160201b1690640100000000600160801b031916179055565b60408201516001860180546001600160a01b039283166001600160a01b03199182161790915560608401516002880155608084015160038801805491909316911617905560a0820151600486015560c082015160058601805460e090940151640100000000600160801b0360209190911b1663ffffffff9092166fffffffffffffffffffffffffffffffff19909416939093171782555181546bffffffffffffffffffffffff60801b191660809190911b6bffffffffffffffffffffffff60801b16179055516006830155516007909101556003546001600160a01b0316803b156104ac576040516301aef34960e01b81526001600160a01b038e166004820152905f908290602490829084905af180156104b857610ef2575b506003546001546001600160a01b039081169291811691908e163b156104ac5760405193633209ff7360e21b8552306004860152600435610c7d90611e70565b60043560248601528d600160a01b6001900316604486015260ff166064850152600160a01b600190038516608485015260ff1660a484015260c48301526001600160601b03861660e483015263ffffffff851661010483015263ffffffff87166101248301526001600160601b0388166101448301526001600160601b03891661016483015261010435610184830152610124356101a4830152836101c48301526001600160601b038a166101e48301526102048201528080600160a01b600190038d165a925f610224928195f180156104b857610ee2575b506003546001600160a01b0316823b156104ac57604051636923670b60e11b81523060048201526001600160a01b038b8116602483015283811660448301526064820192909252908b1660848201526001600160601b03891660a48201525f8160c48183875af180156104b857610ed2575b50610dd4600435611e70565b600435600103610e9a57610de88a826121ab565b604080516001600160a01b039a8b168152918a168c830152988a1698810198909852606088015263ffffffff90811660808801526001600160601b0391821660a08801529190911660c086015290811660e08501529081166101008401526101043561012084015261012435610140840152166101608201527f8fd547930d9019b964dccbf749798118018ee4fe341a4b2f970bf28797842eab9061018090a16040516001600160a01b039091168152f35b610ea5600435611e70565b600435600203610ebe57610eb98a8a6121ab565b610de8565b610ec88a8a6121ab565b610eb98a826121ab565b5f610edc91611c42565b8b610dc8565b5f610eec91611c42565b8b610d56565b5f610efc91611c42565b8d610c3d565b60405163313ce56760e01b81526020816004816001600160a01b038e165afa9081156104b8575f91610f35575b50610888565b610f57915060203d602011610f5d575b610f4f8183611c42565b810190611eb0565b8a610f2f565b503d610f45565b60405163313ce56760e01b81526020816004816001600160a01b038b165afa9081156104b8575f91610f98575b5096610870565b610fb1915060203d602011610f5d57610f4f8183611c42565b89610f91565b63a7bee35960e01b5f5260045ffd5b635403126b60e11b5f5260045ffd5b63747a60fb60e01b5f5260045ffd5b635eb44c5f60e11b5f5260045ffd5b610ffe600435611e70565b600435600203611035576001600160a01b03861615610fe4576001600160a01b0388161561081357635eb44c5f60e11b5f5260045ffd5b6001600160a01b0386168015610fe4576001600160a01b038916908115610fe4570361081357630261cf2f60e31b5f5260045ffd5b346104ac5760403660031901126104ac576004356001600160401b0381116104ac5761109a903690600401611c7a565b6110a2611b91565b906110ab611fe4565b6001600160a01b03909116905f5b8151811015610018576001600160a01b036110d48284611e5c565b511690813b156104ac575f809260246040518095819363f2fde38b60e01b83528960048401525af19182156104b857600192611112575b50016110b9565b5f61111c91611c42565b8461110b565b346104ac5761113036611ce8565b90611139611fe4565b5f5b8151811015610018576001600160a01b036111568284611e5c565b51600454911691906001600160a01b03166111718286611e5c565b51833b156104ac5761119c935f928360405180978195829463278f794360e11b845260048401611e20565b03925af19182156104b8576001926111b6575b500161113b565b5f6111c091611c42565b846111af565b346104ac575f3660031901126104ac576001546040516001600160a01b039091168152602090f35b346104ac575f3660031901126104ac576004546040516001600160a01b039091168152602090f35b346104ac575f3660031901126104ac576002546040516001600160a01b039091168152602090f35b346104ac575f3660031901126104ac57638b78c6d819546040516001600160a01b039091168152602090f35b346104ac5760603660031901126104ac57611283611b7b565b61128b611b91565b9060443580151581036104ac5761136091604051602081019160018060a01b03168252602081526112bd604082611c42565b5190209061029d93604051916112d66020870184611c42565b85835260208301956121eb8739156113725761135761131891955b602080976113266040516113058482611c42565b5f81526040519687918583019485611e20565b03601f198101875286611c42565b60405194859383850197518091895e840190838201905f8252519283915e01015f815203601f198101835282611c42565b51902090611fba565b6040516001600160a01b039091168152f35b506004546001600160a01b03169361131890611357906112f1565b346104ac5761139b36611ce8565b906113a4611fe4565b5f5b8151811015610018576001600160a01b036113c18284611e5c565b51600254911691906001600160a01b03166113dc8286611e5c565b51833b156104ac57611407935f928360405180978195829463278f794360e11b845260048401611e20565b03925af19182156104b857600192611421575b50016113a6565b5f61142b91611c42565b8461141a565b5f3660031901126104ac57611444611fe4565b5f638b78c6d819547f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08280a3600160ff1b638b78c6d81955005b346104ac5760203660031901126104ac576001600160a01b0361149f611b7b565b165f525f60205261016060405f2080549060018060a01b03600182015416906001600160601b03600282015460018060a01b036003840154166004840154906005850154926007600687015496015496856040519963ffffffff81168b5260201c1660208a015260408901526060880152608087015260a086015263ffffffff811660c0860152818160201c1660e086015260801c16610100840152610120830152610140820152f35b346104ac5760403660031901126104ac576004356001600160401b0381116104ac57611579903690600401611c7a565b6024359060038210156104ac5761158e611fe4565b5f5b8151811015610018576001600160a01b036115ab8284611e5c565b511690813b156104ac575f80926024604051809581936339914ae560e11b83526115d48a611e70565b8960048401525af19182156104b8576001926115f2575b5001611590565b5f6115fc91611c42565b846115eb565b5f3660031901126104ac5763389a75e1600c52335f525f6020600c2055337ffa7b8eab7da67f412cc9575ed43464468f9bfbae89d1675917346ca6d8fe3c925f80a2005b346104ac575f3660031901126104ac57307f0000000000000000000000000000000000000000000000000000000000000000036116a75760206040517f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc8152f35b639f03a0265f526004601cfd5b60403660031901126104ac576116c8611b7b565b602435906001600160401b0382116104ac57366023830112156104ac578160040135906001600160401b0382116104ac5736602483850101116104ac57307f0000000000000000000000000000000000000000000000000000000000000000146116a757611734611fe4565b60018060a01b03166352d1902d6001527f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc602060016004601d855afa51036117e557807fbc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3b5f80a281817f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc556117c557005b815f926024604051950185378338925af4156117dd57005b3d5f823e3d90fd5b6355299b496001526004601dfd5b346104ac575f3660031901126104ac576003546040516001600160a01b039091168152602090f35b5f3660031901126104ac5763389a75e1600c52335f526202a30042016020600c2055337fdbf36a107da19e49527a7176a1babf963b4b0ff8cde35ee35d6cd8f1f9ac7e1d5f80a2005b346104ac5760203660031901126104ac5761187d611b7b565b611885611fe4565b6004546001600160a01b0382811692908216908382146104ac57604080516001600160a01b0393841681529190921660208201527f259630c8639e94038db3e075abf4bbdd97f6a4c6bee54e2ea6198ced50e48c299190a16001600160a01b03191617600455005b346104ac5760a03660031901126104ac57611906611b7b565b61190e611b91565b90611917611ba7565b61191f611bbd565b611927611bd3565b9163409feecd199485548060038855611a37575b50638b78c6d81954611a2a576001600160a01b03909416801560ff1b8117638b78c6d819558594905f7f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08180a360018060a01b03166001600160601b0360a01b600354161760035560018060a01b03166001600160601b0360a01b600254161760025560018060a01b03166001600160601b0360a01b600454161760045560018060a01b03166001600160601b0360a01b60015416176001556119fa57005b6002905560016020527fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d2602080a1005b630dc149f05f526004601cfd5b600181819893981c14303b1015611a535760ff1b1b948661193b565b63f92ee8a95f526004601cfd5b346104ac576101803660031901126104ac57611a7a611b7b565b611a82611b91565b90604435906001600160601b03821682036104ac576064359163ffffffff831683036104ac57611ab0611bff565b9160a435946001600160601b03861686036104ac57611acd611be9565b9161012435926001600160601b03841684036104ac5761014435976001600160a01b03891689036104ac57610164359687151588036104ac5761136098611b1c97610104359660e43596611f17565b9061029d9360405191611b326020870184611c42565b85835260208301956121eb873915611b60576113576113189195602080976113266040516113058482611c42565b506002546001600160a01b03169361131890611357906112f1565b600435906001600160a01b03821682036104ac57565b602435906001600160a01b03821682036104ac57565b604435906001600160a01b03821682036104ac57565b606435906001600160a01b03821682036104ac57565b608435906001600160a01b03821682036104ac57565b60c435906001600160601b03821682036104ac57565b6084359063ffffffff821682036104ac57565b61016081019081106001600160401b03821117611c2e57604052565b634e487b7160e01b5f52604160045260245ffd5b90601f801991011681019081106001600160401b03821117611c2e57604052565b6001600160401b038111611c2e5760051b60200190565b9080601f830112156104ac57813590611c9282611c63565b92611ca06040519485611c42565b82845260208085019360051b8201019182116104ac57602001915b818310611cc85750505090565b82356001600160a01b03811681036104ac57815260209283019201611cbb565b9060406003198301126104ac576004356001600160401b0381116104ac5782611d1391600401611c7a565b91602435906001600160401b0382116104ac57806023830112156104ac57816004013591611d4083611c63565b92611d4e6040519485611c42565b8084526024602085019160051b830101918383116104ac5760248101915b838310611d7b57505050505090565b82356001600160401b0381116104ac578201856043820112156104ac576024810135916001600160401b038311611c2e57604051611dc3601f8501601f191660200182611c42565b83815260448385010188106104ac575f602085819660448397018386013783010152815201920191611d6c565b9181601f840112156104ac578235916001600160401b0383116104ac576020808501948460051b0101116104ac57565b6001600160a01b0390911681526040602080830182905283519183018290526060938291018484015e5f828201840152601f01601f1916010190565b80518210156105df5760209160051b010190565b60031115611e7a57565b634e487b7160e01b5f52602160045260245ffd5b604d8111611e9c57600a0a90565b634e487b7160e01b5f52601160045260245ffd5b908160209103126104ac575160ff811681036104ac5790565b91908110156105df5760051b0190565b3580151581036104ac5790565b81810292918115918404141715611e9c57565b8115611f03570490565b634e487b7160e01b5f52601260045260245ffd5b6040516bffffffffffffffffffffffff19606092831b8116602083019081529390921b90911660348201526001600160a01b031960a093841b811660488301526001600160e01b031960e095861b811660548401529590941b909416605885015293811b8216605c84015293841b811660688301526074820194909452609481019490945293811b90911660b48301528152611fb460c082611c42565b51902090565b90600b9160405191604083015260208201523081520160ff8153605590206001600160a01b031690565b638b78c6d819543303611ff357565b6382b429005f526004601cfd5b60018060a01b031680638b78c6d819547f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e05f80a3801560ff1b17638b78c6d81955565b5f9072184f03e93ff9f4daa797ed6e38ed64bf6a1f0160401b81101561211c575b806d04ee2d6d415b85acef8100000000600a921015612101575b662386f26fc100008110156120ed575b6305f5e1008110156120dc575b6127108110156120cd575b60648110156120bf575b10156120b95790565b60010190565b6064600291049201916120b0565b612710600491049201916120a6565b6305f5e1006008910492019161209b565b662386f26fc100006010910492019161208e565b6d04ee2d6d415b85acef81000000006020910492019161207e565b6040915072184f03e93ff9f4daa797ed6e38ed64bf6a1f0160401b9004612064565b90805115612179576020815191015ff5903d15198215166104b8576001600160a01b0382161561216a57565b63b06ebf3d60e01b5f5260045ffd5b631328927760e21b5f5260045ffd5b906001600160601b03821691820361219c57565b637119f00960e11b5f5260045ffd5b604460105f8093602095601452811960345263095ea7b360601b82525af13d1560015f51141716156121dd575f603452565b633e3f8f735f526004601cfdfe608060405261029d8038038061001481610168565b92833981016040828203126101645781516001600160a01b03811692909190838303610164576020810151906001600160401b03821161016457019281601f8501121561016457835161006e610069826101a1565b610168565b9481865260208601936020838301011161016457815f926020809301865e86010152823b15610152577f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc80546001600160a01b031916821790557fbc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3b5f80a282511561013a575f8091610122945190845af43d15610132573d91610113610069846101a1565b9283523d5f602085013e6101bc565b505b6040516082908161021b8239f35b6060916101bc565b50505034156101245763b398979f60e01b5f5260045ffd5b634c9c8ce360e01b5f5260045260245ffd5b5f80fd5b6040519190601f01601f191682016001600160401b0381118382101761018d57604052565b634e487b7160e01b5f52604160045260245ffd5b6001600160401b03811161018d57601f01601f191660200190565b906101e057508051156101d157602081519101fd5b63d6bda27560e01b5f5260045ffd5b81511580610211575b6101f1575090565b639996b31560e01b5f9081526001600160a01b0391909116600452602490fd5b50803b156101e956fe60806040527f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc545f9081906001600160a01b0316368280378136915af43d5f803e156048573d5ff35b3d5ffdfea26469706673582212203e6cdd77f38134cb22407c0ed516e59fee35a8bf35428e8e0f574c7b0909678a64736f6c634300081c0033a26469706673582212202bac7616df64f6ec54532cd5a89fcb9857b58243e5e345e6b46910d69303770364736f6c634300081c0033", "sourceMap": "1082:15797:5:-:0;;;;;;;;;-1:-1:-1;1082:15797:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1082:15797:5;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;:::i;:::-;;;;;;:::i;:::-;;;;:::i;:::-;;;;;;;13740:28;1082:15797;13740:28;;1082:15797;;13819:40;;;1082:15797;;13909:45;;;1082:15797;;;;;;14000:14;;;:::i;:::-;1082:15797;13996:118;;1082:15797;14172:13;;;;;;1082:15797;14167:1011;14187:27;;;;;;1082:15797;;;;;;15195:27;;1082:15797;;;;;15286:10;1082:15797;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;15271:82;;1082:15797;;15271:82;15367:26;1082:15797;;;;13593:806:63;15286:10:5;1082:15797;15286:10;;13593:806:63;;;;;;;;;;;;;;1082:15797:5;;13593:806:63;;;;;;;1082:15797:5;13593:806:63;1082:15797:5;13593:806:63;;15363:175:5;1082:15797;;;;;;13593:806:63;;1082:15797:5;13593:806:63;1082:15797:5;13593:806:63;;15363:175:5;4146:221:63;1082:15797:5;4146:221:63;15286:10:5;;;;4146:221:63;;;;;1082:15797:5;15363:175;;;4146:221:63;;1082:15797:5;4146:221:63;1082:15797:5;4146:221:63;;1082:15797:5;;;;;;;;;14216:3;14260:19;;;;;;;;;;;;;;:::i;:::-;1082:15797;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;14381:32;1082:15797;;14535:14;;1082:15797;14535:14;;;;;;;:::i;:::-;;:::i;:::-;:28;;;14590:9;;14535:28;;;;14590:9;:::i;:::-;:542;;;1082:15797;;14640:268;1082:15797;14759:38;14718;14712:86;1082:15797;;14718:79;1082:15797;;;14718:38;;:::i;:::-;1082:15797;;14759:38;:::i;:::-;14718:79;;:::i;:::-;14712:86;:::i;:::-;1082:15797;;-1:-1:-1;;;14640:268:5;;-1:-1:-1;;;;;1082:15797:5;;;;14640:268;;1082:15797;-1:-1:-1;1082:15797:5;;;;;;;;;;13767:1;1082:15797;;;;;;;;;;;;;;;;;14640:268;;;;;;;;;;1082:15797;14640:268;;;14590:542;;;;14172:13;;;;;;1082:15797;;14577:555;14172:13;;14640:268;;;1082:15797;14640:268;;;;;;;;;1082:15797;14640:268;;;:::i;:::-;;;1082:15797;;;;;14640:268;;;1082:15797;;;;14640:268;;;-1:-1:-1;14640:268:5;;;1082:15797;;;;;;;;;14590:542;14945:187;1082:15797;;15020:77;1082:15797;;15020:37;15014:84;1082:15797;-1:-1:-1;;;;;15060:37:5;1082:15797;;;15020:37;;:::i;15014:84::-;1082:15797;;-1:-1:-1;;;14945:187:5;;-1:-1:-1;;;;;1082:15797:5;;;;14945:187;;1082:15797;-1:-1:-1;1082:15797:5;;;;;;;;;;13767:1;1082:15797;;;;;;;;;;;;;;;;;14945:187;;;;;;;;;;1082:15797;14945:187;;;14590:542;;;;;14945:187;;;1082:15797;14945:187;;;;;;;;;1082:15797;14945:187;;;:::i;:::-;;;1082:15797;;;;;14945:187;;;;;;-1:-1:-1;14945:187:5;;14535:28;14590:9;;14535:28;;1082:15797;14535:28;14590:9;:::i;1082:15797::-;;;;;;;;;13996:118;1082:15797;9254:988:63;;1082:15797:5;9254:988:63;14088:4:5;1082:15797;9254:988:63;14068:10:5;1082:15797;9254:988:63;;;;;;;;1082:15797:5;;;9254:988:63;;;;;;;1082:15797:5;;9254:988:63;;;;;;;1082:15797:5;;9254:988:63;1082:15797:5;9254:988:63;13996:118:5;;9254:988:63;;1082:15797:5;9254:988:63;1082:15797:5;9254:988:63;;1082:15797:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1082:15797:5;;;;;;:::i;:::-;11885:237:58;;;1082:15797:5;11885:237:58;1082:15797:5;11885:237:58;;;;1082:15797:5;;;;;;;;;-1:-1:-1;;1082:15797:5;;;;;;:::i;:::-;12478:70:58;;:::i;:::-;8479:183;;;;;;8681:8;;;:::i;8479:183::-;;1082:15797:5;8479:183:58;1082:15797:5;8479:183:58;;1082:15797:5;;;-1:-1:-1;;1082:15797:5;;;;;;:::i;:::-;12478:70:58;;:::i;:::-;10506:526;;;;1082:15797:5;10506:526:58;1082:15797:5;10506:526:58;;;;;;;;;1082:15797:5;11051:12:58;10506:526;;11051:12;:::i;10506:526::-;;1082:15797:5;10506:526:58;1082:15797:5;10506:526:58;;1082:15797:5;;;;;;-1:-1:-1;;1082:15797:5;;;;;;:::i;:::-;12478:70:58;;:::i;:::-;9677:23:5;1082:15797;-1:-1:-1;;;;;1082:15797:5;;;;;;;;9656:44;;;1082:15797;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;9716:67;;1082:15797;9716:67;-1:-1:-1;;;;;;1082:15797:5;;9677:23;1082:15797;;;;;;;;-1:-1:-1;;1082:15797:5;;;;;;;;;;;;;:::i;:::-;;;:::i;:::-;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;:::i;:::-;;;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;:::i;:::-;;;;3855:48;1082:15797;;-1:-1:-1;;;;;1082:15797:5;;;;-1:-1:-1;;;;;1082:15797:5;;4016:32;1082:15797;;3851:791;1082:15797;;;4659:13;1082:15797;;-1:-1:-1;;;;;1082:15797:5;;4723:32;4729:26;1082:15797;4729:26;:::i;:::-;4723:32;:::i;:::-;:50;1082:15797;;;;;4829:33;4835:27;1082:15797;4835:27;:::i;4829:33::-;:52;1082:15797;;;;;;:::i;:::-;;;;3855:48;1082:15797;;5022:2;4971:100;;1082:15797;;;;:::i;:::-;;;5132:40;5123:49;5132:40;;5175:2;5123:102;1082:15797;;;;;;;;;;;;5265:329;;;;:::i;:::-;1082:15797;;;;;;5707:31;;1082:15797;;:::i;:::-;5707:31;;;;;1082:15797;5707:31;;;1082:15797;;;;;;5132:40;1082:15797;;;;;;;;;;;:::i;:::-;;;;;;5740:46;;;;;;;;;;:::i;:::-;;8714:72;;5740:46;;;;;;;;:::i;:::-;1082:15797;;;;;;;;5707:31;;;5690:97;;;1082:15797;;;;;;;;;;;;;;;;;;;;;5690:97;8714:72;;5690:97;;;;;;;;:::i;:::-;5616:185;;;:::i;:::-;1082:15797;;;-1:-1:-1;;;;;1082:15797:5;;5939:17;;;1082:15797;5939:17;;;1082:15797;;;;;5939:17;;1082:15797;5939:17;:::i;:::-;1082:15797;;5939:17;;;5929:28;1082:15797;;;;5707:31;;1082:15797;;:::i;:::-;5707:31;5992;;;;;5707;;;5992;;1082:15797;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;:::i;:::-;;;;;;6025:49;;;;;;;;;;:::i;:::-;;8714:72;;6025:49;;;;;;;;:::i;:::-;1082:15797;;;;;;5975:100;;;;;1082:15797;;;;;;;;;;;;;;;;;;;;;5975:100;8714:72;;5975:100;;;;;;;;:::i;:::-;-1:-1:-1;;;;;1082:15797:5;5878:211;;;:::i;:::-;1082:15797;;;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;;;;1082:15797:5;;6134:326;;;1082:15797;;;;;;;;;;6134:326;;1082:15797;;;;;6134:326;;1082:15797;;;;;;;;;;6134:326;;1082:15797;;;;;6134:326;;1082:15797;;;;;6134:326;;1082:15797;-1:-1:-1;;;;;1082:15797:5;;;6134:326;;1082:15797;;6134:326;;1082:15797;-1:-1:-1;;;;;1082:15797:5;;;;;6134:326;;1082:15797;;;;;;6134:326;;1082:15797;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6134:326;;1082:15797;-1:-1:-1;;;;;1082:15797:5;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;6134:326;;1082:15797;;;;;;-1:-1:-1;;;;;1082:15797:5;;;-1:-1:-1;;;;;;1082:15797:5;;;;;;;;6134:326;;1082:15797;5132:40;1082:15797;;;;6134:326;;1082:15797;;;;;;;;;;;;;;;;6134:326;;1082:15797;;;;;;6134:326;;1082:15797;;;;;;;6134:326;;;1082:15797;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;-1:-1:-1;;1082:15797:5;;;;;;;;;;;;;-1:-1:-1;;;;1082:15797:5;;;;;;-1:-1:-1;;;1082:15797:5;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1082:15797:5;6470:57;;;;;1082:15797;;-1:-1:-1;;;6470:57:5;;-1:-1:-1;;;;;1082:15797:5;;;6470:57;;1082:15797;;;;;;;;;;;;6470:57;;;;;;;;5123:102;-1:-1:-1;1082:15797:5;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;6551:589;;;;1082:15797;;;;;;6551:589;;6605:4;1082:15797;6551:589;;1082:15797;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;;;6551:589;;1082:15797;6551:589;;;;;;;;;;;5123:102;-1:-1:-1;1082:15797:5;;-1:-1:-1;;;;;1082:15797:5;7161:145;;;;;1082:15797;;-1:-1:-1;;;7161:145:5;;6605:4;1082:15797;7161:145;;1082:15797;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;-1:-1:-1;1082:15797:5;;;-1:-1:-1;7161:145:5;;;;;;;;;5123:102;1082:15797;;;;;:::i;:::-;;;;3855:48;1082:15797;;12366:17;;;;:::i;:::-;1082:15797;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;7408:348;;1082:15797;;7408:348;1082:15797;;-1:-1:-1;;;;;1082:15797:5;;;;;;12258:440;1082:15797;;;;:::i;:::-;;;5132:40;5123:49;5132:40;;12509:17;;;;:::i;:::-;12258:440;;12401:297;12597:17;;;;:::i;:::-;12669;;;;:::i;7161:145::-;1082:15797;7161:145;;;:::i;:::-;;;;6551:589;1082:15797;6551:589;;;:::i;:::-;;;;6470:57;1082:15797;6470:57;;;:::i;:::-;;;;5123:102;1082:15797;;-1:-1:-1;;;5180:45:5;;1082:15797;;;;-1:-1:-1;;;;;1082:15797:5;;5180:45;;;;;;;1082:15797;5180:45;;;5123:102;;;;5180:45;;;;1082:15797;5180:45;1082:15797;5180:45;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;;4971:100;1082:15797;;-1:-1:-1;;;5027:44:5;;1082:15797;;;;-1:-1:-1;;;;;1082:15797:5;;5027:44;;;;;;;1082:15797;5027:44;;;4971:100;;;;;5027:44;;;;1082:15797;5027:44;1082:15797;5027:44;;;;;;;:::i;:::-;;;;1082:15797;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3851:791;1082:15797;;;;:::i;:::-;;;4114:40;4105:49;4114:40;;-1:-1:-1;;;;;1082:15797:5;;4178:31;1082:15797;;-1:-1:-1;;;;;1082:15797:5;;;3851:791;1082:15797;;;;;;;;;4101:541;-1:-1:-1;;;;;1082:15797:5;;4374:31;;1082:15797;;-1:-1:-1;;;;;1082:15797:5;;;4463:32;;1082:15797;;4553:39;3851:791;1082:15797;;;;;;;;;;;;;;;-1:-1:-1;;1082:15797:5;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;:::i;:::-;;;:::i;:::-;12478:70:58;;;:::i;:::-;-1:-1:-1;;;;;1082:15797:5;;;;;11980:3;1082:15797;;11958:20;;;;;-1:-1:-1;;;;;12007:12:5;;;;:::i;:::-;1082:15797;;11999:50;;;;;;1082:15797;;;;;;;;;;;;;11999:50;;;1082:15797;11999:50;;1082:15797;11999:50;;;;;;;1082:15797;11999:50;;;11980:3;;1082:15797;11943:13;;11999:50;1082:15797;11999:50;;;:::i;:::-;;;;1082:15797;;;;;;;:::i;:::-;12478:70:58;;;:::i;:::-;1082:15797:5;11452:3;1082:15797;;11432:18;;;;;-1:-1:-1;;;;;11487:10:5;;;;:::i;:::-;1082:15797;;;;;;;-1:-1:-1;;;;;1082:15797:5;11544:7;1082:15797;11544:7;;:::i;:::-;;11471:81;;;;;;1082:15797;;;;;;;;;;;;;;;11471:81;;1082:15797;11471:81;;;:::i;:::-;;;;;;;;;;1082:15797;11471:81;;;11452:3;;1082:15797;11417:13;;11471:81;1082:15797;11471:81;;;:::i;:::-;;;;1082:15797;;;;;;-1:-1:-1;;1082:15797:5;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;-1:-1:-1;;1082:15797:5;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;-1:-1:-1;;1082:15797:5;;;;1377:38;1082:15797;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;-1:-1:-1;;1082:15797:5;;;;-1:-1:-1;;11523:61:58;1082:15797:5;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;-1:-1:-1;;1082:15797:5;;;;;;:::i;:::-;;;:::i;:::-;;;;;;;;;;;9069:290;1082:15797;;;;9025:26;;1082:15797;;;;;;;;;;9025:26;;;1082:15797;9025:26;;:::i;:::-;1082:15797;9015:37;;9189:31;;1082:15797;;;9189:31;1082:15797;;9189:31;;1082:15797;;:::i;:::-;9189:31;;;1082:15797;9189:31;;;;;;9253:52;;;9151:184;9242:75;9253:52;;;1082:15797;;;9242:75;1082:15797;;;;;;:::i;:::-;;;;;;9242:75;;;;;;;;;:::i;:::-;;8714:72;;9242:75;;;;;;:::i;:::-;1082:15797;;9151:184;;;;;;1082:15797;;;;;;;;;;;;;;;;;;;;;;;;;;9151:184;8714:72;;9151:184;;;;;;:::i;:::-;1082:15797;9124:225;;9069:290;;:::i;:::-;1082:15797;;-1:-1:-1;;;;;1082:15797:5;;;;;;9253:52;-1:-1:-1;1082:15797:5;;-1:-1:-1;;;;;1082:15797:5;;9242:75;;9151:184;;9253:52;;1082:15797;;;;;;;:::i;:::-;12478:70:58;;;:::i;:::-;1082:15797:5;11181:3;1082:15797;;11161:18;;;;;-1:-1:-1;;;;;11216:10:5;;;;:::i;:::-;1082:15797;11245:23;1082:15797;;;;;-1:-1:-1;;;;;1082:15797:5;11270:7;1082:15797;11270:7;;:::i;:::-;;11200:78;;;;;;1082:15797;;;;;;;;;;;;;;;11200:78;;1082:15797;11200:78;;;:::i;:::-;;;;;;;;;;1082:15797;11200:78;;;11181:3;;1082:15797;11146:13;;11200:78;1082:15797;11200:78;;;:::i;:::-;;;;1082:15797;;;-1:-1:-1;;1082:15797:5;;;;12478:70:58;;:::i;:::-;1082:15797:5;5044:589:58;;6299:437;;;;;-1:-1:-1;;;;;6299:437:58;1082:15797:5;;;;;;;-1:-1:-1;;1082:15797:5;;;;-1:-1:-1;;;;;1082:15797:5;;:::i;:::-;;;;;;;;;;;;;;;;;;;1196:54;;;1082:15797;;1196:54;-1:-1:-1;;;;;1196:54:5;;;1082:15797;;;;;;1196:54;;;1082:15797;;;1196:54;;1082:15797;1196:54;;;;1082:15797;1196:54;;;;;1082:15797;1196:54;;1082:15797;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1082:15797:5;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;:::i;:::-;;;;;;;;;;12478:70:58;;:::i;:::-;1082:15797:5;10725:3;1082:15797;;10705:18;;;;;-1:-1:-1;;;;;10755:10:5;;;;:::i;:::-;1082:15797;;10744:42;;;;;;1082:15797;;;;;;;;;;;;;10744:42;;1082:15797;;;:::i;:::-;10744:42;1082:15797;10744:42;;1082:15797;10744:42;;;;;;;1082:15797;10744:42;;;10725:3;;1082:15797;10690:13;;10744:42;1082:15797;10744:42;;;:::i;:::-;;;;1082:15797;;;-1:-1:-1;;1082:15797:5;;;;9831:339:58;;;;1082:15797:5;9831:339:58;1082:15797:5;9831:339:58;;;;;;1082:15797:5;9831:339:58;;1082:15797:5;;;;;;;-1:-1:-1;;1082:15797:5;;;;6466:184:64;6407:6;6466:184;;;1082:15797:5;;;2619:66:64;1082:15797:5;;;6466:184:64;;1082:15797:5;6466:184:64;1082:15797:5;6466:184:64;;1082:15797:5;;;-1:-1:-1;;1082:15797:5;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;5771:446:64;5712:6;5771:446;;;15838:89:5;;:::i;:::-;1082:15797;;;;;4259:1327:64;;;;;1082:15797:5;4259:1327:64;1082:15797:5;4259:1327:64;;;;;;;;;;1082:15797:5;4259:1327:64;;;;;;;;1082:15797:5;4259:1327:64;;1082:15797:5;4259:1327:64;1082:15797:5;;4259:1327:64;1082:15797:5;;4259:1327:64;;;;;;;;;;1082:15797:5;4259:1327:64;;1082:15797:5;4259:1327:64;;;;;;;;;1082:15797:5;4259:1327:64;;1082:15797:5;;;;;;-1:-1:-1;;1082:15797:5;;;;1421:35;1082:15797;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;-1:-1:-1;;1082:15797:5;;;;9239:383:58;;;;1082:15797:5;9239:383:58;7972:9;9132:15;1082:15797:5;9239:383:58;;;;;;1082:15797:5;9239:383:58;;1082:15797:5;;;;;;;-1:-1:-1;;1082:15797:5;;;;;;:::i;:::-;12478:70:58;;:::i;:::-;1082:15797:5;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;10139:47;;;1082:15797;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;10202:73;;1082:15797;10202:73;-1:-1:-1;;;;;;1082:15797:5;;;;;;;;;;;-1:-1:-1;;1082:15797:5;;;;;;:::i;:::-;;;:::i;:::-;;;;:::i;:::-;;;:::i;:::-;;;:::i;:::-;2001:66:62;;;3207:622;;;;;;;;;1082:15797:5;-1:-1:-1;;;5044:589:58;;;-1:-1:-1;;;;;5044:589:58;;;;;;;;;-1:-1:-1;;5044:589:58;1082:15797:5;;-1:-1:-1;1082:15797:5;5044:589:58;1082:15797:5;;5044:589:58;1082:15797:5;;;;;;-1:-1:-1;;;;;1082:15797:5;;3207:622:62;1082:15797:5;;;3207:622:62;1082:15797:5;;;;;;;-1:-1:-1;;;;;1082:15797:5;;2253:50;1082:15797;;;2253:50;1082:15797;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;15746:4;1082:15797;;;15746:4;1082:15797;3892:296:62;;1082:15797:5;3892:296:62;2253:50:5;3892:296:62;;15746:4:5;1082:15797;3892:296:62;;1082:15797:5;3892:296:62;;1082:15797:5;5044:589:58;;1082:15797:5;5044:589:58;1082:15797:5;5044:589:58;;3207:622:62;;;;;;;;;;;;;;;;;;;;;;;;1082:15797:5;3207:622:62;1082:15797:5;3207:622:62;;1082:15797:5;;;;;;-1:-1:-1;;1082:15797:5;;;;;;:::i;:::-;;;:::i;:::-;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;8541:287;1082:15797;8238:285;1082:15797;;;;;;8238:285;;:::i;:::-;8661:31;;1082:15797;;;8661:31;1082:15797;;8661:31;;1082:15797;;:::i;:::-;8661:31;;;1082:15797;8661:31;;;;;;8725:49;;;8623:181;8714:72;8725:49;;1082:15797;;;8714:72;1082:15797;;;;;;:::i;8725:49::-;-1:-1:-1;8751:23:5;1082:15797;-1:-1:-1;;;;;1082:15797:5;;8714:72;;8623:181;;8725:49;;1082:15797;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;:::o;:::-;;;;;;;;;;;:::o;:::-;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;:::o;:::-;;;;-1:-1:-1;1082:15797:5;;;;;-1:-1:-1;1082:15797:5;;;;8714:72;;1082:15797;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;:::o;:::-;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1082:15797:5;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;8714:72;1082:15797;;-1:-1:-1;;1082:15797:5;;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;1082:15797:5;;-1:-1:-1;1082:15797:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;:::o;:::-;-1:-1:-1;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1082:15797:5;;;;:::o;:::-;;;;;;;;;;;;;;;:::o;:::-;;-1:-1:-1;1082:15797:5;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;:::o;:::-;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;:::o;:::-;;;;;;;;;;;;15933:751;1082:15797;;-1:-1:-1;;1082:15797:5;;;;;;16330:337;;;1082:15797;;;;;;;;;;;;;;-1:-1:-1;;;;;;1082:15797:5;;;;;;;;;;-1:-1:-1;;;;;;1082:15797:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;16330:337;;;;1082:15797;16330:337;:::i;:::-;1082:15797;16307:370;;15933:751;:::o;2261:165:46:-;;2794:1663;2261:165;2794:1663;;;;;;;;;;;2413:4;2794:1663;;;;;;;;;-1:-1:-1;;;;;2794:1663:46;;2261:165::o;7292:355:58:-;-1:-1:-1;;7390:251:58;;;;;7292:355::o;7390:251::-;;;;;;;6145:1089;1082:15797:5;;;;;6299:437:58;5044:589;;;6299:437;;;;;;;;;;-1:-1:-1;;6299:437:58;6145:1089::o;29154:916:52:-;29243:1;29282:17;-1:-1:-1;;;29282:17:52;;;29278:103;;29154:916;29398:17;29407:8;29978:7;29398:17;;;29394:103;;29154:916;29523:8;29514:17;;;29510:103;;29154:916;29639:7;29630:16;;;29626:100;;29154:916;29752:7;29743:16;;;29739:100;;29154:916;29865:7;29856:16;;;29852:100;;29154:916;29969:16;;29965:66;;29154:916;:::o;29965:66::-;30015:1;1082:15797:5;29154:916:52;:::o;29852:100::-;29865:7;29936:1;1082:15797:5;;;;29852:100:52;;;29739;29752:7;29823:1;1082:15797:5;;;;29739:100:52;;;29626;29639:7;29710:1;1082:15797:5;;;;29626:100:52;;;29510:103;29523:8;29596:2;1082:15797:5;;;;29510:103:52;;;29394;29407:8;29480:2;1082:15797:5;;;;29394:103:52;;;29278;29364:2;;-1:-1:-1;;;;1082:15797:5;;29278:103:52;;1210:847:46;;1082:15797:5;;1459:20:46;1455:80;;1544:417;;;;;-1:-1:-1;1544:417:46;;;;;;;;;;-1:-1:-1;;;;;1082:15797:5;;1974:18:46;1970:81;;1210:847::o;1970:81::-;2015:25;;;-1:-1:-1;2015:25:46;;-1:-1:-1;2015:25:46;1455:80;1502:22;;;-1:-1:-1;1502:22:46;;-1:-1:-1;1502:22:46;16690:152:5;;-1:-1:-1;;;;;1082:15797:5;;16773:30;;;1082:15797;;16690:152::o;1082:15797::-;;;;;;;;;16262:936:63;16388:804;;;16262:936;;16388:804;16262:936;16388:804;;12597:17:5;;16388:804:63;;;;;;;;;;;;;;;;;;;;;;;16262:936::o;16388:804::-;;;;;;", "linkReferences": {}, "immutableReferences": {"61731": [{"start": 5721, "length": 32}, {"start": 5896, "length": 32}]}}, "methodIdentifiers": {"TRUSTED_FORWARDER()": "af835119", "anyToAnySwap(address[],bool[],bool[],address,address,uint256,uint256)": "ffa5210a", "cancelOwnershipHandover()": "54d1f13d", "completeOwnershipHandover(address)": "f04e283e", "computeAddress(address,address,uint96,uint32,uint32,uint96,uint96,uint256,uint256,uint96,address,bool)": "1444ccb6", "computeVaultAddress(address,address,bool)": "7b4372fd", "deployProxy(uint8,address,address,uint96,uint32,uint32,uint96,uint96,uint256,uint256,uint96)": "ce186ec3", "initialize(address,address,address,address,address)": "1459457a", "kuruAmmVaultImplementation()": "a574b091", "marginAccountAddress()": "483100bd", "orderBookImplementation()": "a0416499", "owner()": "8da5cb5b", "ownershipHandoverExpiresAt(address)": "fee81cf4", "proxiableUUID()": "52d1902d", "renounceOwnership()": "715018a6", "requestOwnershipHandover()": "********", "toggleMarkets(address[],uint8)": "5f2b7a3c", "transferOwnership(address)": "f2fde38b", "transferOwnershipForContracts(address[],address)": "ca0dcb8b", "upgradeMultipleOrderBookProxies(address[],bytes[])": "72cfb2ca", "upgradeMultipleVaultProxies(address[],bytes[])": "c8a296aa", "upgradeOrderBookImplementation(address)": "dd874d0a", "upgradeToAndCall(address,bytes)": "4f1ef286", "upgradeVaultImplementation(address)": "17e4c4d9", "verifiedMarket(address)": "5f71a07c"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AlreadyInitialized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"BaseAndQuoteAssetSame\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Create2EmptyBytecode\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FailedDeployment\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidMarket\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidPricePrecision\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidSizePrecision\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidTickSize\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LengthMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MarketTypeMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NewOwnerIsZeroAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NoHandoverRequest\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NoMarketsPassed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SlippageExceeded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Uint96Overflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnauthorizedCallContext\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UpgradeFailed\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"msgSender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"debitToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"creditToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountIn\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountOut\",\"type\":\"uint256\"}],\"name\":\"KuruRouterSwap\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"baseAsset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"quoteAsset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"vaultAddress\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"pricePrecision\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"sizePrecision\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"tickSize\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"minSize\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"maxSize\",\"type\":\"uint96\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"takerFeeBps\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"makerFeeBps\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint96\",\"name\":\"kuruAmmSpread\",\"type\":\"uint96\"}],\"name\":\"MarketRegistered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"previousImplementation\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"}],\"name\":\"OBImplementationUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"OwnershipHandoverCanceled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"OwnershipHandoverRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"Upgraded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"previousImplementation\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"}],\"name\":\"VaultImplementationUpdated\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"TRUSTED_FORWARDER\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"_marketAddresses\",\"type\":\"address[]\"},{\"internalType\":\"bool[]\",\"name\":\"_isBuy\",\"type\":\"bool[]\"},{\"internalType\":\"bool[]\",\"name\":\"_nativeSend\",\"type\":\"bool[]\"},{\"internalType\":\"address\",\"name\":\"_debitToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_creditToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_minAmountOut\",\"type\":\"uint256\"}],\"name\":\"anyToAnySwap\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"_amountOut\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"cancelOwnershipHandover\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"completeOwnershipHandover\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_baseAssetAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_quoteAssetAddress\",\"type\":\"address\"},{\"internalType\":\"uint96\",\"name\":\"_sizePrecision\",\"type\":\"uint96\"},{\"internalType\":\"uint32\",\"name\":\"_pricePrecision\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_tickSize\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"_minSize\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"_maxSize\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"_takerFeeBps\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_makerFeeBps\",\"type\":\"uint256\"},{\"internalType\":\"uint96\",\"name\":\"_kuruAmmSpread\",\"type\":\"uint96\"},{\"internalType\":\"address\",\"name\":\"oldImplementation\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"old\",\"type\":\"bool\"}],\"name\":\"computeAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"proxy\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_marketAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oldImplementation\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"old\",\"type\":\"bool\"}],\"name\":\"computeVaultAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"enum IOrderBook.OrderBookType\",\"name\":\"_type\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"_baseAssetAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_quoteAssetAddress\",\"type\":\"address\"},{\"internalType\":\"uint96\",\"name\":\"_sizePrecision\",\"type\":\"uint96\"},{\"internalType\":\"uint32\",\"name\":\"_pricePrecision\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_tickSize\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"_minSize\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"_maxSize\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"_takerFeeBps\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_makerFeeBps\",\"type\":\"uint256\"},{\"internalType\":\"uint96\",\"name\":\"_kuruAmmSpread\",\"type\":\"uint96\"}],\"name\":\"deployProxy\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"proxy\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_marginAccount\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_orderbookImplementation\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_kuruAmmVaultImplementation\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_trustedForwarder\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"kuruAmmVaultImplementation\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"marginAccountAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"orderBookImplementation\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"result\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pendingOwner\",\"type\":\"address\"}],\"name\":\"ownershipHandoverExpiresAt\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"result\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"proxiableUUID\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"requestOwnershipHandover\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"markets\",\"type\":\"address[]\"},{\"internalType\":\"enum IOrderBook.MarketState\",\"name\":\"state\",\"type\":\"uint8\"}],\"name\":\"toggleMarkets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"contracts\",\"type\":\"address[]\"},{\"internalType\":\"address\",\"name\":\"_newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnershipForContracts\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"proxies\",\"type\":\"address[]\"},{\"internalType\":\"bytes[]\",\"name\":\"data\",\"type\":\"bytes[]\"}],\"name\":\"upgradeMultipleOrderBookProxies\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"proxies\",\"type\":\"address[]\"},{\"internalType\":\"bytes[]\",\"name\":\"data\",\"type\":\"bytes[]\"}],\"name\":\"upgradeMultipleVaultProxies\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"}],\"name\":\"upgradeOrderBookImplementation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"upgradeToAndCall\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"}],\"name\":\"upgradeVaultImplementation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"verifiedMarket\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"pricePrecision\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"sizePrecision\",\"type\":\"uint96\"},{\"internalType\":\"address\",\"name\":\"baseAssetAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"baseAssetDecimals\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"quoteAssetAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"quoteAssetDecimals\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"tickSize\",\"type\":\"uint32\"},{\"internalType\":\"uint96\",\"name\":\"minSize\",\"type\":\"uint96\"},{\"internalType\":\"uint96\",\"name\":\"maxSize\",\"type\":\"uint96\"},{\"internalType\":\"uint256\",\"name\":\"takerFeeBps\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"makerFeeBps\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"errors\":{\"AlreadyInitialized()\":[{\"details\":\"Cannot double-initialize.\"}],\"BaseAndQuoteAssetSame()\":[{\"details\":\"Thrown when base and quote asset addresses are the same\"}],\"Create2EmptyBytecode()\":[{\"details\":\"There's no code to deploy.\"}],\"FailedDeployment()\":[{\"details\":\"The deployment failed.\"}],\"InsufficientBalance(uint256,uint256)\":[{\"details\":\"The ETH balance of the account is not enough to perform the operation.\"}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"InvalidMarket()\":[{\"details\":\"Thrown when the market is invalid\"}],\"InvalidPricePrecision()\":[{\"details\":\"Thrown when price precision is not a power of 10\"}],\"InvalidSizePrecision()\":[{\"details\":\"Thrown when size precision is not a power of 10\"}],\"InvalidTickSize()\":[{\"details\":\"Thrown when tick size is 0\"}],\"LengthMismatch()\":[{\"details\":\"Thrown when the length of market addresses, isBuy, and nativeSend arrays are not the same\"}],\"MarketTypeMismatch()\":[{\"details\":\"Thrown when market type given and token addresses are not compatible\"}],\"NewOwnerIsZeroAddress()\":[{\"details\":\"The `newOwner` cannot be the zero address.\"}],\"NoHandoverRequest()\":[{\"details\":\"The `pendingOwner` does not have a valid handover request.\"}],\"NoMarketsPassed()\":[{\"details\":\"Thrown when no markets are passed as input\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"SlippageExceeded()\":[{\"details\":\"Thrown when the slippage exceeds the expected value\"}],\"Uint96Overflow()\":[{\"details\":\"Thrown when safe cast to uint96 fails\"}],\"Unauthorized()\":[{\"details\":\"The caller is not authorized to call the function.\"}],\"UnauthorizedCallContext()\":[{\"details\":\"The call is from an unauthorized call context.\"}],\"UpgradeFailed()\":[{\"details\":\"The upgrade failed.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized.\"},\"OwnershipHandoverCanceled(address)\":{\"details\":\"The ownership handover to `pendingOwner` has been canceled.\"},\"OwnershipHandoverRequested(address)\":{\"details\":\"An ownership handover to `pendingOwner` has been requested.\"},\"OwnershipTransferred(address,address)\":{\"details\":\"The ownership is transferred from `oldOwner` to `newOwner`. This event is intentionally kept the same as OpenZeppelin's Ownable to be compatible with indexers and [EIP-173](https://eips.ethereum.org/EIPS/eip-173), despite it not being as lightweight as a single argument event.\"},\"Upgraded(address)\":{\"details\":\"Emitted when the proxy's implementation is upgraded.\"}},\"kind\":\"dev\",\"methods\":{\"anyToAnySwap(address[],bool[],bool[],address,address,uint256,uint256)\":{\"details\":\"This function lets the user swap any token to any token across multiple markets\",\"params\":{\"_amount\":\"The amount of the debit token the user wants to send\",\"_creditToken\":\"The token the user wants to receive\",\"_debitToken\":\"The token the user wants to send\",\"_isBuy\":\"Whether the user wants to buy from or sell to the i-th market\",\"_marketAddresses\":\"The markets to swap through\",\"_minAmountOut\":\"The minimum amount of the credit token the user wants to receive\",\"_nativeSend\":\"Whether the user is sending native tokens or not to the i-th market\"},\"returns\":{\"_amountOut\":\"The amount of the credit token the user received\"}},\"cancelOwnershipHandover()\":{\"details\":\"Cancels the two-step ownership handover to the caller, if any.\"},\"completeOwnershipHandover(address)\":{\"details\":\"Allows the owner to complete the two-step ownership handover to `pendingOwner`. Reverts if there is no existing ownership handover requested by `pendingOwner`.\"},\"deployProxy(uint8,address,address,uint96,uint32,uint32,uint96,uint96,uint256,uint256,uint96)\":{\"details\":\"Deploys an OrderBook proxy for a set of given parameters\",\"params\":{\"_baseAssetAddress\":\"The base asset address. Can be address 0 if _type is NATIVE_IN_BASE\",\"_kuruAmmSpread\":\"The spread in basis points for the kuru amm vault\",\"_makerFeeBps\":\"The maker fee in basis points. The maker fee must be lower than the taker fee.\",\"_maxSize\":\"The maximum size an order can have to be placed\",\"_minSize\":\"The minimum size an order must have to be placed\",\"_pricePrecision\":\"The price precision of the asset pair\",\"_quoteAssetAddress\":\"The quote asset address. Can be address 0 if _type is NATIVE_IN_QUOTE\",\"_sizePrecision\":\"The size precision of the asset pair\",\"_takerFeeBps\":\"The taker fee in basis points\",\"_tickSize\":\"The tick size of the price\",\"_type\":\"The type of OrderBook which can be NO_NATIVE, NATIVE_IN_BASE or NATIVE_IN_QUOTE\"},\"returns\":{\"proxy\":\"The address of the deployed proxy\"}},\"initialize(address,address,address,address,address)\":{\"params\":{\"_kuruAmmVaultImplementation\":\"The address of the kuru amm vault implementation\",\"_marginAccount\":\"The address of the margin account\",\"_orderbookImplementation\":\"The address of the orderbook implementation\",\"_owner\":\"The owner of the contract.\",\"_trustedForwarder\":\"The address of the trusted forwarder\"}},\"owner()\":{\"details\":\"Returns the owner of the contract.\"},\"ownershipHandoverExpiresAt(address)\":{\"details\":\"Returns the expiry timestamp for the two-step ownership handover to `pendingOwner`.\"},\"proxiableUUID()\":{\"details\":\"Returns the storage slot used by the implementation, as specified in [ERC1822](https://eips.ethereum.org/EIPS/eip-1822). Note: The `notDelegated` modifier prevents accidental upgrades to an implementation that is a proxy contract.\"},\"renounceOwnership()\":{\"details\":\"Allows the owner to renounce their ownership.\"},\"requestOwnershipHandover()\":{\"details\":\"Request a two-step ownership handover to the caller. The request will automatically expire in 48 hours (172800 seconds) by default.\"},\"toggleMarkets(address[],uint8)\":{\"details\":\"Function to toggle the market state for pausing or resuming markets. Can only be called by owner\",\"params\":{\"markets\":\"The markets to toggle the state of\",\"state\":\"The state to set the market to\"}},\"transferOwnership(address)\":{\"details\":\"Allows the owner to transfer the ownership to `newOwner`.\"},\"transferOwnershipForContracts(address[],address)\":{\"details\":\"This function lets the owner batch transfer ownership of contracts to a new owner\",\"params\":{\"_newOwner\":\"The new owner of the contracts\",\"contracts\":\"The contracts which are to be transferred to the new owner\"}},\"upgradeMultipleOrderBookProxies(address[],bytes[])\":{\"details\":\"This function lets the owner batch upgrade orderbooks if a new implementation is available\",\"params\":{\"proxies\":\"The proxies which are to be upgraded to the latest orderbook implementation\"}},\"upgradeOrderBookImplementation(address)\":{\"details\":\"Function to upgrade the orderbook implementation. Can only be called by owner\",\"params\":{\"newImplementation\":\"The new deployed orderbook implementation\"}},\"upgradeToAndCall(address,bytes)\":{\"details\":\"Upgrades the proxy's implementation to `newImplementation`. Emits a {Upgraded} event. Note: Passing in empty `data` skips the delegatecall to `newImplementation`.\"},\"upgradeVaultImplementation(address)\":{\"details\":\"Function to upgrade the kuru amm vault implementation. Can only be called by owner\",\"params\":{\"newImplementation\":\"The new deployed kuru amm vault implementation\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/Router.sol\":\"Router\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/Router.sol\":{\"keccak256\":\"0xc1aef5024f67486952008a5bc7617c652b15e2f0f8a43371b94a83529e603402\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://3bdba47306acc4d8ae80f1bec973760e7949a707931126815bddb604d4ed8878\",\"dweb:/ipfs/QmU1Eybo2JteXwvjmKfcgyQYa5waFfTd2jUpsdVArsk1Kk\"]},\"contracts/interfaces/IKuruAMMVault.sol\":{\"keccak256\":\"0xf0460e901fd738f2046c075784aa3045a58342c2f8ed276df83cf5386be969c7\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://54c3a5647cce41595226f4bbdc403bf6ef91177f3caa57de908e9ecaa2ac6460\",\"dweb:/ipfs/QmcWxssB8rFcX1QCZBqHzmmE3Dq2AHUFPfJvogzegQnMoS\"]},\"contracts/interfaces/IMarginAccount.sol\":{\"keccak256\":\"0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4\",\"dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo\"]},\"contracts/interfaces/IOrderBook.sol\":{\"keccak256\":\"0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d\",\"dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68\"]},\"contracts/interfaces/IRouter.sol\":{\"keccak256\":\"0x8b5aed176358b66cd5e9a7286eb927b0aa60cc895b83f1ec3d90bea9788d8702\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://c91e0f439908e9e7ad7c5741cab792806721613fa5c6ac74d0b307bb67489340\",\"dweb:/ipfs/QmYdpg9TuTAAAtT3KzUU6yWNnRXCpGxSs51MsBWjtJnXLu\"]},\"contracts/libraries/Errors.sol\":{\"keccak256\":\"0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7\",\"dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol\":{\"keccak256\":\"0xbf2aefe54b76d7f7bcd4f6da1080b7b1662611937d870b880db584d09cea56b5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5e7e2f12e0feec75296e57f51f82fdaa8bd1551f4b8cc6560442c0bf60f818c\",\"dweb:/ipfs/QmcW9wDMaQ8RbQibMarfp17a3bABzY5KraWe2YDwuUrUoz\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049\",\"dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0xa1ad192cd45317c788618bef5cb1fb3ca4ce8b230f6433ac68cc1d850fb81618\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b43447bb85a53679d269a403c693b9d88d6c74177dfb35eddca63abaf7cf110a\",\"dweb:/ipfs/QmXSDmpd4bNZj1PDgegr6C4w1jDaWHXCconC3rYiw9TSkQ\"]},\"lib/openzeppelin-contracts/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0x20462ddb2665e9521372c76b001d0ce196e59dbbd989de9af5576cad0bd5628b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f417fd12aeec8fbfaceaa30e3a08a0724c0bc39de363e2acf6773c897abbaf6d\",\"dweb:/ipfs/QmU4Hko6sApdweVM92CsiuLKkCk8HfyBeutF89PCTz5Tye\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0x6d0ae6e206645341fd122d278c2cb643dea260c190531f2f3f6a0426e77b00c0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://032d1201d839435be2c85b72e33206b3ea980c569d6ebf7fa57d811ab580a82f\",\"dweb:/ipfs/QmeqQjAtMvdZT2tG7zm39itcRJkuwu8AEReK6WRnLJ18DD\"]},\"lib/openzeppelin-contracts/contracts/utils/Create2.sol\":{\"keccak256\":\"0xbb7e8401583d26268ea9103013bcdcd90866a7718bd91105ebd21c9bf11f4f06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://866a11ad89c93ee918078f7a46ae31e17d89216ce64603f0d34be7ed0a5c520e\",\"dweb:/ipfs/QmW3ckLEJg2v2NzuVLNJFmRuerGSipw6Dzg6ntbmqbAGoC\"]},\"lib/openzeppelin-contracts/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"node_modules/solady/src/auth/Ownable.sol\":{\"keccak256\":\"0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3\",\"dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs\"]},\"node_modules/solady/src/utils/Initializable.sol\":{\"keccak256\":\"0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559\",\"dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy\"]},\"node_modules/solady/src/utils/SafeTransferLib.sol\":{\"keccak256\":\"0x583f47701d9b47bb3ef80fcabbbd62fbb58a01733b7a57e19658b4b02468883a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2523bfac005e21ef9963fdb3c08b2c61824e2b5ce2f53d1a1828b01ed995217c\",\"dweb:/ipfs/QmbBjVG9tZyeZSQH4m5GUzNBwo2iuvLoZYbmhT4gxnJc4J\"]},\"node_modules/solady/src/utils/UUPSUpgradeable.sol\":{\"keccak256\":\"0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16\",\"dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "AlreadyInitialized"}, {"inputs": [], "type": "error", "name": "BaseAndQuoteAssetSame"}, {"inputs": [], "type": "error", "name": "Create2EmptyBytecode"}, {"inputs": [], "type": "error", "name": "FailedDeployment"}, {"inputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "InsufficientBalance"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "InvalidMarket"}, {"inputs": [], "type": "error", "name": "InvalidPricePrecision"}, {"inputs": [], "type": "error", "name": "InvalidSizePrecision"}, {"inputs": [], "type": "error", "name": "InvalidTickSize"}, {"inputs": [], "type": "error", "name": "LengthMismatch"}, {"inputs": [], "type": "error", "name": "MarketTypeMismatch"}, {"inputs": [], "type": "error", "name": "NewOwnerIsZeroAddress"}, {"inputs": [], "type": "error", "name": "NoHandoverRequest"}, {"inputs": [], "type": "error", "name": "NoMarketsPassed"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [], "type": "error", "name": "SlippageExceeded"}, {"inputs": [], "type": "error", "name": "Uint96Overflow"}, {"inputs": [], "type": "error", "name": "Unauthorized"}, {"inputs": [], "type": "error", "name": "UnauthorizedCallContext"}, {"inputs": [], "type": "error", "name": "UpgradeFailed"}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "msgSender", "type": "address", "indexed": false}, {"internalType": "address", "name": "debitToken", "type": "address", "indexed": false}, {"internalType": "address", "name": "creditToken", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "amountIn", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amountOut", "type": "uint256", "indexed": false}], "type": "event", "name": "KuruRouterSwap", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "baseAsset", "type": "address", "indexed": false}, {"internalType": "address", "name": "quoteAsset", "type": "address", "indexed": false}, {"internalType": "address", "name": "market", "type": "address", "indexed": false}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address", "indexed": false}, {"internalType": "uint32", "name": "pricePrecision", "type": "uint32", "indexed": false}, {"internalType": "uint96", "name": "sizePrecision", "type": "uint96", "indexed": false}, {"internalType": "uint32", "name": "tickSize", "type": "uint32", "indexed": false}, {"internalType": "uint96", "name": "minSize", "type": "uint96", "indexed": false}, {"internalType": "uint96", "name": "maxSize", "type": "uint96", "indexed": false}, {"internalType": "uint256", "name": "takerFeeBps", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "makerFeeBps", "type": "uint256", "indexed": false}, {"internalType": "uint96", "name": "kuruAmmSpread", "type": "uint96", "indexed": false}], "type": "event", "name": "MarketRegistered", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousImplementation", "type": "address", "indexed": false}, {"internalType": "address", "name": "newImplementation", "type": "address", "indexed": false}], "type": "event", "name": "OBImplementationUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipHandoverCanceled", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipHandoverRequested", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "old<PERSON>wner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address", "indexed": true}], "type": "event", "name": "Upgraded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousImplementation", "type": "address", "indexed": false}, {"internalType": "address", "name": "newImplementation", "type": "address", "indexed": false}], "type": "event", "name": "VaultImplementationUpdated", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "TRUSTED_FORWARDER", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address[]", "name": "_marketAddresses", "type": "address[]"}, {"internalType": "bool[]", "name": "_isBuy", "type": "bool[]"}, {"internalType": "bool[]", "name": "_nativeSend", "type": "bool[]"}, {"internalType": "address", "name": "_debitToken", "type": "address"}, {"internalType": "address", "name": "_creditToken", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "uint256", "name": "_minAmountOut", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "anyToAnySwap", "outputs": [{"internalType": "uint256", "name": "_amountOut", "type": "uint256"}]}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "cancelOwnershipHandover"}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "stateMutability": "payable", "type": "function", "name": "completeOwnershipHandover"}, {"inputs": [{"internalType": "address", "name": "_baseAssetAddress", "type": "address"}, {"internalType": "address", "name": "_quote<PERSON><PERSON><PERSON>dd<PERSON>", "type": "address"}, {"internalType": "uint96", "name": "_sizePrecision", "type": "uint96"}, {"internalType": "uint32", "name": "_pricePrecision", "type": "uint32"}, {"internalType": "uint32", "name": "_tickSize", "type": "uint32"}, {"internalType": "uint96", "name": "_minSize", "type": "uint96"}, {"internalType": "uint96", "name": "_maxSize", "type": "uint96"}, {"internalType": "uint256", "name": "_takerFeeBps", "type": "uint256"}, {"internalType": "uint256", "name": "_makerFeeBps", "type": "uint256"}, {"internalType": "uint96", "name": "_kuruAmmSpread", "type": "uint96"}, {"internalType": "address", "name": "oldImplementation", "type": "address"}, {"internalType": "bool", "name": "old", "type": "bool"}], "stateMutability": "view", "type": "function", "name": "computeAddress", "outputs": [{"internalType": "address", "name": "proxy", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "_marketAddress", "type": "address"}, {"internalType": "address", "name": "oldImplementation", "type": "address"}, {"internalType": "bool", "name": "old", "type": "bool"}], "stateMutability": "view", "type": "function", "name": "computeVaultAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "enum IOrderBook.OrderBookType", "name": "_type", "type": "uint8"}, {"internalType": "address", "name": "_baseAssetAddress", "type": "address"}, {"internalType": "address", "name": "_quote<PERSON><PERSON><PERSON>dd<PERSON>", "type": "address"}, {"internalType": "uint96", "name": "_sizePrecision", "type": "uint96"}, {"internalType": "uint32", "name": "_pricePrecision", "type": "uint32"}, {"internalType": "uint32", "name": "_tickSize", "type": "uint32"}, {"internalType": "uint96", "name": "_minSize", "type": "uint96"}, {"internalType": "uint96", "name": "_maxSize", "type": "uint96"}, {"internalType": "uint256", "name": "_takerFeeBps", "type": "uint256"}, {"internalType": "uint256", "name": "_makerFeeBps", "type": "uint256"}, {"internalType": "uint96", "name": "_kuruAmmSpread", "type": "uint96"}], "stateMutability": "nonpayable", "type": "function", "name": "deployProxy", "outputs": [{"internalType": "address", "name": "proxy", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "address", "name": "_marginAccount", "type": "address"}, {"internalType": "address", "name": "_orderbookImplementation", "type": "address"}, {"internalType": "address", "name": "_kuruAmmVaultImplementation", "type": "address"}, {"internalType": "address", "name": "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "kuruAmmVaultImplementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "marginA<PERSON>unt<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "orderBookImplementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "result", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "stateMutability": "view", "type": "function", "name": "ownershipHandoverExpiresAt", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "renounceOwnership"}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "requestOwnershipHandover"}, {"inputs": [{"internalType": "address[]", "name": "markets", "type": "address[]"}, {"internalType": "enum IOrderBook.MarketState", "name": "state", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "toggleMarkets"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "payable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "address[]", "name": "contracts", "type": "address[]"}, {"internalType": "address", "name": "_newOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnershipForContracts"}, {"inputs": [{"internalType": "address[]", "name": "proxies", "type": "address[]"}, {"internalType": "bytes[]", "name": "data", "type": "bytes[]"}], "stateMutability": "nonpayable", "type": "function", "name": "upgradeMultipleOrderBookProxies"}, {"inputs": [{"internalType": "address[]", "name": "proxies", "type": "address[]"}, {"internalType": "bytes[]", "name": "data", "type": "bytes[]"}], "stateMutability": "nonpayable", "type": "function", "name": "upgradeMultipleVaultProxies"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "upgradeOrderBookImplementation"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "upgradeToAndCall"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "upgradeVaultImplementation"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "verifiedMarket", "outputs": [{"internalType": "uint32", "name": "pricePrecision", "type": "uint32"}, {"internalType": "uint96", "name": "sizePrecision", "type": "uint96"}, {"internalType": "address", "name": "baseAssetAddress", "type": "address"}, {"internalType": "uint256", "name": "baseAssetDecimals", "type": "uint256"}, {"internalType": "address", "name": "quote<PERSON>set<PERSON>dd<PERSON>", "type": "address"}, {"internalType": "uint256", "name": "quoteAssetDecimals", "type": "uint256"}, {"internalType": "uint32", "name": "tickSize", "type": "uint32"}, {"internalType": "uint96", "name": "minSize", "type": "uint96"}, {"internalType": "uint96", "name": "maxSize", "type": "uint96"}, {"internalType": "uint256", "name": "takerFeeBps", "type": "uint256"}, {"internalType": "uint256", "name": "makerFeeBps", "type": "uint256"}]}, {"inputs": [], "stateMutability": "payable", "type": "receive"}], "devdoc": {"kind": "dev", "methods": {"anyToAnySwap(address[],bool[],bool[],address,address,uint256,uint256)": {"details": "This function lets the user swap any token to any token across multiple markets", "params": {"_amount": "The amount of the debit token the user wants to send", "_creditToken": "The token the user wants to receive", "_debitToken": "The token the user wants to send", "_isBuy": "Whether the user wants to buy from or sell to the i-th market", "_marketAddresses": "The markets to swap through", "_minAmountOut": "The minimum amount of the credit token the user wants to receive", "_nativeSend": "Whether the user is sending native tokens or not to the i-th market"}, "returns": {"_amountOut": "The amount of the credit token the user received"}}, "cancelOwnershipHandover()": {"details": "Cancels the two-step ownership handover to the caller, if any."}, "completeOwnershipHandover(address)": {"details": "Allows the owner to complete the two-step ownership handover to `pendingOwner`. Reverts if there is no existing ownership handover requested by `pendingOwner`."}, "deployProxy(uint8,address,address,uint96,uint32,uint32,uint96,uint96,uint256,uint256,uint96)": {"details": "Deploys an OrderBook proxy for a set of given parameters", "params": {"_baseAssetAddress": "The base asset address. Can be address 0 if _type is NATIVE_IN_BASE", "_kuruAmmSpread": "The spread in basis points for the kuru amm vault", "_makerFeeBps": "The maker fee in basis points. The maker fee must be lower than the taker fee.", "_maxSize": "The maximum size an order can have to be placed", "_minSize": "The minimum size an order must have to be placed", "_pricePrecision": "The price precision of the asset pair", "_quoteAssetAddress": "The quote asset address. Can be address 0 if _type is NATIVE_IN_QUOTE", "_sizePrecision": "The size precision of the asset pair", "_takerFeeBps": "The taker fee in basis points", "_tickSize": "The tick size of the price", "_type": "The type of OrderBook which can be NO_NATIVE, NATIVE_IN_BASE or NATIVE_IN_QUOTE"}, "returns": {"proxy": "The address of the deployed proxy"}}, "initialize(address,address,address,address,address)": {"params": {"_kuruAmmVaultImplementation": "The address of the kuru amm vault implementation", "_marginAccount": "The address of the margin account", "_orderbookImplementation": "The address of the orderbook implementation", "_owner": "The owner of the contract.", "_trustedForwarder": "The address of the trusted forwarder"}}, "owner()": {"details": "Returns the owner of the contract."}, "ownershipHandoverExpiresAt(address)": {"details": "Returns the expiry timestamp for the two-step ownership handover to `pendingOwner`."}, "proxiableUUID()": {"details": "Returns the storage slot used by the implementation, as specified in [ERC1822](https://eips.ethereum.org/EIPS/eip-1822). Note: The `notDelegated` modifier prevents accidental upgrades to an implementation that is a proxy contract."}, "renounceOwnership()": {"details": "Allows the owner to renounce their ownership."}, "requestOwnershipHandover()": {"details": "Request a two-step ownership handover to the caller. The request will automatically expire in 48 hours (172800 seconds) by default."}, "toggleMarkets(address[],uint8)": {"details": "Function to toggle the market state for pausing or resuming markets. Can only be called by owner", "params": {"markets": "The markets to toggle the state of", "state": "The state to set the market to"}}, "transferOwnership(address)": {"details": "Allows the owner to transfer the ownership to `newOwner`."}, "transferOwnershipForContracts(address[],address)": {"details": "This function lets the owner batch transfer ownership of contracts to a new owner", "params": {"_newOwner": "The new owner of the contracts", "contracts": "The contracts which are to be transferred to the new owner"}}, "upgradeMultipleOrderBookProxies(address[],bytes[])": {"details": "This function lets the owner batch upgrade orderbooks if a new implementation is available", "params": {"proxies": "The proxies which are to be upgraded to the latest orderbook implementation"}}, "upgradeOrderBookImplementation(address)": {"details": "Function to upgrade the orderbook implementation. Can only be called by owner", "params": {"newImplementation": "The new deployed orderbook implementation"}}, "upgradeToAndCall(address,bytes)": {"details": "Upgrades the proxy's implementation to `newImplementation`. Emits a {Upgraded} event. Note: Passing in empty `data` skips the delegatecall to `newImplementation`."}, "upgradeVaultImplementation(address)": {"details": "Function to upgrade the kuru amm vault implementation. Can only be called by owner", "params": {"newImplementation": "The new deployed kuru amm vault implementation"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/Router.sol": "Router"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/Router.sol": {"keccak256": "0xc1aef5024f67486952008a5bc7617c652b15e2f0f8a43371b94a83529e603402", "urls": ["bzz-raw://3bdba47306acc4d8ae80f1bec973760e7949a707931126815bddb604d4ed8878", "dweb:/ipfs/QmU1Eybo2JteXwvjmKfcgyQYa5waFfTd2jUpsdVArsk1Kk"], "license": "BUSL-1.1"}, "contracts/interfaces/IKuruAMMVault.sol": {"keccak256": "0xf0460e901fd738f2046c075784aa3045a58342c2f8ed276df83cf5386be969c7", "urls": ["bzz-raw://54c3a5647cce41595226f4bbdc403bf6ef91177f3caa57de908e9ecaa2ac6460", "dweb:/ipfs/QmcWxssB8rFcX1QCZBqHzmmE3Dq2AHUFPfJvogzegQnMoS"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IMarginAccount.sol": {"keccak256": "0x4d51b3fa3b47a785aa41cdfde103b9334780e927e1d3d5fbb0caa97a455fdae2", "urls": ["bzz-raw://19c41c543cca8fa0ef610c3221e6ab1ae803931bb9f44930d58cc102c1f388b4", "dweb:/ipfs/QmSxeZ2So9qsDsPR9Vn2Ww9tmvPt75iDTtS2ByFTrd3aQo"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IOrderBook.sol": {"keccak256": "0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b", "urls": ["bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d", "dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IRouter.sol": {"keccak256": "0x8b5aed176358b66cd5e9a7286eb927b0aa60cc895b83f1ec3d90bea9788d8702", "urls": ["bzz-raw://c91e0f439908e9e7ad7c5741cab792806721613fa5c6ac74d0b307bb67489340", "dweb:/ipfs/QmYdpg9TuTAAAtT3KzUU6yWNnRXCpGxSs51MsBWjtJnXLu"], "license": "GPL-2.0-or-later"}, "contracts/libraries/Errors.sol": {"keccak256": "0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f", "urls": ["bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7", "dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu"], "license": "BUSL-1.1"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol": {"keccak256": "0xbf2aefe54b76d7f7bcd4f6da1080b7b1662611937d870b880db584d09cea56b5", "urls": ["bzz-raw://f5e7e2f12e0feec75296e57f51f82fdaa8bd1551f4b8cc6560442c0bf60f818c", "dweb:/ipfs/QmcW9wDMaQ8RbQibMarfp17a3bABzY5KraWe2YDwuUrUoz"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e", "urls": ["bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049", "dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0xa1ad192cd45317c788618bef5cb1fb3ca4ce8b230f6433ac68cc1d850fb81618", "urls": ["bzz-raw://b43447bb85a53679d269a403c693b9d88d6c74177dfb35eddca63abaf7cf110a", "dweb:/ipfs/QmXSDmpd4bNZj1PDgegr6C4w1jDaWHXCconC3rYiw9TSkQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0x20462ddb2665e9521372c76b001d0ce196e59dbbd989de9af5576cad0bd5628b", "urls": ["bzz-raw://f417fd12aeec8fbfaceaa30e3a08a0724c0bc39de363e2acf6773c897abbaf6d", "dweb:/ipfs/QmU4Hko6sApdweVM92CsiuLKkCk8HfyBeutF89PCTz5Tye"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0x6d0ae6e206645341fd122d278c2cb643dea260c190531f2f3f6a0426e77b00c0", "urls": ["bzz-raw://032d1201d839435be2c85b72e33206b3ea980c569d6ebf7fa57d811ab580a82f", "dweb:/ipfs/QmeqQjAtMvdZT2tG7zm39itcRJkuwu8AEReK6WRnLJ18DD"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Create2.sol": {"keccak256": "0xbb7e8401583d26268ea9103013bcdcd90866a7718bd91105ebd21c9bf11f4f06", "urls": ["bzz-raw://866a11ad89c93ee918078f7a46ae31e17d89216ce64603f0d34be7ed0a5c520e", "dweb:/ipfs/QmW3ckLEJg2v2NzuVLNJFmRuerGSipw6Dzg6ntbmqbAGoC"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "node_modules/solady/src/auth/Ownable.sol": {"keccak256": "0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3", "urls": ["bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3", "dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs"], "license": "MIT"}, "node_modules/solady/src/utils/Initializable.sol": {"keccak256": "0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec", "urls": ["bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559", "dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy"], "license": "MIT"}, "node_modules/solady/src/utils/SafeTransferLib.sol": {"keccak256": "0x583f47701d9b47bb3ef80fcabbbd62fbb58a01733b7a57e19658b4b02468883a", "urls": ["bzz-raw://2523bfac005e21ef9963fdb3c08b2c61824e2b5ce2f53d1a1828b01ed995217c", "dweb:/ipfs/QmbBjVG9tZyeZSQH4m5GUzNBwo2iuvLoZYbmhT4gxnJc4J"], "license": "MIT"}, "node_modules/solady/src/utils/UUPSUpgradeable.sol": {"keccak256": "0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e", "urls": ["bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16", "dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV"], "license": "MIT"}}, "version": 1}, "id": 5}