{"abi": [{"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "receiveETH", "inputs": [], "outputs": [], "stateMutability": "payable"}], "bytecode": {"object": "0x608080604052346013576067908160188239f35b5f80fdfe60043610156013575b36156011575f80fd5b005b5f3560e01c633ecfd51e036008575f366003190112602d57005b5f80fdfea2646970667358221220b1eb37b6a5d3bd8652fc7a24641b8d8a128bb706d8a2d8c05f8efa3ae058c09764736f6c634300081c0033", "sourceMap": "14697:150:35:-:0;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x60043610156013575b36156011575f80fd5b005b5f3560e01c633ecfd51e036008575f366003190112602d57005b5f80fdfea2646970667358221220b1eb37b6a5d3bd8652fc7a24641b8d8a128bb706d8a2d8c05f8efa3ae058c09764736f6c634300081c0033", "sourceMap": "14697:150:35:-:0;;;;;;-1:-1:-1;14697:150:35;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;14697:150:35;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {"receiveETH()": "3ecfd51e"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"receiveETH\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/ETHStrandingBugPOC.t.sol\":\"MockMarginAccount\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/KuruForwarder.sol\":{\"keccak256\":\"0xacc170f5ce66221ae208235ae21d4014b537002f3085c729fd67793a97ce8730\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ce914cf74f9d9d3fd73b8392318063d4b57c90e7b615990030d1afd1f2402368\",\"dweb:/ipfs/QmZvTGqYLwiBSQKDTKVEVAQN3mEBMuLefuUoAWrW6uPAP4\"]},\"contracts/interfaces/IOrderBook.sol\":{\"keccak256\":\"0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d\",\"dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68\"]},\"contracts/libraries/Errors.sol\":{\"keccak256\":\"0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7\",\"dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu\"]},\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3\",\"dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc\",\"dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a\",\"dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT\"]},\"lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4\",\"dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xe2b159412b06b44a7f90972104300c587f308506d37d2143dd7e689e2eac6f01\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://a96e13ac6fec3ffe61a55d6005ef0ef96f47adf3f4f3531e0418f0bf02d3f93c\",\"dweb:/ipfs/QmeFci69gm7a6c6pEqSNoe3HfXPVPiagUc51Pyo5PoS8Rn\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol\":{\"keccak256\":\"0xbf2aefe54b76d7f7bcd4f6da1080b7b1662611937d870b880db584d09cea56b5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5e7e2f12e0feec75296e57f51f82fdaa8bd1551f4b8cc6560442c0bf60f818c\",\"dweb:/ipfs/QmcW9wDMaQ8RbQibMarfp17a3bABzY5KraWe2YDwuUrUoz\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049\",\"dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0xa1ad192cd45317c788618bef5cb1fb3ca4ce8b230f6433ac68cc1d850fb81618\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b43447bb85a53679d269a403c693b9d88d6c74177dfb35eddca63abaf7cf110a\",\"dweb:/ipfs/QmXSDmpd4bNZj1PDgegr6C4w1jDaWHXCconC3rYiw9TSkQ\"]},\"lib/openzeppelin-contracts/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0x20462ddb2665e9521372c76b001d0ce196e59dbbd989de9af5576cad0bd5628b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f417fd12aeec8fbfaceaa30e3a08a0724c0bc39de363e2acf6773c897abbaf6d\",\"dweb:/ipfs/QmU4Hko6sApdweVM92CsiuLKkCk8HfyBeutF89PCTz5Tye\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0x6d0ae6e206645341fd122d278c2cb643dea260c190531f2f3f6a0426e77b00c0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://032d1201d839435be2c85b72e33206b3ea980c569d6ebf7fa57d811ab580a82f\",\"dweb:/ipfs/QmeqQjAtMvdZT2tG7zm39itcRJkuwu8AEReK6WRnLJ18DD\"]},\"lib/openzeppelin-contracts/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"node_modules/solady/src/auth/Ownable.sol\":{\"keccak256\":\"0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3\",\"dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs\"]},\"node_modules/solady/src/utils/ECDSA.sol\":{\"keccak256\":\"0x077d168511141c83fd93914f4609c5363341da03a3971cbc0d3f6a75e9893de0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c218864adb4e97d81908318d9bd0ed75f40ec996c826141fab2646135820295b\",\"dweb:/ipfs/QmVQyWG6Ff4LAUnzj6JMEHHYfNBANVYLaMkun6TYaWShms\"]},\"node_modules/solady/src/utils/EIP712.sol\":{\"keccak256\":\"0xb5c4c8ac5368c9785b4e30314f4ad6f3ae13bdc21679007735681d13da797bec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c4456a4eaa8748f802fd1188db6405d18c452eb7c0dde84a49b49a7f94b5970d\",\"dweb:/ipfs/QmZzsFn4VwvBFy2MJVJXvntCQsDRCXbRrSKKfXxXv9jYGM\"]},\"node_modules/solady/src/utils/Initializable.sol\":{\"keccak256\":\"0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559\",\"dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy\"]},\"node_modules/solady/src/utils/UUPSUpgradeable.sol\":{\"keccak256\":\"0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16\",\"dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV\"]},\"test/ETHStrandingBugPOC.t.sol\":{\"keccak256\":\"0x8034634705bee2f47030d55e7a9b69efece10fcd3edb9c1e685b39ad5a7c732d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8068e0028a9e11860d60ab47b9dac4cb5b2f0c55945fd14c59edf84bbeb344b8\",\"dweb:/ipfs/QmegGBXZKreLboDWiFkxfmoyLnAsXKDJALMDnnszHMnVaH\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "payable", "type": "function", "name": "receiveETH"}, {"inputs": [], "stateMutability": "payable", "type": "receive"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/ETHStrandingBugPOC.t.sol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/KuruForwarder.sol": {"keccak256": "0xacc170f5ce66221ae208235ae21d4014b537002f3085c729fd67793a97ce8730", "urls": ["bzz-raw://ce914cf74f9d9d3fd73b8392318063d4b57c90e7b615990030d1afd1f2402368", "dweb:/ipfs/QmZvTGqYLwiBSQKDTKVEVAQN3mEBMuLefuUoAWrW6uPAP4"], "license": "BUSL-1.1"}, "contracts/interfaces/IOrderBook.sol": {"keccak256": "0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b", "urls": ["bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d", "dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68"], "license": "GPL-2.0-or-later"}, "contracts/libraries/Errors.sol": {"keccak256": "0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f", "urls": ["bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7", "dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu"], "license": "BUSL-1.1"}, "lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/StdAssertions.sol": {"keccak256": "0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43", "urls": ["bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3", "dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd", "urls": ["bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc", "dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8", "urls": ["bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a", "dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT"], "license": "MIT"}, "lib/forge-std/src/Test.sol": {"keccak256": "0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c", "urls": ["bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4", "dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xe2b159412b06b44a7f90972104300c587f308506d37d2143dd7e689e2eac6f01", "urls": ["bzz-raw://a96e13ac6fec3ffe61a55d6005ef0ef96f47adf3f4f3531e0418f0bf02d3f93c", "dweb:/ipfs/QmeFci69gm7a6c6pEqSNoe3HfXPVPiagUc51Pyo5PoS8Rn"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol": {"keccak256": "0xbf2aefe54b76d7f7bcd4f6da1080b7b1662611937d870b880db584d09cea56b5", "urls": ["bzz-raw://f5e7e2f12e0feec75296e57f51f82fdaa8bd1551f4b8cc6560442c0bf60f818c", "dweb:/ipfs/QmcW9wDMaQ8RbQibMarfp17a3bABzY5KraWe2YDwuUrUoz"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e", "urls": ["bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049", "dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0xa1ad192cd45317c788618bef5cb1fb3ca4ce8b230f6433ac68cc1d850fb81618", "urls": ["bzz-raw://b43447bb85a53679d269a403c693b9d88d6c74177dfb35eddca63abaf7cf110a", "dweb:/ipfs/QmXSDmpd4bNZj1PDgegr6C4w1jDaWHXCconC3rYiw9TSkQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0x20462ddb2665e9521372c76b001d0ce196e59dbbd989de9af5576cad0bd5628b", "urls": ["bzz-raw://f417fd12aeec8fbfaceaa30e3a08a0724c0bc39de363e2acf6773c897abbaf6d", "dweb:/ipfs/QmU4Hko6sApdweVM92CsiuLKkCk8HfyBeutF89PCTz5Tye"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0x6d0ae6e206645341fd122d278c2cb643dea260c190531f2f3f6a0426e77b00c0", "urls": ["bzz-raw://032d1201d839435be2c85b72e33206b3ea980c569d6ebf7fa57d811ab580a82f", "dweb:/ipfs/QmeqQjAtMvdZT2tG7zm39itcRJkuwu8AEReK6WRnLJ18DD"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "node_modules/solady/src/auth/Ownable.sol": {"keccak256": "0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3", "urls": ["bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3", "dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs"], "license": "MIT"}, "node_modules/solady/src/utils/ECDSA.sol": {"keccak256": "0x077d168511141c83fd93914f4609c5363341da03a3971cbc0d3f6a75e9893de0", "urls": ["bzz-raw://c218864adb4e97d81908318d9bd0ed75f40ec996c826141fab2646135820295b", "dweb:/ipfs/QmVQyWG6Ff4LAUnzj6JMEHHYfNBANVYLaMkun6TYaWShms"], "license": "MIT"}, "node_modules/solady/src/utils/EIP712.sol": {"keccak256": "0xb5c4c8ac5368c9785b4e30314f4ad6f3ae13bdc21679007735681d13da797bec", "urls": ["bzz-raw://c4456a4eaa8748f802fd1188db6405d18c452eb7c0dde84a49b49a7f94b5970d", "dweb:/ipfs/QmZzsFn4VwvBFy2MJVJXvntCQsDRCXbRrSKKfXxXv9jYGM"], "license": "MIT"}, "node_modules/solady/src/utils/Initializable.sol": {"keccak256": "0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec", "urls": ["bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559", "dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy"], "license": "MIT"}, "node_modules/solady/src/utils/UUPSUpgradeable.sol": {"keccak256": "0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e", "urls": ["bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16", "dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV"], "license": "MIT"}, "test/ETHStrandingBugPOC.t.sol": {"keccak256": "0x8034634705bee2f47030d55e7a9b69efece10fcd3edb9c1e685b39ad5a7c732d", "urls": ["bzz-raw://8068e0028a9e11860d60ab47b9dac4cb5b2f0c55945fd14c59edf84bbeb344b8", "dweb:/ipfs/QmegGBXZKreLboDWiFkxfmoyLnAsXKDJALMDnnszHMnVaH"], "license": "MIT"}}, "version": 1}, "id": 35}