{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "setUp", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "test_AccumulatedETHStranding", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_NoWithdrawalMechanism", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_executeMarginAccountRequest_StrandsExcessETH", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_executePriceDependent_StrandsExcessETH", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_execute_StrandsExcessETH", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}], "bytecode": {"object": "0x60808060405234602f57600160ff19600c541617600c55600160ff19601f541617601f5561492390816100348239f35b5f80fdfe60806040526004361015610011575f80fd5b5f5f3560e01c80630a9254e41461195b5780631ed7831c146118dd5780632ade3880146117265780633e5e3c23146116a85780633f7286f41461162a578063574f1055146111ad57806366d9a9a01461108c578063795a766814610c6e57806385226c8114610be45780638a2c190f146109a6578063916a17c6146108fe578063b0464fdc14610856578063b5508aa9146107cc578063b86e7578146101d4578063ba414fa6146101af578063db1c028f14610193578063e20c9f71146101055763fa7626d4146100e0575f80fd5b34610102578060031936011261010257602060ff601f54166040519015158152f35b80fd5b503461010257806003193601126101025760405180916020601554928381520191601582527f55f448fdea98c4d29eb340757ef0a66cd03dbb9538908a6a81d96026b71ec475915b8181106101745761017085610164818703826120d3565b60405191829182611f0e565b0390f35b82546001600160a01b031684526020909301926001928301920161014d565b50346101025780600319360112610102576101ac6125f5565b80f35b503461010257806003193601126101025760206101ca61254f565b6040519015158152f35b5034610102578060031936011261010257601f546022546001600160a01b0360089290921c82163191168031908390803b156107c8578180916044604051809481936349e4fff360e01b83526107d0600484015261083460248401525af180156107bd576107a4575b505060255460225442610e10810194926001600160a01b0390811692169085106107905760405191610120830183811067ffffffffffffffff82111761077c579087929160405283526020830190815261076c60408401526060830190670de0b6b3a76400008252608084019183835260a0850197885260c085016001815260e0860192631f67ea8f60e11b84526020966040516102db89826120d3565b8781526101008201526102ec612d82565b6103cb6103d98d8b60018060a01b038651169160018060a01b03885116908b8d60408a0151928d5191519051918d5115159363ffffffff60e01b905116946101008d015188815191012096604051988901997f1122e3187221cfd9d40ac63fa267c39b342d8710972523d6cbf343f43c0322f78b5260408a01526060890152608088015260a087015260c086015260e085015261010084015261012083015261014082015261014081526103a2610160826120d3565b5190206040519283918d83019586909160429261190160f01b8352600283015260228201520190565b03601f1981018352826120d3565b5190209060265491604051926338d07aa960e21b8452600484015260248301526060826044815f5160206148ce5f395f51905f525afa801561077157899089908a948b91610730575b50610461929161045391604051968794850191604193918352602083015260ff60f81b9060f81b1660408201520190565b03601f1981018452836120d3565b6024546001600160a01b03165f5160206148ce5f395f51905f523b1561072c576040519063ca669fa760e01b825260048201528881602481835f5160206148ce5f395f51905f525af1908115610721578991610708575b5050601f5460408051630df477d560e41b81526004810182905283516001600160a01b039081166044830152955186166064820152908301516084820152955160a4870152965160c48601529a5160e48501529151151561010484015292516001600160e01b031916610124830152610100909801516101206101448301529097889360081c9092169183916714d1120d7b16000091839161055f90610164840190611f50565b82810360031901602484015261057491611f50565b03925af19182156106fd576106456105ca61069b926106cd977f3d3d3d2045544820537472616e64696e6720696e206578656375746550726963966106dd575b50601f5460081c6001600160a01b0316316122f1565b94610633606096604051906105df89836120d3565b603b82527f457863657373204554482073686f756c6420626520737472616e64656420696e878301527f20666f7277617264657220285072696365446570656e64656e742900000000006040830152612b70565b6022546001600160a01b0316316122f1565b6040519061065386836120d3565b603182527f4f72646572426f6f6b2073686f756c64206f6e6c79207265636569766520726584830152701c5d5a5c99590811551208185b5bdd5b9d607a1b6040830152612bfc565b6106a860405193846120d3565b603083528201526f65446570656e64656e742829203d3d3d60801b6040820152612cb2565b6101ac6106d8612514565b612cf6565b6106f8903d808c833e6106f081836120d3565b8101906121f8565b6105b4565b6040513d87823e3d90fd5b81610712916120d3565b61071d57875f6104b8565b8780fd5b6040513d8b823e3d90fd5b8880fd5b610453955061046193925061075d915060603d60601161076a575b61075581836120d3565b8101906129c4565b9591909591509192610422565b503d61074b565b6040513d8a823e3d90fd5b634e487b7160e01b88526041600452602488fd5b634e487b7160e01b86526011600452602486fd5b816107ae916120d3565b6107b957825f61023d565b8280fd5b6040513d84823e3d90fd5b5080fd5b50346101025780600319360112610102576019546107e981612114565b916107f760405193846120d3565b818352601981527f944998273e477b495144fb8794c914197f3ccb46be2900f4698fd0ef743c9695602084015b83831061083957604051806101708782611fb1565b6001602081926108488561212c565b815201920192019190610824565b5034610102578060031936011261010257601c5461087381612114565b9161088160405193846120d3565b818352601c81527f0e4562a10381dec21b205ed72637e6b1b523bdd0e4d4d50af5cd23dd4500a211602084015b8383106108c357604051806101708782612010565b600260206001926040516108d681612087565b848060a01b0386541681526108ec858701612312565b838201528152019201920191906108ae565b5034610102578060031936011261010257601d5461091b81612114565b9161092960405193846120d3565b818352601d81527f6d4407e7be21f808e6509aa9fa9143369579dd7d760fe20a2c09680fc146134f602084015b83831061096b57604051806101708782612010565b6002602060019260405161097e81612087565b848060a01b038654168152610994858701612312565b83820152815201920192019190610956565b50346101025780600319360112610102576109bf6125f5565b601f5460405160089190911c6001600160a01b031680319160609190849084156109e985836120d3565b602682527f53686f756c64206861766520737472616e6465642045544820666f7220746869602083015280651cc81d195cdd60d21b6040840152610b65575b5050506101ac92610aa86b1d1a194818dbdb9d1c9858dd60a21b92318260405191610a5387846120d3565b603d83527f537472616e646564204554482073686f756c642072656d61696e20696e20636f60208401527f6e74726163742077697468206e6f2077617920746f207265636f7665720000006040840152612c5f565b610b27604091610aec8351610abd85826120d3565b601f81527f3d3d3d204e6f205769746864726177616c204d656368616e69736d203d3d3d006020820152612cb2565b8251610af884826120d3565b601981527f455448207065726d616e656e746c7920737472616e6465643a000000000000006020820152612d3f565b610b33815193846120d3565b602c83527f4e6f207265636f76657279206d656368616e69736d2065786973747320696e206020840152820152612cb2565b5f5160206148ce5f395f51905f523b156107b9576040805163a34edc0360e01b815291156004830152602482015290829082908190610ba8906044830190611f50565b03815f5160206148ce5f395f51905f525afa80156107bd57610bcb575b80610a28565b81610bd5916120d3565b610be057835f610bc5565b8380fd5b5034610102578060031936011261010257601a54610c0181612114565b91610c0f60405193846120d3565b818352601a81527f057c384a7d1c54f3a1b2e5e67b2617b8224fdfd1ea7234eea573a6ff665ff63e602084015b838310610c5157604051806101708782611fb1565b600160208192610c608561212c565b815201920192019190610c3c565b5034610102578060031936011261010257601f546021546025546001600160a01b039081169291811680319260081c909116319042610e10810190811061079057859060405195610cbe876120b7565b8652602086019283526103cb610dd96040880194670de0b6b3a76400008652606089019585875260808a0194855260a08a0196631f67ea8f60e11b885260209760405190610d0c8a836120d3565b88825260c08d01918252610d1e612d82565b978d60018060a01b039051169560018060a01b039051169451935190519163ffffffff60e01b90511692518b815191012093604051958c8701977f4a5f28af10183842f321d76044fae00f1697f0bc9a8eae077fe67857cc5d6404895260408801526060870152608086015260a085015260c084015260e08301526101008201526101008152610db0610120826120d3565b5190206040519283918783019586909160429261190160f01b8352600283015260228201520190565b51902060265490604051916338d07aa960e21b8352600483015260248201526060816044815f5160206148ce5f395f51905f525afa80156107bd57828390849261105f575b610e509293506103cb9060405194859388850191604193918352602083015260ff60f81b9060f81b1660408201520190565b6024546001600160a01b03165f5160206148ce5f395f51905f523b156107b9576040519063ca669fa760e01b825260048201528281602481835f5160206148ce5f395f51905f525af180156110545761103f575b50610edd9560018060a01b03601f5460081c16906714d1120d7b160000604051809981958294636e08490560e01b84526004840161226b565b03925af19182156106fd57610fad610f32611007926106cd977f3d3d3d2045544820537472616e64696e6720696e20657865637574654d617267966106dd5750601f5460081c6001600160a01b0316316122f1565b94610f9b60609660405190610f4789836120d3565b603a82527f457863657373204554482073686f756c6420626520737472616e64656420696e878301527f20666f7277617264657220284d617267696e4163636f756e74290000000000006040830152612b70565b6021546001600160a01b0316316122f1565b60405190610fbb86836120d3565b603582527f4d617267696e4163636f756e742073686f756c64206f6e6c7920726563656976848301527419481c995c5d5a5c99590811551208185b5bdd5b9d605a1b6040830152612bfc565b61101460405193846120d3565b6036835282015275696e4163636f756e74526571756573742829203d3d3d60501b6040820152612cb2565b61104a8380926120d3565b6107c8575f610ea4565b6040513d85823e3d90fd5b505050610e506110806103cb9260603d60601161076a5761075581836120d3565b91935090915082610e1e565b5034610102578060031936011261010257601b546110a981612114565b6110b660405191826120d3565b818152601b83526020810191837f3ad8aa4f87544323a9d1e5dd902f40c356527a7955687113db5f9a85ad579dc1845b83831061117257868587604051928392602084019060208552518091526040840160408260051b8601019392905b82821061112357505050500390f35b919360019193955060206111628192603f198a8203018652885190836111528351604084526040840190611f50565b9201519084818403910152611f74565b9601920192018594939192611114565b6002602060019260405161118581612087565b61118e8661212c565b815261119b858701612312565b838201528152019201920191906110e6565b503461010257806003193601126101025760018060a01b03601f5460081c163181610e104201918242116020925b600581106114c45785846111fd8560018060a01b03601f5460081c16316122f1565b908260405161120d6060826120d3565b603981527f537472616e646564204554482073686f756c6420616363756d756c617465206f838201527f766572206d756c7469706c65207472616e73616374696f6e730000000000000060408201526722b1c8c1227a00008403611443575b505f8084846112ba6040516112826060826120d3565b602181527f3d3d3d20416363756d756c617465642045544820537472616e64696e67203d3d83820152603d60f81b6040820152612cb2565b6113b5604092848061131f61133487516112d489826120d3565b601781527f4e756d626572206f66207472616e73616374696f6e733a000000000000000000888201528851632d839cb360e21b818a0152602481018a90529283916064830190611f50565b6005604483015203601f1981018352826120d3565b85815191016a636f6e736f6c652e6c6f675afa50611385845161135786826120d3565b601d81527f45544820737472616e64656420706572207472616e73616374696f6e3a00000085820152612cf6565b835161139185826120d3565b60138152722a37ba30b61022aa241039ba3930b73232b21d60691b84820152612d3f565b61142d8251926113c581856120d3565b601884527f457870656374656420746f74616c20737472616e6465643a0000000000000000838501526114118151948592632d839cb360e21b8685015260248401526064830190611f50565b6722b1c8c1227a0000604483015203601f1981018452836120d3565b815191016a636f6e736f6c652e6c6f675afa5080f35b5f5160206148ce5f395f51905f523b156107c8578161149291604051809381926388b44c8560e01b83528860048401526722b1c8c1227a00006024840152606060448401526064830190611f50565b03815f5160206148ce5f395f51905f525afa80156107bd571561126c57816114b9916120d3565b6107b957828461126c565b60255484546001600160a01b039182169291168361161657604051926114e9846120b7565b835285830152670de0b6b3a76400006040830152806060830152856080830152631f67ea8f60e11b60a083015260405161152386826120d3565b87815260c083015286611535836129e9565b6024546001600160a01b03165f5160206148ce5f395f51905f523b156107b9576040519063ca669fa760e01b825260048201528281602481835f5160206148ce5f395f51905f525af1801561105457611601575b506115c29360018060a01b03601f5460081c16906714d1120d7b16000060405180978195829463243f6cb760e11b84526004840161226b565b03925af19182156115f6576001926115dc575b50016111db565b6115ef903d808a833e6106f081836120d3565b505f6115d5565b6040513d89823e3d90fd5b61160c8380926120d3565b6107c8575f611589565b634e487b7160e01b88526011600452602488fd5b503461010257806003193601126101025760405180916020601754928381520191601782527fc624b66cc0138b8fabc209247f72d758e1cf3343756d543badbf24212bed8c15915b8181106116895761017085610164818703826120d3565b82546001600160a01b0316845260209093019260019283019201611672565b503461010257806003193601126101025760405180916020601854928381520191601882527fb13d2d76d1f4b7be834882e410b3e3a8afaf69f83600ae24db354391d2378d2e915b8181106117075761017085610164818703826120d3565b82546001600160a01b03168452602090930192600192830192016116f0565b5034610102578060031936011261010257601e5461174381612114565b61175060405191826120d3565b818152601e83526020810191837f50bb669a95c7b50b7e8a6f09454034b2b14cf2b85c730dca9a539ca82cb6e350845b8383106118545786858760405192839260208401906020855251809152604084019160408260051b8601019392815b8383106117bc5786860387f35b919395509193603f198782030183528551906020604082019260018060a01b0381511683520151916040602083015282518091526060820190602060608260051b850101940192855b828110611829575050505050602080600192970193019301909286959492936117af565b9091929394602080611847600193605f198782030189528951611f50565b9701950193929101611805565b60405161186081612087565b82546001600160a01b0316815260018301805461187c81612114565b9161188a60405193846120d3565b8183528a526020808b20908b9084015b8382106118c0575050505060019282602092836002950152815201920192019190611780565b6001602081926118cf8661212c565b81520193019101909161189a565b503461010257806003193601126101025760405180916020601654928381520191601682527fd833147d7dc355ba459fc788f669e58cfaf9dc25ddcd0702e87d69c7b5124289915b81811061193c5761017085610164818703826120d3565b82546001600160a01b0316845260209093019260019283019201611925565b5034611eeb575f366003190112611eeb57306001600160601b0360a01b6023541617602355604090815161198f83826120d3565b6007815260208101663932b630bcb2b960c91b8152835160076020820192835e5f6027820152600781526119c46027826120d3565b51902083519063ffa1864960e01b825260048201526020816024815f5160206148ce5f395f51905f525afa908115611ee1575f91611eef575b505f5160206148ce5f395f51905f523b15611eeb5783516318caf8e360e31b81526001600160a01b039091166004820181905260248201859052915f9082908190611a4c906044830190611f50565b0381835f5160206148ce5f395f51905f525af18015611ee157611ecc575b506001600160601b0360a01b60245416176024557f1234567890123456789012345678901234567890123456789012345678901234602655815163ffa1864960e01b81527f123456789012345678901234567890123456789012345678901234567890123460048201526020816024815f5160206148ce5f395f51905f525afa908115611ec2578291611e93575b5060018060a01b03166001600160601b0360a01b60255416176025558151607f8082019082821067ffffffffffffffff831117611e7f57908291612e288339039082f08015611e5f5760018060a01b03166001600160601b0360a01b60205416176020558151607f8082019082821067ffffffffffffffff831117611e7f57908291612ea78339039082f08015611e5f5760018060a01b03166001600160601b0360a01b602154161760215581516101268082019082821067ffffffffffffffff831117611e7f57908291612f268339039082f08015611e5f5760018060a01b03166001600160601b0360a01b602254161760225581516115e58082019082821067ffffffffffffffff831117611e7f5790829161304c8339039082f08015611e5f5782519061029d908183019183831067ffffffffffffffff841117611e6b5791839160609361463184396001600160a01b031681526020810186905285810185905203019082f08015611e5f57601f8054610100600160a81b03191660089290921b610100600160a81b031691909117908190558251909290611c9d82826120d3565b600181526020810193601f198301368637815115611e4b57631f67ea8f60e11b909452602354929384936001600160a01b0360089290921c821692911690823b15611e4757611d109285928386518096819582946306cce95f60e41b845260048401528960248401526044830190611f74565b03925af18015611e2657908391611e32575b50506024546001600160a01b03165f5160206148ce5f395f51905f523b15611e0a5781519063c88a5e6d60e01b82526004820152678ac7230489e8000060248201528281604481835f5160206148ce5f395f51905f525af18015611e2657908391611e0e575b50506025546001600160a01b0316905f5160206148ce5f395f51905f523b15611e0a5780519163c88a5e6d60e01b83526004830152678ac7230489e8000060248301528282604481835f5160206148ce5f395f51905f525af1908115611e015750611df05750f35b81611dfa916120d3565b6101025780f35b513d84823e3d90fd5b5050fd5b81611e18916120d3565b611e2357815f611d88565b50fd5b505051903d90823e3d90fd5b81611e3c916120d3565b611e2357815f611d22565b8480fd5b634e487b7160e01b84526032600452602484fd5b509051903d90823e3d90fd5b634e487b7160e01b85526041600452602485fd5b634e487b7160e01b84526041600452602484fd5b611eb5915060203d602011611ebb575b611ead81836120d3565b8101906120f5565b5f611af8565b503d611ea3565b83513d84823e3d90fd5b611ed99192505f906120d3565b5f905f611a6a565b84513d5f823e3d90fd5b5f80fd5b611f08915060203d602011611ebb57611ead81836120d3565b5f6119fd565b60206040818301928281528451809452019201905f5b818110611f315750505090565b82516001600160a01b0316845260209384019390920191600101611f24565b805180835260209291819084018484015e5f828201840152601f01601f1916010190565b90602080835192838152019201905f5b818110611f915750505090565b82516001600160e01b031916845260209384019390920191600101611f84565b602081016020825282518091526040820191602060408360051b8301019401925f915b838310611fe357505050505090565b9091929394602080612001600193603f198682030187528951611f50565b97019301930191939290611fd4565b602081016020825282518091526040820191602060408360051b8301019401925f915b83831061204257505050505090565b9091929394602080612078600193603f198682030187526040838b51878060a01b03815116845201519181858201520190611f74565b97019301930191939290612033565b6040810190811067ffffffffffffffff8211176120a357604052565b634e487b7160e01b5f52604160045260245ffd5b60e0810190811067ffffffffffffffff8211176120a357604052565b90601f8019910116810190811067ffffffffffffffff8211176120a357604052565b90816020910312611eeb57516001600160a01b0381168103611eeb5790565b67ffffffffffffffff81116120a35760051b60200190565b90604051915f8154908160011c92600183169283156121ee575b6020851084146121da5784875286939081156121b85750600114612174575b50612172925003836120d3565b565b90505f9291925260205f20905f915b81831061219c575050906020612172928201015f612165565b6020919350806001915483858901015201910190918492612183565b90506020925061217294915060ff191682840152151560051b8201015f612165565b634e487b7160e01b5f52602260045260245ffd5b93607f1693612146565b602081830312611eeb5780519067ffffffffffffffff8211611eeb570181601f82011215611eeb5780519067ffffffffffffffff82116120a3576040519261224a601f8401601f1916602001856120d3565b82845260208383010111611eeb57815f9260208093018386015e8301015290565b90916122e060c06122ee946040855260018060a01b03815116604086015260018060a01b03602082015116606086015260408101516080860152606081015160a086015260808101518286015263ffffffff60e01b60a08201511660e0860152015160e0610100850152610120840190611f50565b916020818403910152611f50565b90565b919082039182116122fe57565b634e487b7160e01b5f52601160045260245ffd5b90604051918281549182825260208201905f5260205f20925f905b80600783011061246f57612172945491818110612450575b818110612431575b818110612412575b8181106123f3575b8181106123d4575b8181106123b5575b818110612398575b10612383575b5003836120d3565b6001600160e01b03191681526020015f61237b565b602083811b6001600160e01b031916855290930192600101612375565b604083901b6001600160e01b031916845260209093019260010161236d565b606083901b6001600160e01b0319168452602090930192600101612365565b608083901b6001600160e01b031916845260209093019260010161235d565b60a083901b6001600160e01b0319168452602090930192600101612355565b60c083901b6001600160e01b031916845260209093019260010161234d565b60e083901b6001600160e01b0319168452602090930192600101612345565b916008919350610100600191865463ffffffff60e01b8160e01b16825263ffffffff60e01b8160c01b16602083015263ffffffff60e01b8160a01b16604083015263ffffffff60e01b8160801b16606083015263ffffffff60e01b8160601b16608083015263ffffffff60e01b8160401b1660a083015263ffffffff60e01b8160201b1660c083015263ffffffff60e01b1660e082015201940192018592939161232d565b604051906125236040836120d3565b601a82527f45544820737472616e64656420696e20666f727761726465723a0000000000006020830152565b60085460ff161561255f57600190565b604051630667f9d760e41b81525f5160206148ce5f395f51905f5260048201526519985a5b195960d21b60248201526020816044815f5160206148ce5f395f51905f525afa9081156125ea575f916125b8575b50151590565b90506020813d6020116125e2575b816125d3602093836120d3565b81010312611eeb57515f6125b2565b3d91506125c6565b6040513d5f823e3d90fd5b601f546020546024546025545f94936001600160a01b03918216939282169260089190911c821631918331911680319042610e108101919082106122fe5760405196612640886120b7565b87526020870152670de0b6b3a764000060408701525f60608701526080860152631f67ea8f60e11b60a086015260209360405161267d86826120d3565b5f815260c087015261268e866129e9565b905f5160206148ce5f395f51905f523b15611eeb576040519063ca669fa760e01b825260048201525f81602481835f5160206148ce5f395f51905f525af180156125ea576129ad575b50876127129160018060a01b03601f5460081c166714d1120d7b160000988960405180968195829463243f6cb760e11b84526004840161226b565b03925af18015610771576128bf947f457863657373204554482073686f756c6420626520737472616e64656420696e946127bd6127c28c9661287896612815966121729e9f50612995575b50601f548b546024546001600160a01b03908116319791811631926127bd928f9261278c9260081c16316122f1565b60609c8d9261279e60405194856120d3565b602a845283015269103337b93bb0b93232b960b11b6040830152612b70565b6122f1565b604051906127d089836120d3565b602e82527f5461726765742073686f756c64206f6e6c7920726563656976652072657175698b8301526d1c99590811551208185b5bdd5b9d60921b6040830152612bfc565b866040519161282486846120d3565b603a83527f52656c617965722073686f756c64206c6f73652066756c6c20616d6f756e7420888401527f73656e7420286e6f20726566756e64206d656368616e69736d290000000000006040840152612c5f565b5061288660405191826120d3565b602281527f3d3d3d2045544820537472616e64696e6720696e20657865637574652829203d83820152613d3d60f01b6040820152612cb2565b6128ca6106d8612514565b7f546f74616c20455448206c6f73742062792072656c617965723a0000000000006040915f8061294d612969865161290288826120d3565b601781527f4554482070726f7065726c7920666f727761726465643a000000000000000000868201528751632d839cb360e21b81880152602481018990529283916064830190611f50565b670de0b6b3a7640000604483015203601f1981018352826120d3565b83815191016a636f6e736f6c652e6c6f675afa50612989835193846120d3565b601a8352820152612d3f565b6129a8903d808b833e6106f081836120d3565b61275d565b6129ba9198505f906120d3565b5f966127126126d7565b90816060910312611eeb57805160ff81168103611eeb57916040602083015192015190565b6103cb612ac86129f7612d82565b9260018060a01b038151169060018060a01b0360208201511690604081015190606081015160808201519060c063ffffffff60e01b60a08501511693015160208151910120936040519560208701977f199c879f0d8420ea1ff045984f2ac5541f91073952f48791833c79f1bea4534e895260408801526060870152608086015260a085015260c084015260e08301526101008201526101008152612a9e610120826120d3565b519020604051928391602083019586909160429261190160f01b8352600283015260228201520190565b51902060265490604051916338d07aa960e21b8352600483015260248201526060816044815f5160206148ce5f395f51905f525afa80156125ea575f5f905f92612b43575b60408051602081019390935282019290925260f89190911b6001600160f81b031916606082015290506122ee81606181016103cb565b5050506122ee612b646103cb9260603d60601161076a5761075581836120d3565b91935090915082612b0d565b6706f05b59d3b200008103612b83575050565b5f5160206148ce5f395f51905f523b15611eeb57612bd3915f9160405193849283926388b44c8560e01b845260048401526706f05b59d3b200006024840152606060448401526064830190611f50565b03815f5160206148ce5f395f51905f525afa80156125ea57612bf25750565b5f612172916120d3565b670de0b6b3a76400008103612c0f575050565b5f5160206148ce5f395f51905f523b15611eeb57612bd3915f9160405193849283926388b44c8560e01b84526004840152670de0b6b3a76400006024840152606060448401526064830190611f50565b90808203612c6c57505050565b5f5160206148ce5f395f51905f523b15611eeb575f91612bd360405194859384936388b44c8560e01b855260048501526024840152606060448401526064830190611f50565b5f6103cb612cdf829360405192839163104c13eb60e21b6020840152602060248401526044830190611f50565b6020815191016a636f6e736f6c652e6c6f675afa50565b5f612d23612cdf8293604051928391632d839cb360e21b6020840152604060248401526064830190611f50565b6706f05b59d3b20000604483015203601f1981018352826120d3565b612d6e612cdf5f93928493604051938492632d839cb360e21b6020850152604060248501526064840190611f50565b90604483015203601f1981018352826120d3565b60018060a01b03601f5460081c1660405160208101917f8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f83527f4b9364a5f1fa97fe478bc1fdd48051059c48ef2be4680dd95464d0d7d921f64360408301527f06c015bd22b4c69690933c1058878ebdfef31f9aaae40bbe86d8a09fe1b2972c606083015246608083015260a082015260a08152612e2160c0826120d3565b5190209056fe608080604052346013576067908160188239f35b5f80fdfe60043610156013575b36156011575f80fd5b005b5f3560e01c633ecfd51e036008575f366003190112602d57005b5f80fdfea2646970667358221220248315d66241d0ccbb8660992a0b62e78b5cc201b1b84b6206f902c1d72053ca64736f6c634300081c0033608080604052346013576067908160188239f35b5f80fdfe60043610156013575b36156011575f80fd5b005b5f3560e01c633ecfd51e036008575f366003190112602d57005b5f80fdfea2646970667358221220b1eb37b6a5d3bd8652fc7a24641b8d8a128bb706d8a2d8c05f8efa3ae058c09764736f6c634300081c00336080806040523460155761010c908161001a8239f35b5f80fdfe6080806040526004361015601a575b5036156018575f80fd5b005b5f3560e01c90816320c1614b1460bf575080633ecfd51e1460b357806349e4fff3146097578063b4de8b701460765763cffb7d15146057575f600e565b346072575f3660031901126072576020600154604051908152f35b5f80fd5b346072575f36600319011260725760405f5460015482519182526020820152f35b3460725760403660031901126072576004355f55602435600155005b5f366003190112607257005b346072575f3660031901126072576020905f548152f3fea264697066735822122076195113ee9c17aa898717b32ea53e7ab4e6613ba3a9c814dc4b1ddcdd7448a264736f6c634300081c0033610140604052346100d657306080524660a0525f60c0525f60e0525f61010052306101205263409feecd1954600181166100c95760011c6002600160401b031901610084575b60405161150a90816100db823960805181505060a05181505060c05181505060e051815050610100518150506101205181818161087d01526109100152f35b6002600160411b0363409feecd19556001600160401b0360209081527fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d29080a1610045565b63f92ee8a95f526004601cfd5b5f80fdfe60806040526004361015610011575f80fd5b5f3560e01c806310012a1514610b8e5780631691267114610b5f5780631a4e971f14610ae65780631fbe2e4f14610aa75780632569296214610a5e5780632d0335ab14610a265780633bcb61ef14610a0d578063487ed96e146109f85780634f1ef286146108d857806352d1902d1461086a57806354d1f13d146108265780635b31c2e6146107b15780636b446f49146107985780636cce95f0146106595780636e0849051461059b578063715018a61461054e57806384b0196e146104b05780638da5cb5b14610484578063950b2e511461046b578063df477d5014610204578063e6bc6b56146101e1578063f04e283e14610194578063f2fde38b146101575763fee81cf414610121575f80fd5b346101535760203660031901126101535761013a610d14565b63389a75e1600c525f52602080600c2054604051908152f35b5f80fd5b60203660031901126101535761016b610d14565b61017361132a565b8060601b156101875761018590611446565b005b637448fbae5f526004601cfd5b6020366003190112610153576101a8610d14565b6101b061132a565b63389a75e1600c52805f526020600c2090815442116101d4575f6101859255611446565b636f5e88185f526004601cfd5b346101535760206101fa6101f436610d2a565b91611255565b6040519015158152f35b61021961021036610d9c565b90829392611043565b1561045c5760608101359081341061044d5760e08101916001600160e01b031961024284610f3c565b165f525f60205260ff60405f2054161561043e5761025f82610e13565b60405160609190911b6bffffffffffffffffffffffff19166020820190815260808401356034830152906102a081605481015b03601f198101835282610c01565b5190205f52600160205260405f20600160ff19825416179055602082016004604060018060a01b036102d184610e13565b16815192838092630b4de8b760e41b82525afa908115610433575f91610401575b5060c0840161030081611000565b90816103f2575b81156103ce575b50156103b3575f8086868661034b61035c61033161032b8a610e13565b95610f3c565b93610292610343610100830183610f51565b949092610e13565b604051948593602085019889610f83565b51925af1610368610fd1565b901561038a5761038690604051918291602083526020830190610d78565b0390f35b6040516315fcd67560e01b8152602060048201529081906103af906024830190610d78565b0390fd5b8360409163eaac67f160e01b5f52600452013560245260445ffd5b6103d89150611000565b15806103e5575b8661030e565b50806040850135116103df565b90508160408601351090610307565b90506040813d60401161042b575b8161041c60409383610c01565b810103126101535751856102f2565b3d915061040f565b6040513d5f823e3d90fd5b63d02b530d60e01b5f5260045ffd5b63044044a560e21b5f5260045ffd5b630e751dc360e31b5f5260045ffd5b346101535760206101fa61047e36610d2a565b9161113f565b34610153575f36600319011261015357638b78c6d819546040516001600160a01b039091168152602090f35b34610153575f366003190112610153576104ef6104fd6104ce611489565b604092919251938493600f60f81b855260e0602086015260e0850190610d78565b908382036040850152610d78565b4660608301523060808301525f60a083015281810360c083015260206060519182815201906080905f5b818110610535575050500390f35b8251845285945060209384019390920191600101610527565b5f3660031901126101535761056161132a565b5f638b78c6d819547f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08280a3600160ff1b638b78c6d81955005b6105b06105a736610d2a565b90829392611255565b1561045c5760408101359081341061044d5760a081016001600160e01b03196105d882610f3c565b165f525f60205260ff60405f2054161561043e5760608201359160018301809311610645575f9384936001600160a01b0361061284610e13565b1685526002602052604085205561034b61035c61063461032b60208601610e13565b9361029261034360c0830183610f51565b634e487b7160e01b5f52601160045260245ffd5b3461015357604036600319011261015357610672610d14565b6024356001600160401b03811161015357610691903690600401610c36565b63409feecd19918254806003855561076f575b50638b78c6d81954610762576001600160a01b0316801560ff1b8117638b78c6d819555f7f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08180a35f5b82825182101561072b57506001906001600160e01b031961070f8285610deb565b51165f525f60205260405f208260ff19825416179055016106ee565b8061073257005b6002905560016020527fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d2602080a1005b630dc149f05f526004601cfd5b600181819593951c14303b101561078b5760ff1b1b91836106a4565b63f92ee8a95f526004601cfd5b346101535760206101fa6107ab36610d9c565b91611043565b34610153576020366003190112610153576004356001600160401b038111610153576107e1903690600401610c36565b6107e961132a565b5f5b8151811015610185576001906001600160e01b031961080a8285610deb565b51165f525f60205260405f208260ff19825416179055016107eb565b5f3660031901126101535763389a75e1600c52335f525f6020600c2055337ffa7b8eab7da67f412cc9575ed43464468f9bfbae89d1675917346ca6d8fe3c925f80a2005b34610153575f36600319011261015357307f0000000000000000000000000000000000000000000000000000000000000000036108cb5760206040517f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc8152f35b639f03a0265f526004601cfd5b6040366003190112610153576108ec610d14565b6024356001600160401b0381116101535761090b903690600401610cad565b9091307f0000000000000000000000000000000000000000000000000000000000000000146108cb5761093c61132a565b60018060a01b03166352d1902d6001527f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc602060016004601d855afa51036109ea57807fbc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3b5f80a281817f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc556109cd57005b815f926040519485378338925af4156109e257005b3d5f823e3d90fd5b6355299b496001526004601dfd5b6105b0610a0436610d2a565b9082939261113f565b346101535760206101fa610a2036610cda565b91610e27565b34610153576020366003190112610153576001600160a01b03610a47610d14565b165f526002602052602060405f2054604051908152f35b5f3660031901126101535763389a75e1600c52335f526202a30042016020600c2055337fdbf36a107da19e49527a7176a1babf963b4b0ff8cde35ee35d6cd8f1f9ac7e1d5f80a2005b346101535760203660031901126101535760043563ffffffff60e01b8116809103610153575f525f602052602060ff60405f2054166040519015158152f35b3461015357610b00610af736610cda565b90829392610e27565b1561045c57610292610b42610b1483610e13565b6040519283916020808401960135908690916034926001600160601b03199060601b16825260148201520190565b5190205f52600160205260405f20600160ff198254161790555f80f35b34610153576020366003190112610153576004355f526001602052602060ff60405f2054166040519015158152f35b34610153576020366003190112610153576004356001600160401b03811161015357610bbe903690600401610c36565b610bc661132a565b5f5b8151811015610185576001906001600160e01b0319610be78285610deb565b51165f525f60205260405f2060ff19815416905501610bc8565b90601f801991011681019081106001600160401b03821117610c2257604052565b634e487b7160e01b5f52604160045260245ffd5b81601f82011215610153578035916001600160401b038311610c22578260051b9160405193610c686020850186610c01565b845260208085019382010191821161015357602001915b818310610c8c5750505090565b82356001600160e01b03198116810361015357815260209283019201610c7f565b9181601f84011215610153578235916001600160401b038311610153576020838186019501011161015357565b906003198201608081126101535760601361015357600491606435906001600160401b03821161015357610d1091600401610cad565b9091565b600435906001600160a01b038216820361015357565b906040600319830112610153576004356001600160401b0381116101535760e081840360031901126101535760040191602435906001600160401b03821161015357610d1091600401610cad565b805180835260209291819084018484015e5f828201840152601f01601f1916010190565b906040600319830112610153576004356001600160401b0381116101535761012081840360031901126101535760040191602435906001600160401b03821161015357610d1091600401610cad565b8051821015610dff5760209160051b010190565b634e487b7160e01b5f52603260045260245ffd5b356001600160a01b03811681036101535790565b90604082013592834211610f2d57610eab91610ea6610e4585610e13565b6020860135966040519060208201927f3ec94f0b764242014d547a29ab20da6056912fc1b658b4ad364cd66f407a8005845260018060a01b03166040830152886060830152608082015260808152610e9e60a082610c01565b519020611346565b6113bb565b91610292610ee6610ebb84610e13565b9260405192839160208301958690916034926001600160601b03199060601b16825260148201520190565b5190205f52600160205260ff60405f205416610f1f576001600160a01b0390610f0e90610e13565b6001600160a01b0390921691161490565b623f613760e71b5f5260045ffd5b631ab7da6b60e01b5f5260045ffd5b356001600160e01b0319811681036101535790565b903590601e198136030182121561015357018035906001600160401b0382116101535760200191813603831361015357565b6001600160e01b0319909116815260189392918290600483013701906001600160601b03199060601b1660048201520190565b6001600160401b038111610c2257601f01601f191660200190565b3d15610ffb573d90610fe282610fb6565b91610ff06040519384610c01565b82523d5f602084013e565b606090565b3580151581036101535790565b92919261101982610fb6565b916110276040519384610c01565b829481845281830111610153578281602093845f960137010152565b9060a082013592834211610f2d57610eab91610ea661106185610e13565b61106d60208701610e13565b60808701359761107f60c08901611000565b61108b60e08a01610f3c565b906110a461109d6101008c018c610f51565b369161100d565b60208151910120926040519460208601967f1122e3187221cfd9d40ac63fa267c39b342d8710972523d6cbf343f43c0322f7885260018060a01b0316604087015260018060a01b0316606086015260408b0135608086015260608b013560a08601528b60c086015260e0850152151561010084015263ffffffff60e01b166101208301526101408201526101408152610e9e61016082610c01565b9190608083013591824211610f2d5761121491610ea661115e86610e13565b61116a60208801610e13565b60608801359661117c60a08a01610f3c565b61118c61109d60c08c018c610f51565b60208151910120916040519360208501957f199c879f0d8420ea1ff045984f2ac5541f91073952f48791833c79f1bea4534e875260018060a01b0316604086015260018060a01b0316606085015260408b013560808501528960a085015260c084015263ffffffff60e01b1660e08301526101008201526101008152610e9e61012082610c01565b906001600160a01b0361122684610e13565b165f52600260205260405f20541115918261124057505090565b9091506001600160a01b0390610f0e90610e13565b9190608083013591824211610f2d5761121491610ea661127486610e13565b61128060208801610e13565b60608801359661129260a08a01610f3c565b6112a261109d60c08c018c610f51565b60208151910120916040519360208501957f4a5f28af10183842f321d76044fae00f1697f0bc9a8eae077fe67857cc5d6404875260018060a01b0316604086015260018060a01b0316606085015260408b013560808501528960a085015260c084015263ffffffff60e01b1660e08301526101008201526101008152610e9e61012082610c01565b638b78c6d81954330361133957565b6382b429005f526004601cfd5b60a0611350611489565b90602081519101209060208151910120604051917f8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f835260208301526040820152466060820152306080820152206719010000000000005f52601a52603a526042601820905f603a52565b929190600191604051945f526040811461141b576041146114015750506020600160805f805b5afa51913d156113f4575f606052604052565b638baa579f5f526004601cfd5b60805f602093604084816001960135841a875281376113e1565b5060805f6020938385600195013590601b8260ff1c01875235604052848060ff1b03166060526113e1565b60018060a01b031680638b78c6d819547f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e05f80a3801560ff1b17638b78c6d81955565b60409081516114988382610c01565b600d81526c25bab93aa337b93bb0b93232b960991b6020820152916114bf81519182610c01565b60058152640312e302e360dc1b60208201529056fea264697066735822122025a7e3a0dac83d99c2251871374b6d597021638e3d56a231206e91c42f04d7a564736f6c634300081c0033608060405261029d8038038061001481610168565b92833981016040828203126101645781516001600160a01b03811692909190838303610164576020810151906001600160401b03821161016457019281601f8501121561016457835161006e610069826101a1565b610168565b9481865260208601936020838301011161016457815f926020809301865e86010152823b15610152577f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc80546001600160a01b031916821790557fbc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3b5f80a282511561013a575f8091610122945190845af43d15610132573d91610113610069846101a1565b9283523d5f602085013e6101bc565b505b6040516082908161021b8239f35b6060916101bc565b50505034156101245763b398979f60e01b5f5260045ffd5b634c9c8ce360e01b5f5260045260245ffd5b5f80fd5b6040519190601f01601f191682016001600160401b0381118382101761018d57604052565b634e487b7160e01b5f52604160045260245ffd5b6001600160401b03811161018d57601f01601f191660200190565b906101e057508051156101d157602081519101fd5b63d6bda27560e01b5f5260045ffd5b81511580610211575b6101f1575090565b639996b31560e01b5f9081526001600160a01b0391909116600452602490fd5b50803b156101e956fe60806040527f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc545f9081906001600160a01b0316368280378136915af43d5f803e156048573d5ff35b3d5ffdfea26469706673582212203e6cdd77f38134cb22407c0ed516e59fee35a8bf35428e8e0f574c7b0909678a64736f6c634300081c00330000000000000000000000007109709ecfa91a80626ff3989d68f67f5b1dd12da2646970667358221220a60f7129e382a58c67a94960ba6496a8314ebf815d5c2f36f7e6d0407084735e64736f6c634300081c0033", "sourceMap": "1107:13413:35:-:0;;;;;;;3166:4:5;1107:13413:35;;3166:4:5;1107:13413:35;;;3166:4:5;1107:13413:35;3166:4:5;1107:13413:35;;1087:4:16;1107:13413:35;;;1087:4:16;1107:13413:35;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1107:13413:35:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1065:26:16;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2723:18:9;1107:13413:35;;;;;;;2723:18:9;1107:13413:35;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;2024:14:3;;-1:-1:-1;;;;;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;7478:13;2024:14:3;7552:13:35;1107:13413;-1:-1:-1;;;;;2024:14:3;;;;;1107:13413:35;;7470:30;;1107:13413;7544:30;;;;;7640:39;;;;;1107:13413;;;7640:39;1107:13413;;2024:14:3;;;;;;;7640:39:35;;7668:4;1107:13413;7640:39;;2024:14:3;7674:4:35;1107:13413;;;2024:14:3;7640:39:35;;;;;;;;1107:13413;-1:-1:-1;;7824:4:35;2024:14:3;7552:13:35;1107:13413;8021:15;8039:7;1457;;;1107:13413;-1:-1:-1;;;;;1107:13413:35;;;;;;1457:7;-1:-1:-1;1457:7:35;;1107:13413;;;;;;;;;;;;;;;;;;;;;;;;7768:443;;1107:13413;;;7893:4;1107:13413;7768:443;;1107:13413;7768:443;;;1107:13413;1457:7;1107:13413;;7768:443;;;1107:13413;;;;7768:443;;;1107:13413;;;7768:443;;;8074:4;1107:13413;;;7768:443;;3195:30;;;;2024:14:3;;1107:13413:35;;;;;;;;:::i;:::-;;;;7768:443;;;1107:13413;13655:23;;:::i;:::-;14045:57;;1107:13413;;;;;;;1832:128;;1107:13413;;;;;;;1832:128;;1107:13413;7768:443;;;1107:13413;7768:443;;1832:128;;;;;;;;2216:167;;;1107:13413;;;;;;1832:128;;1107:13413;7768:443;;;;13988:8;1107:13413;;;;;13978:19;1107:13413;;;13719:288;;;2024:14:3;2216:167:35;2024:14:3;;1107:13413:35;2216:167;;1107:13413;7768:443;2216:167;;1107:13413;7768:443;2216:167;;2024:14:3;7768:443:35;2216:167;;2024:14:3;7768:443:35;2216:167;;2024:14:3;1107:13413:35;2216:167;;2024:14:3;7768:443:35;2216:167;;1107:13413;;2216:167;;1107:13413;2216:167;;;2024:14:3;2216:167:35;13719:288;;;;;;:::i;:::-;1107:13413;13709:299;;1107:13413;;14045:57;;;;;;;;1832:128;;;;;;;;;;;;2024:14:3;1832:128:35;;;2024:14:3;1832:128:35;;;14045:57;;1107:13413;;14045:57;;;;;;:::i;:::-;1107:13413;14035:68;;2024:14:3;14156::35;2024::3;1107:13413:35;;;2024:14:3;;;;14148:31:35;;1107:13413;14148:31;;2024:14:3;1107:13413:35;1832:128;;2024:14:3;7768:443:35;14148:31;7640:39;14148:31;-1:-1:-1;;;;;;;;;;;14148:31:35;;;;;;;;;1107:13413;;;;14148:31;;;1107:13413;;14196:25;1107:13413;;14196:25;1107:13413;;;14196:25;;;;;1832:128;;;;2024:14:3;;1832:128:35;;;2024:14:3;;1832:128:35;;;;;;;;;;;;;14196:25;;1107:13413;;14196:25;;;;;;:::i;:::-;1107:13413;2024:14:3;-1:-1:-1;;;;;1107:13413:35;-1:-1:-1;;;;;;;;;;;8305:17:35;;;;1107:13413;;2024:14:3;;;;8305:17:35;;1107:13413;8305:17;;1107:13413;8305:17;;1107:13413;8305:17;;-1:-1:-1;;;;;;;;;;;8305:17:35;;;;;;;;;;;1107:13413;-1:-1:-1;;7478:13:35;2024:14:3;1107:13413:35;;;-1:-1:-1;;;8332:70:35;;1107:13413;8332:70;;1107:13413;;;;;-1:-1:-1;;;;;1107:13413:35;;;7640:39;1107:13413;;;;;;;;;;;7768:443;;;1107:13413;;;;2024:14:3;1107:13413:35;;;;;2024:14:3;1107:13413:35;;;;;2024:14:3;1107:13413:35;;2024:14:3;1107:13413:35;;2024:14:3;1107:13413:35;;;;;;;;;;-1:-1:-1;;;;;;1107:13413:35;;;;;7768:443;;;;1107:13413;;;;;;;;;;2024:14:3;;1107:13413:35;;;;;;1457:7;;1107:13413;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;1107:13413:35;;;;;;;;:::i;:::-;8332:70;;;;;;;;;8715:56;8507;8693:181;8332:70;8893:63;8332:70;1107:13413;8332:70;;;1107:13413;-1:-1:-1;7478:13:35;2024:14:3;;;-1:-1:-1;;;;;1107:13413:35;8507:30;:56;:::i;:::-;1107:13413;8485:189;7768:443;1107:13413;;;;;;;;:::i;:::-;;;;;;;;;;;;;;8485:189;:::i;:::-;7552:13;1107:13413;-1:-1:-1;;;;;1107:13413:35;8715:30;:56;:::i;:::-;1107:13413;;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;1107:13413:35;;;;8693:181;:::i;:::-;1107:13413;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;1107:13413:35;;;;8893:63;:::i;:::-;8966:55;1107:13413;;:::i;:::-;8966:55;:::i;8332:70::-;;;;;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;1107:13413;;2024:14:3;1107:13413:35;;2024:14:3;;;;8305:17:35;;;;;:::i;:::-;1107:13413;;8305:17;;;;1107:13413;;;;8305:17;1107:13413;;2024:14:3;1107:13413:35;;2024:14:3;;;;8305:17:35;1107:13413;;;14148:31;14196:25;14148:31;;14196:25;14148:31;;;;;;7768:443;14148:31;7768:443;14148:31;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;;;;;;;;;;1107:13413;;2024:14:3;1107:13413:35;;2024:14:3;;;;1107:13413:35;-1:-1:-1;;;1107:13413:35;;;;;;;;1457:7;-1:-1:-1;;;1502:9:35;;;1107:13413;1502:9;1107:13413;;1502:9;7640:39;;;;;:::i;:::-;1107:13413;;7640:39;;;;1107:13413;;;;7640:39;1107:13413;;2024:14:3;1107:13413:35;;2024:14:3;;;;7640:39:35;1107:13413;;;;;;;;;;;;;;;;2575:18:9;1107:13413:35;2024:14:3;;;:::i;:::-;1107:13413:35;;;;;;;:::i;:::-;2024:14:3;;;2575:18:9;1107:13413:35;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;2876:18:9;1107:13413:35;2024:14:3;;;:::i;:::-;1107:13413:35;;;;;;;:::i;:::-;2024:14:3;;;2876:18:9;1107:13413:35;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;2024:14:3;;1107:13413:35;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3653:18:9;1107:13413:35;2024:14:3;;;:::i;:::-;1107:13413:35;;;;;;;:::i;:::-;2024:14:3;;;3653:18:9;1107:13413:35;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;2024:14:3;;1107:13413:35;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;10900:1115;;:::i;:::-;11072:13;2024:14:3;1107:13413:35;;2024:14:3;;;;;-1:-1:-1;;;;;1107:13413:35;11064:30;;;1107:13413;;;;;11115:18;;1107:13413;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;;;1107:13413:35;;;;1976:60:4;;1107:13413:35;11663:30;;;11949:59;11663:30;11641:167;-1:-1:-1;;;11663:30:35;;1107:13413;;;;;;;;:::i;:::-;;;;;;;;;;;;;;11641:167;:::i;:::-;11883:56;1107:13413;;11827:46;1107:13413;;;;;;:::i;:::-;11072:13;1107:13413;;;;;;;11827:46;:::i;:::-;1107:13413;;;;;;:::i;:::-;;;;;;;;;11883:56;:::i;:::-;1107:13413;;;;;;:::i;:::-;;;;;;;;;;;;11949:59;:::i;1976:60:4:-;-1:-1:-1;;;;;;;;;;;2001:24:4;;;;1107:13413:35;;;-1:-1:-1;;;2001:24:4;;11115:18:35;;1107:13413;2001:24:4;;1107:13413:35;;;;;;;;;;;;;;;;;;;:::i;:::-;2001:24:4;;-1:-1:-1;;;;;;;;;;;2001:24:4;;;;;;;;1976:60;;;;2001:24;;;;;:::i;:::-;1107:13413:35;;2001:24:4;;;;1107:13413:35;;;;;;;;;;;;;;;;;3162:18:9;1107:13413:35;2024:14:3;;;:::i;:::-;1107:13413:35;;;;;;;:::i;:::-;2024:14:3;;;3162:18:9;1107:13413:35;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;5948:13;2024:14:3;6026:17:35;1107:13413;6174:4;2024:14:3;-1:-1:-1;;;;;1107:13413:35;;;;;;;6018:34;;;2024:14:3;;1107:13413:35;;;5940:30;;6314:15;6332:7;1457;;;;-1:-1:-1;1457:7:35;;1107:13413;;;;;;;;:::i;:::-;;;6119:307;;;1107:13413;;;13281:57;;1107:13413;6119:307;;1107:13413;1457:7;1107:13413;;6119:307;;;1107:13413;;;;6119:307;;;1107:13413;;;6119:307;;;3195:30;;;;2024:14:3;;1107:13413:35;;;;;;;;;:::i;:::-;;;;6119:307;;;1107:13413;;;12938:23;;:::i;:::-;1107:13413;;;;;;;1832:128;;1107:13413;;;;;;;1832:128;;1107:13413;1832:128;;;;;;1107:13413;;;;1832:128;;1107:13413;13224:8;;1107:13413;;;;;13214:19;1107:13413;;;13002:241;;;;;2017:141;2024:14:3;;1107:13413:35;1832:128;;1107:13413;6119:307;1832:128;;1107:13413;6119:307;1832:128;;2024:14:3;6119:307:35;1832:128;;2024:14:3;6119:307:35;1832:128;;2024:14:3;1107:13413:35;1832:128;;1107:13413;1832:128;;;2024:14:3;1832:128:35;13002:241;;;;;;:::i;:::-;1107:13413;12992:252;;1107:13413;;13281:57;;;;;;;;1832:128;;;;;;;;;;;;2024:14:3;1832:128:35;;;2024:14:3;1832:128:35;;;13281:57;1107:13413;13271:68;;13392:14;2024::3;1107:13413:35;;;2024:14:3;;;;13384:31:35;;1107:13413;13384:31;;2024:14:3;1832:128:35;;;2024:14:3;6119:307:35;13384:31;;;-1:-1:-1;;;;;;;;;;;13384:31:35;;;;;;;1107:13413;;;13384:31;;;1107:13413;13432:25;1107:13413;;;13432:25;1107:13413;;;13432:25;;;;;;1832:128;;;;2024:14:3;;1832:128:35;;;2024:14:3;;1832:128:35;;;;;;;;;;;;;13432:25;1832:128;2024:14:3;-1:-1:-1;;;;;1107:13413:35;-1:-1:-1;;;;;;;;;;;6519:17:35;;;;1107:13413;;2024:14:3;;;;6519:17:35;;1107:13413;6519:17;;1107:13413;6519:17;;1832:128;6519:17;;-1:-1:-1;;;;;;;;;;;6519:17:35;;;;;;;;1107:13413;;6546:76;1107:13413;;;;;;5948:13;2024:14:3;;;1107:13413:35;6595:10;1457:7;1107:13413;;2024:14:3;;;;;;;;;6546:76:35;;1107:13413;6546:76;;;:::i;:::-;;;;;;;;;;6934:64;6727:56;6912:193;6546:76;7124:69;6546:76;1107:13413;6546:76;;;-1:-1:-1;5948:13:35;2024:14:3;;;-1:-1:-1;;;;;1107:13413:35;6727:30;:56;:::i;:::-;1107:13413;6705:188;1107:13413;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;6705:188;:::i;:::-;6026:17;1107:13413;-1:-1:-1;;;;;1107:13413:35;6934:34;:64;:::i;:::-;1107:13413;;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;1107:13413:35;;;;6912:193;:::i;:::-;1107:13413;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;1107:13413:35;;;;7124:69;:::i;6519:17::-;;;;;;:::i;:::-;1107:13413;;6519:17;;;;1107:13413;;2024:14:3;1107:13413:35;;2024:14:3;;;;13384:31:35;;;;13432:25;13384:31;13432:25;13384:31;6119:307;13384:31;6119:307;13384:31;;;;;;;:::i;:::-;;;-1:-1:-1;13384:31:35;;-1:-1:-1;13384:31:35;;;1107:13413;;;;;;;;;;;;;3346:26:9;1107:13413:35;2024:14:3;;;:::i;:::-;1107:13413:35;;;;;;:::i;:::-;2024:14:3;;;3346:26:9;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;9268:13;2024:14:3;;;1107:13413:35;9260:30;9423:13;9733:7;9715:15;1457:7;9715:15;;;1457:7;1107:13413;9418:622;9438:19;9326:1;9438:19;;;;1107:13413;;10145:29;1107:13413;;;;;;9268:13;2024:14:3;;;1107:13413:35;10081:30;10145:29;:::i;:::-;1107:13413;;;;;9520:319;1107:13413;;:::i;:::-;1547:29;1107:13413;;1547:29;;;;;;1107:13413;1547:29;;;1502:9;2925:13:4;;2921:73;;9418:622:35;1107:13413;358:279:18;1107:13413:35;;;10439:48;1107:13413;;;9520:319;1107:13413;;:::i;:::-;1547:29;1107:13413;;1547:29;;;;;-1:-1:-1;;;1107:13413:35;1547:29;;;10439:48;:::i;:::-;10630:49;1107:13413;;;;;7222:54:18;1107:13413:35;;;;;;:::i;:::-;1547:29;1107:13413;;1547:29;;;;;1107:13413;;-1:-1:-1;;;7222:54:18;;;;9951:7:35;7222:54:18;;1107:13413:35;;;;;;;;;;;:::i;:::-;9326:1;1107:13413;;;2024:14:3;7222:54:18;1107:13413:35;;7222:54:18;;;;;;:::i;:::-;358:279;;;;;131:42;358:279;;;10562:58:35;1107:13413;;;;;;:::i;:::-;1547:29;1107:13413;;1547:29;;;;;10562:58;:::i;:::-;1107:13413;;;;;;:::i;:::-;1547:29;1107:13413;;-1:-1:-1;;;1547:29:35;;;;10630:49;:::i;:::-;7222:54:18;1107:13413:35;;;;;;;:::i;:::-;1547:29;1107:13413;;1547:29;;;;;1107:13413;;;7222:54:18;;;;;;;;;;9951:7:35;7222:54:18;;1107:13413:35;;;;;;:::i;:::-;1502:9;1107:13413;;;2024:14:3;7222:54:18;1107:13413:35;;7222:54:18;;;;;;:::i;:::-;358:279;;;;131:42;358:279;;;1107:13413:35;;2921:73:4;-1:-1:-1;;;;;;;;;;;2954:29:4;;;;1107:13413:35;;;;;2024:14:3;;;;;;;2954:29:4;;;1107:13413:35;2954:29:4;;2024:14:3;1502:9:35;9951:7;1107:13413;;2024:14:3;9520:319:35;1107:13413;;;;;;;;;:::i;:::-;2954:29:4;;-1:-1:-1;;;;;;;;;;;2954:29:4;;;;;;;2921:73;2954:29;;;;;:::i;:::-;1107:13413:35;;2954:29:4;;2921:73;;9459:3:35;9573:4;2024:14:3;1502:9:35;;-1:-1:-1;;;;;1107:13413:35;;;;;;;1457:7;;1107:13413;;;;;;:::i;:::-;;;9520:319;;;1107:13413;1457:7;1107:13413;9520:319;;1107:13413;9520:319;;;;1107:13413;9520:319;;;;1107:13413;3195:30;;;9520:319;;;2024:14:3;1107:13413:35;;;;;;:::i;:::-;;;;9520:319;;;1107:13413;9891:24;;;;:::i;:::-;9951:7;2024:14:3;-1:-1:-1;;;;;1107:13413:35;-1:-1:-1;;;;;;;;;;;9942:17:35;;;;1107:13413;;2024:14:3;;;;9942:17:35;;1107:13413;9942:17;;1107:13413;9942:17;;9951:7;9942:17;;-1:-1:-1;;;;;;;;;;;9942:17:35;;;;;;;;9459:3;1107:13413;9973:56;1107:13413;;;;;;9268:13;2024:14:3;;;1107:13413:35;10002:10;1457:7;1107:13413;;2024:14:3;;;;;;;;;9973:56:35;;1107:13413;9973:56;;;:::i;:::-;;;;;;;;;;1502:9;9973:56;;;9459:3;;1502:9;9423:13;;9973:56;;;;;;;;;;;;:::i;:::-;;;;;;1107:13413;;2024:14:3;1107:13413:35;;2024:14:3;;;;9942:17:35;;;;;;:::i;:::-;1107:13413;;9942:17;;;1457:7;-1:-1:-1;;;1502:9:35;;;1107:13413;1502:9;9951:7;1107:13413;1502:9;1107:13413;;;;;;;;;;;;;;;;;;3501:18:9;1107:13413:35;;;;;;;3501:18:9;1107:13413:35;;;;;;;;;;;;;;;;;;:::i;:::-;2024:14:3;;-1:-1:-1;;;;;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3794:16:9;1107:13413:35;;;;;;;3794:16:9;1107:13413:35;;;;;;;;;;;;;;;;;;:::i;:::-;2024:14:3;;-1:-1:-1;;;;;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3948:19:9;1107:13413:35;2024:14:3;;;:::i;:::-;1107:13413:35;;;;;;:::i;:::-;2024:14:3;;;3948:19:9;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;:::i;:::-;2024:14:3;;-1:-1:-1;;;;;1107:13413:35;;;;;;;;2024:14:3;1107:13413:35;2024:14:3;:::i;:::-;1107:13413:35;;;;;;;:::i;:::-;2024:14:3;;;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3018:16:9;1107:13413:35;;;;;;;3018:16:9;1107:13413:35;;;;;;;;;;;;;;;;;;:::i;:::-;2024:14:3;;-1:-1:-1;;;;;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1107:13413:35;;;;2440:4;-1:-1:-1;;;;;1107:13413:35;;2424:21;1107:13413;;;2424:21;1107:13413;;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;1107:13413:35;;;;;;20624:22:6;;1107:13413:35;;;;;;;;;20624:22:6;;;;;;:::i;:::-;1107:13413:35;20614:33:6;;1107:13413:35;;2024:14:3;;;;20665:19:6;;1107:13413:35;20665:19:6;;2024:14:3;1107:13413:35;20665:19:6;;;-1:-1:-1;;;;;;;;;;;20665:19:6;;;;;;;1107:13413:35;20665:19:6;;;1107:13413:35;20694:20:6;-1:-1:-1;;;;;;;;;;;20694:20:6;;;;1107:13413:35;;-1:-1:-1;;;20694:20:6;;-1:-1:-1;;;;;1107:13413:35;;;;20694:20:6;;1107:13413:35;;;20665:19:6;291:59;;;;;1107:13413:35;;;;;;;291:59:6;;;;;;;:::i;:::-;20694:20;;;-1:-1:-1;;;;;;;;;;;20694:20:6;;;;;;;;1107:13413:35;;-1:-1:-1;;;;;1107:13413:35;;20665:19:6;1107:13413:35;;;20665:19:6;1107:13413:35;2558:66;1107:13413;;;;2024:14:3;;;2641:23:35;;2558:66;1107:13413;2641:23;;2024:14:3;1107:13413:35;2641:23;20665:19:6;2641:23:35;-1:-1:-1;;;;;;;;;;;2641:23:35;;;;;;;;;;;1107:13413;;;;;;;;-1:-1:-1;;;;;1107:13413:35;;2634:30;1107:13413;;;2634:30;1107:13413;;;2721:16;;;;;;;;1107:13413;2721:16;;;;;;;;;;;;;;;;;;;1107:13413;;;;;;-1:-1:-1;;;;;1107:13413:35;;;;;;;;;;2767:23;;;;;;;;1107:13413;2767:23;;;;;;;;;;;;;;;;;;;1107:13413;;;;;;-1:-1:-1;;;;;1107:13413:35;;2747:43;1107:13413;;;2747:43;1107:13413;;;2816:19;;;;;;;;1107:13413;2816:19;;;;;;;;;;;;;;;;;;;1107:13413;;;;;;-1:-1:-1;;;;;1107:13413:35;;2800:35;1107:13413;;;2800:35;1107:13413;;;2909:19;;;;;;;;1107:13413;2909:19;;;;;;;;;;;;;;;;;;;1107:13413;;2962:45;;;;;;;;;;1107:13413;2962:45;;;;;;;;1107:13413;2962:45;;;;-1:-1:-1;;;;;1107:13413:35;;;;2024:14:3;;;;;;;;1107:13413:35;;;2962:45;;;2024:14:3;2962:45:35;;;;;1107:13413;2024:14:3;;-1:-1:-1;;;;;;2024:14:3;;;;;;-1:-1:-1;;;;;2024:14:3;;;;;;;;;1107:13413:35;;2024:14:3;;;1107:13413:35;2024:14:3;1107:13413:35;;:::i;:::-;3160:1;2024:14:3;;1107:13413:35;2024:14:3;;;-1:-1:-1;;2024:14:3;;1107:13413:35;2024:14:3;;1107:13413:35;;2024:14:3;;;-1:-1:-1;;;2024:14:3;;;2424:21:35;2024:14:3;3195:30:35;;;;-1:-1:-1;;;;;2024:14:3;;;;;1107:13413:35;;;;;;3235:50;;;;;2024:14:3;1107:13413:35;;;;;;2024:14:3;;;;;;;;;3235:50:35;;1107:13413;3235:50;;1107:13413;2024:14:3;20665:19:6;2024:14:3;;;291:59:6;2024:14:3;;;;:::i;:::-;3235:50:35;;;;;;;;;;;;;1107:13413;-1:-1:-1;;20665:19:6;2024:14:3;-1:-1:-1;;;;;1107:13413:35;-1:-1:-1;;;;;;;;;;;3321:26:35;;;;1107:13413;;2024:14:3;;;;3321:26:35;;1107:13413;3321:26;;1107:13413;3338:8;20665:19:6;2024:14:3;;;3321:26:35;;291:59:6;3321:26:35;;-1:-1:-1;;;;;;;;;;;3321:26:35;;;;;;;;;;;1107:13413;-1:-1:-1;;2634:30:35;2024:14:3;-1:-1:-1;;;;;1107:13413:35;;-1:-1:-1;;;;;;;;;;;3357:23:35;;;;1107:13413;;2024:14:3;;;;3357:23:35;;1107:13413;3357:23;;1107:13413;3338:8;20665:19:6;2024:14:3;;;3357:23:35;;291:59:6;3357:23:35;;-1:-1:-1;;;;;;;;;;;3357:23:35;;;;;;;;;;1107:13413;;3357:23;;;;;:::i;:::-;1107:13413;;3357:23;1107:13413;3357:23;1107:13413;2024:14:3;1107:13413:35;;2024:14:3;;;;3357:23:35;1107:13413;;;3321:26;;;;;:::i;:::-;1107:13413;;3321:26;;;;1107:13413;;;3321:26;1107:13413;;;2024:14:3;;;;;;;;3235:50:35;;;;;:::i;:::-;1107:13413;;3235:50;;;;;1107:13413;;;2024:14:3;-1:-1:-1;;;2024:14:3;;;1107:13413:35;2024:14:3;20665:19:6;1107:13413:35;2024:14:3;2962:45:35;1107:13413;;;2024:14:3;;;;;;;;2962:45:35;-1:-1:-1;;;1107:13413:35;;;;;20665:19:6;1107:13413:35;;2909:19;-1:-1:-1;;;1107:13413:35;;;;;20665:19:6;1107:13413:35;;2641:23;;;;1107:13413;2641:23;1107:13413;2641:23;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;;;1107:13413;;2024:14:3;1107:13413:35;;2024:14:3;;;;20694:20:6;;;;;1107:13413:35;20694:20:6;;:::i;:::-;1107:13413:35;20694:20:6;;;;;1107:13413:35;;2024:14:3;1107:13413:35;2024:14:3;;;;;20694:20:6;1107:13413:35;;;20665:19:6;;;;1107:13413:35;20665:19:6;1107:13413:35;20665:19:6;;;;;;;:::i;:::-;;;;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;;;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1107:13413:35;;;;;;;;-1:-1:-1;;1107:13413:35;;;;:::o;:::-;;;;;;;;;;;;;;-1:-1:-1;1107:13413:35;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;;;;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;-1:-1:-1;1107:13413:35;;;;;-1:-1:-1;1107:13413:35;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;:::o;2024:14:3:-;;;;;;;;;;-1:-1:-1;;;;;1107:13413:35;;2024:14:3;;;;;:::o;:::-;;;;;;;;;;;:::o;1107:13413:35:-;;;;;-1:-1:-1;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:::o;:::-;;;-1:-1:-1;1107:13413:35;;;;;-1:-1:-1;1107:13413:35;;-1:-1:-1;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2024:14:3;1107:13413:35;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1107:13413:35;;;;;-1:-1:-1;1107:13413:35;;;;;;;;1547:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1107:13413;;;;;;;;;;;-1:-1:-1;;1107:13413:35;1547:29;1107:13413;;;:::i;:::-;1547:29;;;;;;;;;;;;-1:-1:-1;1547:29:35;;;;;;;;1107:13413;;;;;1547:29;:::o;:::-;;;;;;;;;;1107:13413;;;;;1547:29;;1107:13413;1547:29;;;1107:13413;;;;;;1547:29;;;;1107:13413;1547:29;;;1107:13413;1547:29;;;;;;;2024:14:3;1547:29:35;;;;;;;2024:14:3;1547:29:35;;;;;;;2024:14:3;1107:13413:35;;;1547:29;;;;1107:13413;1547:29;;;1107:13413;1547:29;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;:::o;:::-;;;;;;;;;;:::o;:::-;1107:13413;;;1502:9;;;;;;;;1107:13413;;;;;;;;;;;;;;;;-1:-1:-1;1107:13413:35;;-1:-1:-1;1107:13413:35;;-1:-1:-1;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;;2024:14:3;1107:13413:35;;;;;;;;;2024:14:3;;;-1:-1:-1;;;;;;2024:14:3;1107:13413:35;;;;;;;;;;;;2024:14:3;;;-1:-1:-1;;;;;;2024:14:3;1107:13413:35;;;;;;;;;;;;;2024:14:3;;;-1:-1:-1;;;;;;2024:14:3;1107:13413:35;;;;;;;;;;;;;2024:14:3;;;-1:-1:-1;;;;;;2024:14:3;1107:13413:35;;;;;;;;;;;;;2024:14:3;;;-1:-1:-1;;;;;;2024:14:3;1107:13413:35;;;;;;;;;;;;;2024:14:3;;;-1:-1:-1;;;;;;2024:14:3;1107:13413:35;;;;;;;;;;;;;2024:14:3;;;-1:-1:-1;;;;;;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;;;;;2024:14:3;;;1107:13413:35;;;;;;2024:14:3;1107:13413:35;2024:14:3;;1107:13413:35;;;;;;;2024:14:3;1107:13413:35;2024:14:3;;1107:13413:35;;;;;;;2024:14:3;1107:13413:35;2024:14:3;;1107:13413:35;;;;;;;2024:14:3;1107:13413:35;2024:14:3;;1107:13413:35;;;;;;;2024:14:3;1107:13413:35;2024:14:3;;1107:13413:35;;;;;;;2024:14:3;1107:13413:35;2024:14:3;;1107:13413:35;;;;;;;2024:14:3;;1107:13413:35;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;:::o;1306:195:4:-;1365:7;1107:13413:35;;;;;;1395:4:4;1388:11;:::o;1361:134::-;1107:13413:35;;2024:14:3;;;1437:33:4;;-1:-1:-1;;;;;;;;;;;1437:33:4;;;1107:13413:35;192:59:4;;;1255:17;;;2024:14:3;1255:17:4;1437:33;;;-1:-1:-1;;;;;;;;;;;1437:33:4;;;;;;;1107:13413:35;1437:33:4;;;1361:134;1437:47;;;1430:54;:::o;1437:33::-;;;1255:17;1437:33;;1255:17;1437:33;;;;;;1255:17;1437:33;;;:::i;:::-;;;1255:17;;;;;1437:33;;;;;;-1:-1:-1;1437:33:4;;;1107:13413:35;;2024:14:3;1107:13413:35;2024:14:3;;;;;3564:2159:35;3699:13;2024:14:3;3770:10:35;1502:9;3831:7;2024:14:3;3999:4:35;2024:14:3;-1:-1:-1;;2024:14:3;-1:-1:-1;;;;;1107:13413:35;;;;;;;;2024:14:3;;;;;1107:13413:35;;3691:30;;3831:15;;;1107:13413;3762:27;;;4125:15;4143:7;1457;;;1107:13413;1457:7;-1:-1:-1;1457:7:35;;1107:13413;;;;;;:::i;:::-;;;3770:10;3950:287;;1107:13413;1457:7;1107:13413;3950:287;;1107:13413;-1:-1:-1;3950:287:35;;;1107:13413;3950:287;;;1107:13413;3195:30;;;3950:287;;;2024:14:3;1107:13413:35;;;;;;;;:::i;:::-;-1:-1:-1;1107:13413:35;;3950:287;;;1107:13413;4281:24;;;:::i;:::-;4399:17;-1:-1:-1;;;;;;;;;;;4399:17:35;;;;1107:13413;;2024:14:3;;;;4399:17:35;;;;;1107:13413;-1:-1:-1;4399:17:35;3831:7;4399:17;;-1:-1:-1;;;;;;;;;;;4399:17:35;;;;;;;;3564:2159;1107:13413;;4426:56;1107:13413;;;;;;3699:13;2024:14:3;;;1107:13413:35;1457:7;1107:13413;;;;2024:14:3;;;;;;;;;4426:56:35;;4399:17;4426:56;;;:::i;:::-;;;;;;;;;5475:49;4426:56;1107:13413;4426:56;5047:163;5069:41;4426:56;;5283:173;4426:56;5305:43;4426:56;5663:53;4426:56;;;;;3564:2159;-1:-1:-1;3699:13:35;2024:14:3;1502:9:35;;3831:7;2024:14:3;-1:-1:-1;;;;;1107:13413:35;;;4712:15;;1107:13413;;;4645:27;;4813:163;;1107:13413;;4835:47;;2024:14:3;;1107:13413:35;4576:30;4835:47;:::i;:::-;3950:287;1107:13413;;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;1107:13413:35;;;;4813:163;:::i;:::-;5069:41;:::i;:::-;1107:13413;;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;1107:13413:35;;;;5047:163;:::i;5305:43::-;1107:13413;;;;;;;;:::i;:::-;;;;;;;;;;;;;;5283:173;:::i;:::-;1107:13413;;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;1107:13413:35;;;;5475:49;:::i;:::-;5534:55;1107:13413;;:::i;5534:55::-;1107:13413;;;358:279:18;1107:13413:35;;7222:54:18;1107:13413:35;;;;;;:::i;:::-;;;;;;;;;;;-1:-1:-1;;;7222:54:18;;;;3831:7:35;7222:54:18;;1107:13413:35;;;;;;;;;;;:::i;:::-;1457:7;1107:13413;;;2024:14:3;7222:54:18;1107:13413:35;;7222:54:18;;;;;;:::i;:::-;358:279;;;;;131:42;358:279;;;1107:13413:35;;;;;;:::i;:::-;;;;;;;5663:53;:::i;4426:56::-;;;;;;;;;;;;:::i;:::-;;;4399:17;;;;;-1:-1:-1;4399:17:35;;:::i;:::-;-1:-1:-1;;4426:56:35;4399:17;;1832:128;;;;;;;;;;;1107:13413;;;1832:128;;;;;;;;;1255:17:4;1832:128:35;;1255:17:4;1832:128:35;:::o;12066:683::-;12566:57;;12237:23;;:::i;:::-;1107:13413;;;;;;1832:128;;1107:13413;;;;;;;12377:10;;;1832:128;1107:13413;12401:9;;;;1832:128;12424:9;;;;1832:128;12447:12;;;1832:128;1107:13413;12509:8;1107:13413;;;12473:12;;;1832:128;1107:13413;12509:8;;;12377:10;1107:13413;;;;12499:19;1107:13413;12401:9;1107:13413;12301:227;12377:10;12301:227;;;1832:128;2024:14:3;;12401:9:35;1832:128;;1107:13413;12424:9;1832:128;;1107:13413;12447:12;1832:128;;2024:14:3;12473:12:35;1832:128;;2024:14:3;12509:8:35;1832:128;;2024:14:3;1832:128:35;;;1107:13413;1832:128;;;2024:14:3;1832:128:35;12301:227;;;;;;:::i;:::-;1107:13413;12291:238;;12401:9;1107:13413;12566:57;;;12377:10;12566:57;;;;1832:128;;;;;;;;;;;;2024:14:3;1832:128:35;;;2024:14:3;1832:128:35;;;12566:57;1107:13413;12556:68;;12677:14;2024::3;1107:13413:35;12401:9;1107:13413;2024:14:3;;;;12669:31:35;;;;;2024:14:3;1832:128:35;;;2024:14:3;12424:9:35;12669:31;;;-1:-1:-1;;;;;;;;;;;12669:31:35;;;;;;-1:-1:-1;;;;12669:31:35;;;12066:683;12401:9;1107:13413;;12377:10;12717:25;;2024:14:3;;;;1832:128:35;;2024:14:3;;;;1832:128:35;;;;;-1:-1:-1;;;;;;1832:128:35;;;;;1107:13413;-1:-1:-1;12717:25:35;1107:13413;1832:128;;;12717:25;1832:128;12669:31;;;;12717:25;12669:31;12717:25;12669:31;12424:9;12669:31;12424:9;12669:31;;;;;;;:::i;:::-;;;-1:-1:-1;12669:31:35;;-1:-1:-1;12669:31:35;;;2823:177:4;1502:9:35;2925:13:4;;2921:73;;2823:177;;:::o;2921:73::-;-1:-1:-1;;;;;;;;;;;2954:29:4;;;;1107:13413:35;;-1:-1:-1;1107:13413:35;;;2024:14:3;;;;;;;;2954:29:4;;;;;2024:14:3;1502:9:35;1107:13413;;;2024:14:3;1107:13413:35;;;;;;;;;;:::i;:::-;2954:29:4;;-1:-1:-1;;;;;;;;;;;2954:29:4;;;;;;;;2823:177;:::o;2954:29::-;-1:-1:-1;2954:29:4;;;:::i;2823:177::-;1457:7:35;2925:13:4;;2921:73;;2823:177;;:::o;2921:73::-;-1:-1:-1;;;;;;;;;;;2954:29:4;;;;1107:13413:35;;-1:-1:-1;1107:13413:35;;;2024:14:3;;;;;;;;2954:29:4;;;;;2024:14:3;1457:7:35;1107:13413;;;2024:14:3;1107:13413:35;;;;;;;;;;:::i;2823:177:4:-;;2925:13;;;2921:73;;2823:177;;;:::o;2921:73::-;-1:-1:-1;;;;;;;;;;;2954:29:4;;;;-1:-1:-1;1107:13413:35;;;;2024:14:3;;;;;;;;2954:29:4;;;;;2024:14:3;1107:13413:35;;;2024:14:3;1107:13413:35;;;;;;;;;;:::i;6191:121:18:-;358:279;1107:13413:35;6262:42:18;6191:121;;1107:13413:35;;6262:42:18;;;;;;;;;;;;;;1107:13413:35;;;;;;:::i;6262:42:18:-;358:279;;;;;131:42;358:279;;;6191:121::o;7139:145::-;358:279;1107:13413:35;7222:54:18;7139:145;;1107:13413:35;;7222:54:18;;;;;;;;;;1107:13413:35;7222:54:18;;;1107:13413:35;;;;;;:::i;:::-;1502:9;1107:13413;;;2024:14:3;7222:54:18;1107:13413:35;;7222:54:18;;;;;;:::i;7139:145::-;1107:13413:35;7222:54:18;358:279;7139:145;;;;1107:13413:35;;7222:54:18;;;;;;;;;;1107:13413:35;7222:54:18;;;1107:13413:35;;;;;;:::i;:::-;;;;;2024:14:3;7222:54:18;1107:13413:35;;7222:54:18;;;;;;:::i;14234:284:35:-;1107:13413;;;;;14486:13;2024:14:3;;;1107:13413:35;;;14326:184;;;2024:14:3;1687:95:35;2024:14:3;;14379:26:35;1107:13413;1687:95;;2024:14:3;14419:18:35;1687:95;;;2024:14:3;14451:13:35;1687:95;;;2024:14:3;1687:95:35;;;1107:13413;1687:95;14326:184;;;;;;:::i;:::-;1107:13413;14316:195;;14234:284;:::o", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "setUp()": "0a9254e4", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "test_AccumulatedETHStranding()": "574f1055", "test_NoWithdrawalMechanism()": "8a2c190f", "test_executeMarginAccountRequest_StrandsExcessETH()": "795a7668", "test_executePriceDependent_StrandsExcessETH()": "b86e7578", "test_execute_StrandsExcessETH()": "db1c028f"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"setUp\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_AccumulatedETHStranding\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_NoWithdrawalMechanism\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_executeMarginAccountRequest_StrandsExcessETH\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_executePriceDependent_StrandsExcessETH\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_execute_StrandsExcessETH\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Proof of Concept test to verify the alleged ETH stranding bug in KuruForwarder.sol  Bug Claims from Issue.md: - Location: execute, executeMarginAccountRequest, executePriceDependent functions - Issue: require(msg.value >= req.value, ...) allows excess ETH - Problem: Only req.value is forwarded via call{value: req.value}(...) - Impact: Any msg.value > req.value remains stuck in the forwarder - No withdraw mechanism exists for stranded ETH  This POC will test: 1. Whether excess ETH gets stranded in the forwarder 2. Whether the stranded ETH is unrecoverable 3. Impact across all three vulnerable functions 4. Accumulation of stranded ETH over multiple transactions\",\"kind\":\"dev\",\"methods\":{\"test_AccumulatedETHStranding()\":{\"details\":\"Test 4: Verify ETH accumulation over multiple transactions This demonstrates how the bug compounds over time\"},\"test_NoWithdrawalMechanism()\":{\"details\":\"Test 5: Verify no withdrawal mechanism exists This confirms that stranded ETH is truly unrecoverable\"},\"test_executeMarginAccountRequest_StrandsExcessETH()\":{\"details\":\"Test 2: Verify ETH stranding in executeMarginAccountRequest() function\"},\"test_executePriceDependent_StrandsExcessETH()\":{\"details\":\"Test 3: Verify ETH stranding in executePriceDependent() function\"},\"test_execute_StrandsExcessETH()\":{\"details\":\"Test 1: Verify ETH stranding in execute() function This test demonstrates that excess ETH sent to execute() gets stuck in the forwarder\"}},\"title\":\"ETHStrandingBugPOC\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/ETHStrandingBugPOC.t.sol\":\"ETHStrandingBugPOC\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@chainlink/=node_modules/@chainlink/\",\":@eth-optimism/=node_modules/@eth-optimism/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":eth-gas-reporter/=node_modules/eth-gas-reporter/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":hardhat/=node_modules/hardhat/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin-solidity/=node_modules/openzeppelin-solidity/\",\":solady/=node_modules/solady/\"],\"viaIR\":true},\"sources\":{\"contracts/KuruForwarder.sol\":{\"keccak256\":\"0xacc170f5ce66221ae208235ae21d4014b537002f3085c729fd67793a97ce8730\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ce914cf74f9d9d3fd73b8392318063d4b57c90e7b615990030d1afd1f2402368\",\"dweb:/ipfs/QmZvTGqYLwiBSQKDTKVEVAQN3mEBMuLefuUoAWrW6uPAP4\"]},\"contracts/interfaces/IOrderBook.sol\":{\"keccak256\":\"0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d\",\"dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68\"]},\"contracts/libraries/Errors.sol\":{\"keccak256\":\"0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7\",\"dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu\"]},\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3\",\"dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc\",\"dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a\",\"dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT\"]},\"lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4\",\"dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xe2b159412b06b44a7f90972104300c587f308506d37d2143dd7e689e2eac6f01\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://a96e13ac6fec3ffe61a55d6005ef0ef96f47adf3f4f3531e0418f0bf02d3f93c\",\"dweb:/ipfs/QmeFci69gm7a6c6pEqSNoe3HfXPVPiagUc51Pyo5PoS8Rn\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol\":{\"keccak256\":\"0xbf2aefe54b76d7f7bcd4f6da1080b7b1662611937d870b880db584d09cea56b5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5e7e2f12e0feec75296e57f51f82fdaa8bd1551f4b8cc6560442c0bf60f818c\",\"dweb:/ipfs/QmcW9wDMaQ8RbQibMarfp17a3bABzY5KraWe2YDwuUrUoz\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049\",\"dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0xa1ad192cd45317c788618bef5cb1fb3ca4ce8b230f6433ac68cc1d850fb81618\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b43447bb85a53679d269a403c693b9d88d6c74177dfb35eddca63abaf7cf110a\",\"dweb:/ipfs/QmXSDmpd4bNZj1PDgegr6C4w1jDaWHXCconC3rYiw9TSkQ\"]},\"lib/openzeppelin-contracts/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0x20462ddb2665e9521372c76b001d0ce196e59dbbd989de9af5576cad0bd5628b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f417fd12aeec8fbfaceaa30e3a08a0724c0bc39de363e2acf6773c897abbaf6d\",\"dweb:/ipfs/QmU4Hko6sApdweVM92CsiuLKkCk8HfyBeutF89PCTz5Tye\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0x6d0ae6e206645341fd122d278c2cb643dea260c190531f2f3f6a0426e77b00c0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://032d1201d839435be2c85b72e33206b3ea980c569d6ebf7fa57d811ab580a82f\",\"dweb:/ipfs/QmeqQjAtMvdZT2tG7zm39itcRJkuwu8AEReK6WRnLJ18DD\"]},\"lib/openzeppelin-contracts/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"node_modules/solady/src/auth/Ownable.sol\":{\"keccak256\":\"0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3\",\"dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs\"]},\"node_modules/solady/src/utils/ECDSA.sol\":{\"keccak256\":\"0x077d168511141c83fd93914f4609c5363341da03a3971cbc0d3f6a75e9893de0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c218864adb4e97d81908318d9bd0ed75f40ec996c826141fab2646135820295b\",\"dweb:/ipfs/QmVQyWG6Ff4LAUnzj6JMEHHYfNBANVYLaMkun6TYaWShms\"]},\"node_modules/solady/src/utils/EIP712.sol\":{\"keccak256\":\"0xb5c4c8ac5368c9785b4e30314f4ad6f3ae13bdc21679007735681d13da797bec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c4456a4eaa8748f802fd1188db6405d18c452eb7c0dde84a49b49a7f94b5970d\",\"dweb:/ipfs/QmZzsFn4VwvBFy2MJVJXvntCQsDRCXbRrSKKfXxXv9jYGM\"]},\"node_modules/solady/src/utils/Initializable.sol\":{\"keccak256\":\"0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559\",\"dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy\"]},\"node_modules/solady/src/utils/UUPSUpgradeable.sol\":{\"keccak256\":\"0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16\",\"dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV\"]},\"test/ETHStrandingBugPOC.t.sol\":{\"keccak256\":\"0x8034634705bee2f47030d55e7a9b69efece10fcd3edb9c1e685b39ad5a7c732d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8068e0028a9e11860d60ab47b9dac4cb5b2f0c55945fd14c59edf84bbeb344b8\",\"dweb:/ipfs/QmegGBXZKreLboDWiFkxfmoyLnAsXKDJALMDnnszHMnVaH\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "setUp"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_AccumulatedETHStranding"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_NoWithdrawalMechanism"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_executeMarginAccountRequest_StrandsExcessETH"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_executePriceDependent_StrandsExcessETH"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_execute_StrandsExcessETH"}], "devdoc": {"kind": "dev", "methods": {"test_AccumulatedETHStranding()": {"details": "Test 4: Verify ETH accumulation over multiple transactions This demonstrates how the bug compounds over time"}, "test_NoWithdrawalMechanism()": {"details": "Test 5: Verify no withdrawal mechanism exists This confirms that stranded ETH is truly unrecoverable"}, "test_executeMarginAccountRequest_StrandsExcessETH()": {"details": "Test 2: Verify ETH stranding in executeMarginAccountRequest() function"}, "test_executePriceDependent_StrandsExcessETH()": {"details": "Test 3: Verify ETH stranding in executePriceDependent() function"}, "test_execute_StrandsExcessETH()": {"details": "Test 1: Verify ETH stranding in execute() function This test demonstrates that excess ETH sent to execute() gets stuck in the forwarder"}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@chainlink/=node_modules/@chainlink/", "@eth-optimism/=node_modules/@eth-optimism/", "@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "hardhat/=node_modules/hardhat/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin-solidity/=node_modules/openzeppelin-solidity/", "solady/=node_modules/solady/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/ETHStrandingBugPOC.t.sol": "ETHStrandingBugPOC"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"contracts/KuruForwarder.sol": {"keccak256": "0xacc170f5ce66221ae208235ae21d4014b537002f3085c729fd67793a97ce8730", "urls": ["bzz-raw://ce914cf74f9d9d3fd73b8392318063d4b57c90e7b615990030d1afd1f2402368", "dweb:/ipfs/QmZvTGqYLwiBSQKDTKVEVAQN3mEBMuLefuUoAWrW6uPAP4"], "license": "BUSL-1.1"}, "contracts/interfaces/IOrderBook.sol": {"keccak256": "0x838cfddf7f9743c4e3be2293336a48b261ebcb84e2151f04113b10c94e75339b", "urls": ["bzz-raw://e0e008f19c54714f3c2903246800d55bf05f7edb891480b67e78d0d87128d52d", "dweb:/ipfs/QmPeW1C8TwJP8xkbfiUnEFEEd5EGBU2Z6VrZEPXqS5da68"], "license": "GPL-2.0-or-later"}, "contracts/libraries/Errors.sol": {"keccak256": "0xc63d2cde2ffcc44adc70a3b2e0610733d70d0983e9ddc0e4135eb420256f3a6f", "urls": ["bzz-raw://ad8a3b33a7a4825ff43d3222778eda0d493879dd02a4bc5974f3ca2efa5e10b7", "dweb:/ipfs/QmaPkLxbLHQaFSJDB2pbpUNi6Ah2MFVtVGL5Uppa8i2wdu"], "license": "BUSL-1.1"}, "lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/StdAssertions.sol": {"keccak256": "0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43", "urls": ["bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3", "dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd", "urls": ["bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc", "dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8", "urls": ["bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a", "dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT"], "license": "MIT"}, "lib/forge-std/src/Test.sol": {"keccak256": "0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c", "urls": ["bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4", "dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xe2b159412b06b44a7f90972104300c587f308506d37d2143dd7e689e2eac6f01", "urls": ["bzz-raw://a96e13ac6fec3ffe61a55d6005ef0ef96f47adf3f4f3531e0418f0bf02d3f93c", "dweb:/ipfs/QmeFci69gm7a6c6pEqSNoe3HfXPVPiagUc51Pyo5PoS8Rn"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol": {"keccak256": "0xbf2aefe54b76d7f7bcd4f6da1080b7b1662611937d870b880db584d09cea56b5", "urls": ["bzz-raw://f5e7e2f12e0feec75296e57f51f82fdaa8bd1551f4b8cc6560442c0bf60f818c", "dweb:/ipfs/QmcW9wDMaQ8RbQibMarfp17a3bABzY5KraWe2YDwuUrUoz"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e", "urls": ["bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049", "dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0xa1ad192cd45317c788618bef5cb1fb3ca4ce8b230f6433ac68cc1d850fb81618", "urls": ["bzz-raw://b43447bb85a53679d269a403c693b9d88d6c74177dfb35eddca63abaf7cf110a", "dweb:/ipfs/QmXSDmpd4bNZj1PDgegr6C4w1jDaWHXCconC3rYiw9TSkQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0x20462ddb2665e9521372c76b001d0ce196e59dbbd989de9af5576cad0bd5628b", "urls": ["bzz-raw://f417fd12aeec8fbfaceaa30e3a08a0724c0bc39de363e2acf6773c897abbaf6d", "dweb:/ipfs/QmU4Hko6sApdweVM92CsiuLKkCk8HfyBeutF89PCTz5Tye"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0x6d0ae6e206645341fd122d278c2cb643dea260c190531f2f3f6a0426e77b00c0", "urls": ["bzz-raw://032d1201d839435be2c85b72e33206b3ea980c569d6ebf7fa57d811ab580a82f", "dweb:/ipfs/QmeqQjAtMvdZT2tG7zm39itcRJkuwu8AEReK6WRnLJ18DD"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "node_modules/solady/src/auth/Ownable.sol": {"keccak256": "0xc208cdd9de02bbf4b5edad18b88e23a2be7ff56d2287d5649329dc7cda64b9a3", "urls": ["bzz-raw://e8fba079cc7230c617f7493a2e97873f88e59a53a5018fcb2e2b6ac42d8aa5a3", "dweb:/ipfs/QmTXg8GSt8hsK2cZhbPFrund1mrwVdkLQmEPoQaFy4fhjs"], "license": "MIT"}, "node_modules/solady/src/utils/ECDSA.sol": {"keccak256": "0x077d168511141c83fd93914f4609c5363341da03a3971cbc0d3f6a75e9893de0", "urls": ["bzz-raw://c218864adb4e97d81908318d9bd0ed75f40ec996c826141fab2646135820295b", "dweb:/ipfs/QmVQyWG6Ff4LAUnzj6JMEHHYfNBANVYLaMkun6TYaWShms"], "license": "MIT"}, "node_modules/solady/src/utils/EIP712.sol": {"keccak256": "0xb5c4c8ac5368c9785b4e30314f4ad6f3ae13bdc21679007735681d13da797bec", "urls": ["bzz-raw://c4456a4eaa8748f802fd1188db6405d18c452eb7c0dde84a49b49a7f94b5970d", "dweb:/ipfs/QmZzsFn4VwvBFy2MJVJXvntCQsDRCXbRrSKKfXxXv9jYGM"], "license": "MIT"}, "node_modules/solady/src/utils/Initializable.sol": {"keccak256": "0x039ac865df50f874528619e58f2bfaa665b6cec82647c711e515cb252a45a2ec", "urls": ["bzz-raw://1886c0e71f4861a23113f9d3eb5f6f00397c1d1bf0191f92534c177a79ac8559", "dweb:/ipfs/QmPLWU427MN9KHFg6DFkrYNutCDLdtNSQLaqmPqKcoPRLy"], "license": "MIT"}, "node_modules/solady/src/utils/UUPSUpgradeable.sol": {"keccak256": "0x6d24f09177c512b8591c333541cb0f8e207a546cdde9ed4893adc5c77e21063e", "urls": ["bzz-raw://ac4da033e91ffbe23485942102c200d301cbe9d600c289bc82a2b8320a7b1f16", "dweb:/ipfs/QmRZHXyDwPumCodzefmKUQSxUgkmCj5pwkPXC1Uvz6TEQV"], "license": "MIT"}, "test/ETHStrandingBugPOC.t.sol": {"keccak256": "0x8034634705bee2f47030d55e7a9b69efece10fcd3edb9c1e685b39ad5a7c732d", "urls": ["bzz-raw://8068e0028a9e11860d60ab47b9dac4cb5b2f0c55945fd14c59edf84bbeb344b8", "dweb:/ipfs/QmegGBXZKreLboDWiFkxfmoyLnAsXKDJALMDnnszHMnVaH"], "license": "MIT"}}, "version": 1}, "id": 35}