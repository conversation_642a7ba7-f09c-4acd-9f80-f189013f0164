// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>} from "../contracts/KuruForwarder.sol";
import {<PERSON><PERSON>ForwarderErrors} from "../contracts/libraries/Errors.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {ECDSA} from "solady/src/utils/ECDSA.sol";

/**
 * @title ETHStrandingBugPOC
 * @dev Proof of Concept test to verify the alleged ETH stranding bug in KuruForwarder.sol
 * 
 * Bug Claims from Issue.md:
 * - Location: execute, executeMarginAccountRequest, executePriceDependent functions
 * - Issue: require(msg.value >= req.value, ...) allows excess ETH
 * - Problem: Only req.value is forwarded via call{value: req.value}(...)
 * - Impact: Any msg.value > req.value remains stuck in the forwarder
 * - No withdraw mechanism exists for stranded ETH
 * 
 * This POC will test:
 * 1. Whether excess ETH gets stranded in the forwarder
 * 2. Whether the stranded ETH is unrecoverable
 * 3. Impact across all three vulnerable functions
 * 4. Accumulation of stranded ETH over multiple transactions
 */
contract ETHStrandingBugPOC is Test {
    using ECDSA for bytes32;

    KuruForwarder kuruForwarder;
    MockTarget mockTarget;
    MockMarginAccount mockMarginAccount;
    MockOrderBook mockOrderBook;

    address owner;
    address relayer;
    address user;
    uint256 userPrivateKey;

    // Test constants
    uint256 constant REQUIRED_VALUE = 1 ether;
    uint256 constant EXCESS_VALUE = 0.5 ether;
    uint256 constant TOTAL_SENT = REQUIRED_VALUE + EXCESS_VALUE; // 1.5 ether

    // EIP-712 domain separator and type hashes
    bytes32 private constant DOMAIN_TYPEHASH = keccak256("EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)");
    bytes32 private constant FORWARD_TYPEHASH = keccak256("ForwardRequest(address from,address market,uint256 value,uint256 nonce,uint256 deadline,bytes4 selector,bytes data)");
    bytes32 private constant MARGIN_ACCOUNT_TYPEHASH = keccak256("MarginAccountRequest(address from,address marginAccount,uint256 value,uint256 nonce,uint256 deadline,bytes4 selector,bytes data)");
    bytes32 private constant PRICE_DEPENDENT_TYPEHASH = keccak256("PriceDependentRequest(address from,address market,uint256 price,uint256 value,uint256 nonce,uint256 deadline,bool isBelowPrice,bytes4 selector,bytes data)");

    function setUp() public {
        owner = address(this);
        relayer = makeAddr("relayer");

        // Create user with known private key
        userPrivateKey = 0x1234567890123456789012345678901234567890123456789012345678901234;
        user = vm.addr(userPrivateKey);

        // Deploy mock contracts
        mockTarget = new MockTarget();
        mockMarginAccount = new MockMarginAccount();
        mockOrderBook = new MockOrderBook();

        // Deploy KuruForwarder
        KuruForwarder implementation = new KuruForwarder();
        address proxy = address(new ERC1967Proxy(address(implementation), ""));
        kuruForwarder = KuruForwarder(proxy);

        // Initialize with allowed interfaces
        bytes4[] memory allowedInterfaces = new bytes4[](1);
        allowedInterfaces[0] = MockTarget.receiveETH.selector;
        kuruForwarder.initialize(owner, allowedInterfaces);

        // Fund accounts
        vm.deal(relayer, 10 ether);
        vm.deal(user, 10 ether);
    }

    /**
     * @dev Test 1: Verify ETH stranding in execute() function
     * This test demonstrates that excess ETH sent to execute() gets stuck in the forwarder
     */
    function test_execute_StrandsExcessETH() public {
        // Record initial balances
        uint256 initialForwarderBalance = address(kuruForwarder).balance;
        uint256 initialTargetBalance = address(mockTarget).balance;
        uint256 initialRelayerBalance = relayer.balance;
        
        // Create and sign forward request
        KuruForwarder.ForwardRequest memory req = KuruForwarder.ForwardRequest({
            from: user,
            market: address(mockTarget),
            value: REQUIRED_VALUE,
            nonce: 0,
            deadline: block.timestamp + 1 hours,
            selector: MockTarget.receiveETH.selector,
            data: ""
        });
        
        bytes memory signature = _signForwardRequest(req);
        
        // Execute with excess ETH (1.5 ether sent, only 1 ether required)
        vm.prank(relayer);
        kuruForwarder.execute{value: TOTAL_SENT}(req, signature);
        
        // Verify balances after execution
        uint256 finalForwarderBalance = address(kuruForwarder).balance;
        uint256 finalTargetBalance = address(mockTarget).balance;
        uint256 finalRelayerBalance = relayer.balance;
        
        // CRITICAL ASSERTION: Excess ETH is stranded in forwarder
        assertEq(
            finalForwarderBalance - initialForwarderBalance,
            EXCESS_VALUE,
            "Excess ETH should be stranded in forwarder"
        );
        
        // Only required amount should reach target
        assertEq(
            finalTargetBalance - initialTargetBalance,
            REQUIRED_VALUE,
            "Target should only receive required ETH amount"
        );
        
        // Relayer loses full amount sent (no refund)
        assertEq(
            initialRelayerBalance - finalRelayerBalance,
            TOTAL_SENT,
            "Relayer should lose full amount sent (no refund mechanism)"
        );
        
        console.log("=== ETH Stranding in execute() ===");
        console.log("ETH stranded in forwarder:", EXCESS_VALUE);
        console.log("ETH properly forwarded:", REQUIRED_VALUE);
        console.log("Total ETH lost by relayer:", TOTAL_SENT);
    }

    /**
     * @dev Test 2: Verify ETH stranding in executeMarginAccountRequest() function
     */
    function test_executeMarginAccountRequest_StrandsExcessETH() public {
        uint256 initialForwarderBalance = address(kuruForwarder).balance;
        uint256 initialMarginAccountBalance = address(mockMarginAccount).balance;
        
        KuruForwarder.MarginAccountRequest memory req = KuruForwarder.MarginAccountRequest({
            from: user,
            marginAccount: address(mockMarginAccount),
            value: REQUIRED_VALUE,
            nonce: 0,
            deadline: block.timestamp + 1 hours,
            selector: MockTarget.receiveETH.selector,
            data: ""
        });
        
        bytes memory signature = _signMarginAccountRequest(req);
        
        vm.prank(relayer);
        kuruForwarder.executeMarginAccountRequest{value: TOTAL_SENT}(req, signature);
        
        // CRITICAL ASSERTION: Excess ETH stranded in forwarder
        assertEq(
            address(kuruForwarder).balance - initialForwarderBalance,
            EXCESS_VALUE,
            "Excess ETH should be stranded in forwarder (MarginAccount)"
        );
        
        assertEq(
            address(mockMarginAccount).balance - initialMarginAccountBalance,
            REQUIRED_VALUE,
            "MarginAccount should only receive required ETH amount"
        );
        
        console.log("=== ETH Stranding in executeMarginAccountRequest() ===");
        console.log("ETH stranded in forwarder:", EXCESS_VALUE);
    }

    /**
     * @dev Test 3: Verify ETH stranding in executePriceDependent() function
     */
    function test_executePriceDependent_StrandsExcessETH() public {
        uint256 initialForwarderBalance = address(kuruForwarder).balance;
        uint256 initialOrderBookBalance = address(mockOrderBook).balance;
        
        // Set mock price to trigger execution
        mockOrderBook.setBestBidAsk(2000, 2100); // Current bid: 2000
        
        KuruForwarder.PriceDependentRequest memory req = KuruForwarder.PriceDependentRequest({
            from: user,
            market: address(mockOrderBook),
            price: 1900, // Trigger when price < 2000 (current bid)
            value: REQUIRED_VALUE,
            nonce: 0,
            deadline: block.timestamp + 1 hours,
            isBelowPrice: true, // Execute when trigger price < current price
            selector: MockTarget.receiveETH.selector,
            data: ""
        });
        
        bytes memory signature = _signPriceDependentRequest(req);
        
        vm.prank(relayer);
        kuruForwarder.executePriceDependent{value: TOTAL_SENT}(req, signature);
        
        // CRITICAL ASSERTION: Excess ETH stranded in forwarder
        assertEq(
            address(kuruForwarder).balance - initialForwarderBalance,
            EXCESS_VALUE,
            "Excess ETH should be stranded in forwarder (PriceDependent)"
        );
        
        assertEq(
            address(mockOrderBook).balance - initialOrderBookBalance,
            REQUIRED_VALUE,
            "OrderBook should only receive required ETH amount"
        );
        
        console.log("=== ETH Stranding in executePriceDependent() ===");
        console.log("ETH stranded in forwarder:", EXCESS_VALUE);
    }

    /**
     * @dev Test 4: Verify ETH accumulation over multiple transactions
     * This demonstrates how the bug compounds over time
     */
    function test_AccumulatedETHStranding() public {
        uint256 initialBalance = address(kuruForwarder).balance;
        uint256 numTransactions = 5;
        uint256 expectedStrandedTotal = EXCESS_VALUE * numTransactions;
        
        for (uint256 i = 0; i < numTransactions; i++) {
            KuruForwarder.ForwardRequest memory req = KuruForwarder.ForwardRequest({
                from: user,
                market: address(mockTarget),
                value: REQUIRED_VALUE,
                nonce: i,
                deadline: block.timestamp + 1 hours,
                selector: MockTarget.receiveETH.selector,
                data: ""
            });
            
            bytes memory signature = _signForwardRequest(req);
            
            vm.prank(relayer);
            kuruForwarder.execute{value: TOTAL_SENT}(req, signature);
        }
        
        uint256 finalBalance = address(kuruForwarder).balance;
        uint256 totalStranded = finalBalance - initialBalance;
        
        // CRITICAL ASSERTION: ETH accumulates over multiple transactions
        assertEq(
            totalStranded,
            expectedStrandedTotal,
            "Stranded ETH should accumulate over multiple transactions"
        );
        
        console.log("=== Accumulated ETH Stranding ===");
        console.log("Number of transactions:", numTransactions);
        console.log("ETH stranded per transaction:", EXCESS_VALUE);
        console.log("Total ETH stranded:", totalStranded);
        console.log("Expected total stranded:", expectedStrandedTotal);
    }

    /**
     * @dev Test 5: Verify no withdrawal mechanism exists
     * This confirms that stranded ETH is truly unrecoverable
     */
    function test_NoWithdrawalMechanism() public {
        // First, strand some ETH
        test_execute_StrandsExcessETH();
        
        uint256 strandedAmount = address(kuruForwarder).balance;
        assertTrue(strandedAmount > 0, "Should have stranded ETH for this test");
        
        // Try various potential withdrawal methods (all should fail or not exist)
        
        // 1. Check if there's a withdraw function (there shouldn't be)
        // This will be verified by the contract not having such a function
        
        // 2. Try to send ETH out via owner (should fail - no such function)
        // The contract has no payable functions that could extract ETH
        
        // 3. Verify ETH remains stuck
        assertEq(
            address(kuruForwarder).balance,
            strandedAmount,
            "Stranded ETH should remain in contract with no way to recover"
        );
        
        console.log("=== No Withdrawal Mechanism ===");
        console.log("ETH permanently stranded:", strandedAmount);
        console.log("No recovery mechanism exists in the contract");
    }

    // Helper functions for signing requests
    function _signForwardRequest(KuruForwarder.ForwardRequest memory req)
        internal
        view
        returns (bytes memory)
    {
        bytes32 domainSeparator = _buildDomainSeparator();
        bytes32 structHash = keccak256(abi.encode(
            FORWARD_TYPEHASH,
            req.from,
            req.market,
            req.value,
            req.nonce,
            req.deadline,
            req.selector,
            keccak256(req.data)
        ));
        bytes32 digest = keccak256(abi.encodePacked("\x19\x01", domainSeparator, structHash));

        (uint8 v, bytes32 r, bytes32 s) = vm.sign(userPrivateKey, digest);
        return abi.encodePacked(r, s, v);
    }

    function _signMarginAccountRequest(KuruForwarder.MarginAccountRequest memory req)
        internal
        view
        returns (bytes memory)
    {
        bytes32 domainSeparator = _buildDomainSeparator();
        bytes32 structHash = keccak256(abi.encode(
            MARGIN_ACCOUNT_TYPEHASH,
            req.from,
            req.marginAccount,
            req.value,
            req.nonce,
            req.deadline,
            req.selector,
            keccak256(req.data)
        ));
        bytes32 digest = keccak256(abi.encodePacked("\x19\x01", domainSeparator, structHash));

        (uint8 v, bytes32 r, bytes32 s) = vm.sign(userPrivateKey, digest);
        return abi.encodePacked(r, s, v);
    }

    function _signPriceDependentRequest(KuruForwarder.PriceDependentRequest memory req)
        internal
        view
        returns (bytes memory)
    {
        bytes32 domainSeparator = _buildDomainSeparator();
        bytes32 structHash = keccak256(abi.encode(
            PRICE_DEPENDENT_TYPEHASH,
            req.from,
            req.market,
            req.price,
            req.value,
            req.nonce,
            req.deadline,
            req.isBelowPrice,
            req.selector,
            keccak256(req.data)
        ));
        bytes32 digest = keccak256(abi.encodePacked("\x19\x01", domainSeparator, structHash));

        (uint8 v, bytes32 r, bytes32 s) = vm.sign(userPrivateKey, digest);
        return abi.encodePacked(r, s, v);
    }

    function _buildDomainSeparator() internal view returns (bytes32) {
        return keccak256(abi.encode(
            DOMAIN_TYPEHASH,
            keccak256("KuruForwarder"),
            keccak256("1.0.0"),
            block.chainid,
            address(kuruForwarder)
        ));
    }
}

// Mock contracts for testing
contract MockTarget {
    receive() external payable {}
    
    function receiveETH() external payable {
        // Simply receive ETH
    }
}

contract MockMarginAccount {
    receive() external payable {}
    
    function receiveETH() external payable {
        // Simply receive ETH
    }
}

contract MockOrderBook {
    uint256 public bestBid;
    uint256 public bestAsk;
    
    function setBestBidAsk(uint256 _bid, uint256 _ask) external {
        bestBid = _bid;
        bestAsk = _ask;
    }
    
    function bestBidAsk() external view returns (uint256, uint256) {
        return (bestBid, bestAsk);
    }
    
    receive() external payable {}
    
    function receiveETH() external payable {
        // Simply receive ETH
    }
}
